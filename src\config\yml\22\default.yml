aPlusContentReport:
  auditRank: 1
  dataPoint: "A+ Content"
  conditions:
    - store:
        contentData: "data.productData[0].AplusContent"
        prospectRevenue: "Math.round(data.productData[0].price * data.productData[0].sales)"
        revenueIncrease: "Math.round(0.12 * prospectRevenue)"
        hasAPlusContent: "contentData.aplusContentPresent"
        hasAltText: "contentData.allImagesHaveAltText"
        productDescriptionLength: "data.productData[0].textDescription.numOfChars"
        hasBrandStory: "contentData.brandStory?.brandStoryPresent"
        hasPremiumAplus: "contentData?.premiumAPlusPresent"

    - type: notPresent
      auditRank: 1
      when: "!hasAPlusContent && prospectRevenue>0"

    - type: notPresentZeroProspectRevenue
      auditRank: 1
      when: "!hasAPlusContent && prospectRevenue==0"

    - type: noAltText
      auditRank: 1
      when: "!hasAltText && hasAPlusContent"

    - type: noPremiumAplus
      auditRank: 1
      when: "hasAPlusContent && !hasPremiumAplus"

    - type: noBrandStory
      auditRank: 1
      when: "!hasBrandStory"

  notPresent:
    priority: "Urgent"
    logic: "Not Present"
    painPoint: "There is no A+ content on the listing right now"
    improvements:
      - "Your competitors are using A+ Content and they are getting an algorithm boost. You should also do that as it can increase sales by up to 12%. That is, meaning an increase of {{revenueIncrease}} per month just for this listing."
      - "Also, include Alt-text in the images."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"

  notPresentZeroProspectRevenue:
    priority: "Urgent"
    logic: "Not Present"
    painPoint: "There is no A+ content on the listing right now"
    improvements:
      - "Your competitors are using A+ Content and they are getting an algorithm boost. You should also do that as it can increase sales by up to 12%."
      - "Also, include Alt-text in the images."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"

  noAltText:
    priority: "High"
    logic: "Alt Text is not there"
    painPoint: "There is no Alt text being used in the images"
    improvements:
      - "You should add alt-text with relevant keywords as that helps with the algorithm and boosts the listing's SEO"
    benefits:
      - "Indexing"
      - "Organic Rankings ↑"

  noPremiumAplus:
    priority: "Medium"
    logic: "A+ content is present but premium A+ content not present"
    painPoint: "Premium A+ content is not being used right now."
    improvements:
      - "Including Premium A+ content can increase your conversion rate, with Amazon stating it can boost sales by 20%. You should take advantage of this free feature."
    benefits:
      - "CVR ↑"
      - "Sales ↑"

  noBrandStory:
    priority: "Medium"
    logic: "Brand story not present"
    painPoint: "There is no Brand Story in the A+ content."
    improvements:
      - "Adding a Brand Story can help customers connect with your brand better and improve conversion rates. You should also add a video there, to further increase the trust in your brand. For the most refined visual experience, mind the technical differences of the Brand Story video vs regular listing video."
    benefits:
      - "CVR ↑"
      - "Brand Loyalty ↑"

storefrontReport:
  auditRank: 1
  dataPoint: "Storefront"
  conditions:
    - type: notPresent
      auditRank: 1
      when: "!storefrontStatus"
    - type: present
      auditRank: 2
      when: "storefrontStatus"

  notPresent:
    priority: "High"
    logic: "Not Present"
    painPoint: "I don't see a storefront present right now."
    improvements:
      - "You should create a storefront, high quality video content can and should be used there to increase customer trust and understanding of the products"
    benefits:
      - "Brand ↑"
      - "CVR ↑"
      - "Algorithm boost"
      - "Visibility ↑"

  present:
    priority: "High"
    logic: "Present"
    painPoint: "Great job! I can see you have a storefront."
    improvements:
      - "Include high-quality video content, which is a great addition to a storefront. Place your best sellers at the top. "
    benefits:
      - "Brand ↑"
      - "CVR ↑"
      - "Algorithm boost"
      - "Visibility ↑"


ratingsReport:
  auditRank: 1
  dataPoint: "Ratings"
  conditions:
    - store:
        lowStarReviewsImprovement: "`You have ${lowStarReviews} of 1* & 2* reviews. Work on increasing the number of good reviews by fixing the listing & product issues.`"

    - type: belowFourWithLowStarReviews
      auditRank: 1
      when: "rating < 4 && lowStarReviews > 0"

    - type: belowFourNoLowStarReviews
      auditRank: 1
      when: "rating < 4 && lowStarReviews == 0"

    - type: belowFourPointThreeWithLowStarReviews
      auditRank: 1
      when: "rating >= 4 && rating < 4.3 && lowStarReviews > 0"

    - type: belowFourPointThreeZeroWithNoLowStarReviews
      auditRank: 1
      when: "rating >= 4 && rating < 4.3 && lowStarReviews == 0"

    - type: belowFourPointFiveWithLowStarReviews
      auditRank: 1
      when: "rating >= 4.3 && rating < 4.5 && lowStarReviews > 0"

    - type: belowFourPointFiveZeroWithNoLowStarReviews
      auditRank: 1
      when: "rating >= 4.3 && rating < 4.5 && lowStarReviews == 0"

    - type: belowFourPointSevenWithLowStarReviews
      auditRank: 1
      when: "rating >= 4.5 && rating < 4.7 && lowStarReviews > 0"

    - type: aboveFourPointSeven
      auditRank: 1
      when: "rating >= 4.7"

  belowFourWithLowStarReviews:
    priority: "High"
    logic: "below 4"
    painPoint: "You have {{rating}} star rating which is low"
    improvements:
      - "On the search page your ratings are shown as {{ratingComesAs}} stars, making it very hard to compete against competition with higher ratings."
      - "Work on increasing the number of good reviews by fixing the listing & product issues."
      - "Ratings often come down to customers understanding the product, its benefits and using the product as intended. Making it easy for customers to understand it, is among the most important tasks for your listing. Well produced product and instructional videos are irreplaceable for this task."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourNoLowStarReviews:
    priority: "High"
    logic: "below 4"
    painPoint: "You have {{rating}} star rating which is low"
    improvements:
      - "On the search page your ratings are shown as {{ratingComesAs}} stars, making it very hard to compete against competition with higher ratings."
      - "Work on increasing the number of good reviews by fixing the listing & product issues"
      - "Ratings often come down to customers understanding the product, its benefits and using the product as intended. Making it easy for customers to understand it, is among the most important tasks for your listing. Well produced product and instructional videos are irreplaceable for this task."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointThreeWithLowStarReviews:
    priority: "High"
    logic: "4 - 4.3"
    painPoint: "You have {{rating}} star rating which is decent but can be improved quickly to 4.5 stars."
    improvements:
      - "On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Once you get that number to 4.3 it will show up 4.5 stars."
      - "Work on increasing the number of good reviews by fixing the listing & product issues."
      - "Ratings often come down to customers understanding the product, its benefits and using the product as intended. Making it easy for customers to understand it, is among the most important tasks for your listing. Well produced product and instructional videos are irreplaceable for this task."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointThreeZeroWithNoLowStarReviews:
    priority: "High"
    logic: "4 - 4.3"
    painPoint: "You have {{rating}} star rating which is decent but can be improved quickly to 4.5 stars"
    improvements:
      - "On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Once you get that number to 4.3 it will show up 4.5 stars."
      - "You should work on increasing the number of good reviews by fixing the listing & product issues."
      - "Ratings often come down to customers understanding the product, its benefits and using the product as intended. Making it easy for customers to understand it, is among the most important tasks for your listing. Well produced product and instructional videos are irreplaceable for this task."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointFiveWithLowStarReviews:
    priority: "High"
    logic: "4.3 - 4.5"
    painPoint: "You have a {{rating}} star rating which is good but this is the threshold to be downgraded to a 4-star rating"
    improvements:
      - "On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Your next aim should be 4.3 stars as it will show up 4.5 stars"
      - "Work on increasing the number of good reviews by fixing the listing & product issues."
      - "Ratings often come down to customers understanding the product, its benefits and using the product as intended. Making it easy for customers to understand it, is among the most important tasks for your listing. Well produced product and instructional videos are irreplaceable for this task."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointFiveZeroWithNoLowStarReviews:
    priority: "High"
    logic: "4.3 - 4.5"
    painPoint: "You have a {{rating}} star rating which is good but this is the threshold to be downgraded to a 4-star rating"
    improvements:
      - "On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Your next aim should be 4.3 stars as it will show up 4.5 stars."
      - "You should work on increasing the number of good reviews by fixing the listing & product issues."
      - "Ratings often come down to customers understanding the product, its benefits and using the product as intended. Making it easy for customers to understand it, is among the most important tasks for your listing. Well produced product and instructional videos are irreplaceable for this task."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointSevenWithLowStarReviews:
    priority: "High"
    logic: "4.5 - 4.7"
    painPoint: "You have {{rating}} star rating which is good but can be improved further to 5 stars, making you #1 your category."
    improvements:
      - "On the search page your ratings are shown as 4.5 stars, making it impossible to rank #1. Once you get that number to 4.7 it will show up 5 stars."
      - "{{lowStarReviewsImprovement}}"
      - "Ratings often come down to customers understanding the product, it’s benefits and using the product as intended. Making it easy for customers to understand it, is among the most important tasks for your listing. Well produced product and instructional videos are irreplaceable for this task."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointSevenZero:
    priority: "High"
    logic: "4.5 - 4.7"
    painPoint: "You have {{rating}} star rating which is good but can be improved further to 5 stars, making you #1 your category."
    improvements:
      - "On the search page your ratings are shown as 4.5 stars, making it impossible to rank #1. Once you get that number to 4.7 it will show up 5 stars."
      - "{{lowStarReviewsImprovement}}"
      - "Ratings often come down to customers understanding the product, it’s benefits and using the product as intended. Making it easy for customers to understand it, is among the most important tasks for your listing. Well produced product and instructional videos are irreplaceable for this task."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  aboveFourPointSeven:
    priority: "High"
    logic: "4.7+"
    painPoint: "Great job! Your ratings are perfect just make sure they don't fall below 4.7 stars."
    improvements:
      - "If they fall below 4.7 stars your ratings will show up as 4.5 stars on the Amazon search page, greatly affecting the CVR & CTR."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

reviewReport:
  auditRank: 1
  dataPoint: "Reviews"
  conditions:
    - store:
        nextIdealNumber: "calculateNextIdealNumber(reviewCount)"
        lowStarReviewsText: "lowStarReviews > 0 ? `${lowStarReviews}, 1 & 2 star reviews` : `some bad reviews`"

    - type: lessThan30
      auditRank: 1
      when: "reviewCount < 30"

    - type: between30And70
      auditRank: 1
      when: "reviewCount >= 30 && reviewCount < 70"

    - type: moreThan70
      auditRank: 1
      when: "reviewCount >= 70"

  lessThan30:
    priority: "High"
    logic: "<30"
    painPoint: "You have {{reviewCount}} reviews which is significantly affecting your conversions."
    improvements:
      - "70+ reviews are ideal for scaling but the first you should aim for 30."
      - "Bad reviews often come down to customers using the product wrong without knowing it. An instructional video is a great way to fix that. It also helps with managing customers’ expectations, which can also be a reason for bad reviews."
      - "The best ways to get reviews is enroll in the vine program & insert a review request card in the packaging."
      - "You can see upto 4% review rate per order with the right review funnel"
    benefits:
      - "CVR ↑"
      - "CTR ↑"

  between30And70:
    priority: "Medium"
    logic: "30-70"
    painPoint: "You have {{reviewCount}} reviews which is enough fuel to rev your engine but not enough to hit full speed."
    improvements:
      - "Bad reviews often come down to customers using the product wrong without knowing it. An instructional video is a great way to fix that. It also helps with managing customers’ expectations, which can also be a reason for bad reviews."
      - "The best ways to get reviews is enroll in the vine program & insert a review request card in the packaging."
      - "You can start scaling but 70+ reviews are ideal before you run ads."
      - "You can see up to 4% review rate per order with the right review funnel"
    benefits:
      - "CVR ↑"
      - "CTR ↑"

  moreThan70:
    priority: "Low"
    logic: "70+"
    painPoint: "Great Job! I see you have {{reviewCount}} reviews but I also see {{lowStarReviewsText}}."
    improvements:
      - "Bad reviews often come down to customers using the product wrong without knowing it. An instructional video is a great way to fix that. It also helps with managing customers’ expectations, which can also be a reason for bad reviews."

    benefits:
      - "CVR ↑"
      - "CTR ↑"

adsReport:
  auditRank: 1
  dataPoint: "Ads"
  conditions:
    - store:
        improvementMessage: "prospectRevenue >= 20 && prospectRevenue <= 80 ? 'Amazon ads perform best for products that are $20 - $80 (fitting your range).' : 'Our expertise is in running ads for expensive products which are $100+.'"

    - type: notPresent
      auditRank: 1
      when: "!adsStatus"

  notPresent:
    priority: "Urgent"
    logic: "Not Present"
    painPoint: "Was searching your company's name on Amazon from multiple US pincodes but didn't see any ads from you guys."
    improvements:
      - "You should instantly start running ads, they are the best way you can scale on Amazon."
      - "{{improvementMessage}}"
      - "At your stage of the business, I think you should start with Sponsored product ads."
      - "Make sure campaigns are structured properly."
    benefits:
      - "Profit ↑"
      - "Visibility & SEO ↑"

brandReport:
  auditRank: 1
  dataPoint: "Branding"
  conditions:
    - type: haveVideo
      auditRank: 1
      when: "videosCount > 0"


  haveVideo:
    priority: "High"
    logic: "Video present"
    painPoint: "Your listing is missing a powerful video, limiting your brand's potential to stand out and connect with viewers"
    improvements:
      - "A well crafted product video that also highlights your brand will set you apart from most of the competition. Improving your branding this way offers multiple benefits allowing you to command higher prices increasing your bottom line, improve your conversions, build trust in your brand and the product."
    benefits:
      - "Brand ↑"
      - "Algorithm boost"

videoReport:
  auditRank: 1
  dataPoint: "Videos"
  conditions:
    - store:
        revenueIncrease: "Math.ceil(prospectRevenue * 0.097)"

    - type: noVideoWithRevenue
      auditRank: 1
      when: "videosCount === 0 && prospectRevenue>0"

    - type: noVideoWithoutRevenue
      auditRank: 1
      when: "videosCount === 0 && prospectRevenue==0"

  noVideoWithRevenue:
    priority: "Urgent"
    logic: "Video not present"
    painPoint: "I don't see any product video on the listing"
    improvements:
      - "You should instantly make a great product video showing the benefits & addressing pain points. Amazon states an increase of up to 9.7% in sales, which is ${{revenueIncrease}} per month just for this listing alone. Be sure to understand though, that it does matter, how the video is produced. A better video has a higher chance to bring the expected gains."
    benefits:
      - "Brand ↑"
      - "CVR ↑"
      - "Algorithm boost"

  noVideoWithoutRevenue:
    priority: "Urgent"
    logic: "Video not present"
    painPoint: "I don't see any product video on the listing"
    improvements:
      - "You should instantly make a great product video showing benefits & addressing pain points. You can see up to a 9.7% lift in sales."
    benefits:
      - "Brand ↑"
      - "CVR ↑"
      - "Algorithm boost"

imageReport:
  auditRank: 1
  dataPoint: "Images"
  conditions:
    - store:
        hasVideo: "videosCount > 0"
        insufficientImages: "(0 < imagesCount && imagesCount < 6) || (imagesCount === 5 && hasVideo)"

    - type: insufficientImages
      auditRank: 1
      when: "insufficientImages"

    - type: noImages
      auditRank: 1
      when: "imagesCount == 0"

  insufficientImages:
    priority: "Urgent"
    logic: "< 6 images"
    painPoint: "There {{(imagesCount === 1)? 'is': 'are'}} only {{imagesCount}} {{(imagesCount === 1)? 'image': 'images'}} on your listing right now."
    improvements:
      - "You should have at least 6 images including different angles, lifestyle images & infographics."
      - "This helps the consumers understand your product better and leads to higher conversions."
    benefits:
      - "CVR ↑"

  noImages:
    priority: "Urgent"
    logic: "No images"
    painPoint: "There are no Images on your listing right now."
    improvements:
      - "You should have at least 6 images including different angles, lifestyle images & infographics."
      - "This helps the consumers understand your product better and leads to higher conversions."
    benefits:
      - "CVR ↑"
