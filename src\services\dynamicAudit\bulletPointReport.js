const { parseYamlReport } = require("../../utils/yamlParser");

function updateBulletPointReport(data, report, clientId,ymlPath) {
  if (!data.productData?.[0]?.bulletPoints) {
    return;
  }

  const bulletPoints = data.productData[0].bulletPoints;
  // console.log("BulletPoint Data: ", bulletPoints.Points);

  const store = {
    bulletPoints,
    getFormattedText: (text) => {
      let words = text.split(" ").slice(0, 5);
      for (let i = 0; i < words.length; i++) {
        let word = words[i];
        for (let j = 0; j < word.length; j++) {
          if (/^[a-zA-Z]$/.test(word.charAt(j))) {
            words[i] =
              word.slice(0, j) +
              word.charAt(j).toUpperCase() +
              word.slice(j + 1).toLowerCase();
            break;
          }
        }
      }
      return words.join(" ") + " ...";
    },
  };
  // console.log("BulletPoint Store: ", {store});

  const bulletPointReports = parseYamlReport("bulletPointReport", store,ymlPath);
  report.push(...bulletPointReports);
}

module.exports = updateBulletPointReport;
