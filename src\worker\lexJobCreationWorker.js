const cron = require("node-cron");
const { PrismaClient } = require("@prisma/client");
const axios = require("axios");

const prisma = new PrismaClient();

class LexJobCreationWorker {
  constructor() {
    this.BASE_URL = process.env.LEX_SCRAPER_BASE_URL || "http://localhost:3001";
    this.MAX_ASINS_PER_BATCH = 3;
    this.isRunning = false;
  }
  async getProgressAsins() {
    const pendingAsinsFromTable = await prisma.lexASIN.findMany({
      where: {
        status: "REVIEW_IN_PROGRESS",
      },
      select: {
        asin: true,
        countryCode: true,
      },
      take: 10, // Limit to prevent overwhelming the system
    });
    return pendingAsinsFromTable;
  }
  async start() {
    console.log("🚀 Starting Lex Job Creation Worker...");

    // Run every 3 minutes
    cron.schedule("*/2 * * * *", async () => {
      if (this.isRunning) {
        console.log("⏳ Job creation worker already running, skipping...");
        return;
      }

      try {
        this.isRunning = true;
        await this.processJobs();
      } catch (error) {
        console.error("❌ Error in job creation worker:", error);
      } finally {
        this.isRunning = false;
      }
    });

    console.log("✅ Lex Job Creation Worker scheduled to run every 2 minutes");
  }

  async processJobs() {
    console.log("🔄 Processing job creation...");

    try {
      const existingJobsInQueue = await this.getProgressAsins();
      if (existingJobsInQueue.length > 0) {
        console.log("⏳ Still waiting for existing review jobs to complete...");
        return;
      }

      const { pendingAsins, countryCode } = await this.getPendingAsins();

      if (!pendingAsins || pendingAsins?.length === 0) {
        console.log("📭 No pending ASINs found");
        return;
      }

      // Get active cookies
      const { cookies, cookie_id } = await this.getActiveCookies(countryCode);

      if (cookies?.length === 0) {
        console.log("🍪 No active cookies found");
        return;
      }

      const batches = this.createBatches(
        pendingAsins,
        this.MAX_ASINS_PER_BATCH
      );

      const batch = batches[0];
      
      // Update each ASIN individually using the compound unique key
      for (const asin of batch) {
        try {
          await prisma.lexASIN.update({
            where: {
              asin_countryCode: {
                asin: asin,
                countryCode: countryCode
              }
            },
            data: {
              status: "REVIEW_IN_PROGRESS",
              updatedAt: new Date(),
            },
          });
        } catch (updateError) {
          console.error(`❌ Failed to update ASIN ${asin} (${countryCode}):`, updateError.message);
        }
      }

      await this.createJobForBatch(batch, cookies, countryCode, cookie_id);

      // Small delay between batches
      await this.delay(1000);
    } catch (error) {
      console.error("❌ Error processing jobs:", error);
    }
  }

  async getPendingAsins() {
    try {
      // Get ASINs from jobs that are PENDING

      const pendingAsinsFromTable = await prisma.lexASIN.findMany({
        where: {
          status: "REVIEW_PENDING",
        },
        select: {
          asin: true,
          countryCode: true,
        },
        take: 20, // Limit to prevent overwhelming the system
      });

      // Group ASINs by country code to handle duplicates properly
      const asinsByCountry = new Map();
      for (const asinObj of pendingAsinsFromTable) {
        const countryCode = asinObj.countryCode || "US";
        const key = `${countryCode}`;
        if (!asinsByCountry.has(key)) {
          asinsByCountry.set(key, [
            {
              asin: asinObj.asin,
              countryCode: countryCode,
            },
          ]);
        } else {
          asinsByCountry.get(key).push({
            asin: asinObj.asin,
            countryCode: countryCode,
          });
        }
      }
      const asinEntries = Array.from(asinsByCountry.keys());
      const randomEntry =
        asinEntries[Math.floor(Math.random() * (asinEntries.length - 1))];
      // console.log(
      //   pendingAsinsFromTable,
      //   {
      //     pendingAsins: asinsByCountry.get(randomEntry),
      //     countryCode: randomEntry,
      //   },
      //   asinsByCountry,
      //   asinEntries,
      //   randomEntry
      // );
      return {
        pendingAsins: asinsByCountry
          .get(randomEntry)
          ?.map((asinObj) => asinObj.asin),

        countryCode: randomEntry,
      };
    } catch (error) {
      console.error("❌ Error getting pending ASINs:", error);
      return [];
    }
  }

  async getActiveCookies(countryCode = "US") {
    try {
      // First try to get cookies for the specific country
      let cookies = await prisma.lexReviewScraperCookies.findMany({
        where: {
          active: true,
          cookieStatus: "ACTIVE",
          countryCode: countryCode,
        },
        take: 1, // Use first available cookie
      });

      if (cookies.length > 0) {
        console.log(
          `🍪 Found ${cookies.length} active cookie(s) for country ${countryCode}`
        );
        return {
          cookies: JSON.parse(cookies[0].cookieKey),
          cookie_id: cookies[0].id,
        };
      }

      console.log(`🍪 No active cookies found for country ${countryCode}`);
      return {
        cookies: [],
        cookie_id: -1,
      };
    } catch (error) {
      console.error("❌ Error getting active cookies:", error);
      return {
        cookies: [],
        cookie_id: -1,
      };
    }
  }

  createBatches(asins, batchSize) {
    const batches = [];
    for (let i = 0; i < asins.length; i += batchSize) {
      batches.push(asins.slice(i, i + batchSize));
    }
    return batches;
  }

  async createJobForBatch(asins, cookies, countryCode = "US", cookieId) {
    try {
      const payload = {
        asins: asins,
        cookieKey: cookies,
        countryCode,
        cookieId,
      };
      // console.log(payload);
      console.log(`📤 Creating job for ASINs: ${asins.join(", ")}`);

      const response = await axios.post(
        `${this.BASE_URL}/api/create_job`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 30000, // 30 second timeout
        }
      );

      if (response.data && response.data.success) {
        console.log(
          `✅ Job created successfully for ASINs: ${asins.join(", ")}`
        );

        // Update job status to IN_PROGRESS
        // await this.updateJobStatus(asins, "IN_PROGRESS");
      } else {
        // console.log(
        //   `⚠️ Job creation response: ${JSON.stringify(response.data)}`
        // );
      }
    } catch (error) {
      console.error(
        `❌ Error creating job for ASINs ${asins.join(", ")}:`,
        error.message
      );

      // Update job status to FAILED
      // await this.updateJobStatus(asins, "FAILED", error.message);
    }
  }

  async updateJobStatus(asins, status, errorMessage = null) {
    try {
      for (const asin of asins) {
        await prisma.lexJob.updateMany({
          where: {
            asin: asin,
            status: "PENDING",
          },
          data: {
            status: status,
            errorMessage: errorMessage,
          },
        });
      }
    } catch (error) {
      console.error("❌ Error updating job status:", error);
    }
  }

  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async stop() {
    console.log("🛑 Stopping Lex Job Creation Worker...");
    // Graceful shutdown logic here
  }
}

// Initialize and start worker if this file is run directly
if (require.main === module) {
  const worker = new LexJobCreationWorker();
  worker.start();

  // Graceful shutdown
  process.on("SIGINT", async () => {
    await worker.stop();
    await prisma.$disconnect();
    process.exit(0);
  });
}

module.exports = LexJobCreationWorker;
