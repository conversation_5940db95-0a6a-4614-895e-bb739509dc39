/**
 * Simple test script to verify Azure OpenAI connection
 * Run this to check if your Azure OpenAI credentials and setup are working
 */

const { testAzureOpenAI } = require("../src/services/spike/runPromptChain");

async function runBasicTest() {
  console.log("🚀 Starting Azure OpenAI Connection Test\n");
  console.log("=".repeat(50));
  
  try {
    // Test 1: Basic connection test
    console.log("\n📋 Test 1: Basic Connection Test");
    const result1 = await testAzureOpenAI();
    
    if (result1.success) {
      console.log("✅ Basic test passed!");
    } else {
      console.log("❌ Basic test failed!");
      console.log("Error details:", result1);
      return;
    }
    
    // Test 2: Custom message test
    console.log("\n📋 Test 2: Custom Message Test");
    const result2 = await testAzureOpenAI("Can you help me analyze product reviews?");
    
    if (result2.success) {
      console.log("✅ Custom message test passed!");
    } else {
      console.log("❌ Custom message test failed!");
      console.log("Error details:", result2);
      return;
    }
    
    // Test 3: Performance test
    console.log("\n📋 Test 3: Performance Test");
    const startTime = Date.now();
    const result3 = await testAzureOpenAI("What is 2+2?");
    const totalTime = Date.now() - startTime;
    
    if (result3.success) {
      console.log("✅ Performance test passed!");
      console.log(`⏱️ Total response time: ${totalTime}ms`);
    } else {
      console.log("❌ Performance test failed!");
      console.log("Error details:", result3);
      return;
    }
    
    // Summary
    console.log("\n" + "=".repeat(50));
    console.log("🎉 All Azure OpenAI tests passed successfully!");
    console.log("\n📊 Summary:");
    console.log(`- Model: ${result1.model}`);
    console.log(`- Average response time: ${((parseInt(result1.duration) + parseInt(result2.duration) + parseInt(result3.duration)) / 3).toFixed(0)}ms`);
    console.log(`- Total tokens used: ${(result1.tokenUsage.total_tokens || 0) + (result2.tokenUsage.total_tokens || 0) + (result3.tokenUsage.total_tokens || 0)}`);
    
    console.log("\n✅ Your Azure OpenAI setup is working correctly!");
    console.log("🔄 You can now proceed with the prompt chain functionality.");
    
  } catch (error) {
    console.error("\n💥 Unexpected error during testing:", error.message);
    console.error("Stack trace:", error.stack);
  }
}

async function runQuickTest() {
  console.log("⚡ Quick Azure OpenAI Test\n");
  
  const result = await testAzureOpenAI("Hello!");
  
  if (result.success) {
    console.log("✅ Azure OpenAI is working!");
    console.log(`Response: "${result.response}"`);
    console.log(`Duration: ${result.duration}`);
  } else {
    console.log("❌ Azure OpenAI connection failed!");
    console.log("Error:", result.error);
    
    // Provide troubleshooting tips
    console.log("\n🔧 Troubleshooting tips:");
    console.log("1. Check your .env file has the correct Azure OpenAI credentials");
    console.log("2. Verify AZURE_OPENAI_API_KEY is set");
    console.log("3. Verify AZURE_OPENAI_ENDPOINT is correct");
    console.log("4. Verify AZURE_OPENAI_API_VERSION is correct");
    console.log("5. Make sure your Azure OpenAI deployment is active");
  }
  
  return result;
}

// Export functions for use in other files
module.exports = {
  runBasicTest,
  runQuickTest
};

// If this file is run directly, execute the basic test
if (require.main === module) {
  runBasicTest().catch(console.error);
}
