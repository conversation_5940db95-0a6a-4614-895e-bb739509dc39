bulletPointReport:
  auditRank: 1
  noOfReviews: 2
  dataPoint: "Bullet Points"
  conditions:
    - store:
        bulletPointCounts: "bulletPoints.Points.length || 0"
        totalCharacters: "bulletPoints.TotalChars"
        allCapsCheck: "bulletPoints.isItAllCaps"
        isLongBulletPoint: "(point, index) => point.NumberChars > 270 ? index + 1 : null"
        isShortBulletPoint: "(point, index) => point.NumberChars < 130 ? index + 1 : null"
        longPoints: "bulletPoints.Points.map(isLongBulletPoint).filter(i=>i)"
        shortPoints: "bulletPoints.Points.map(isShortBulletPoint).filter(i=>i)"
        formattedExample: "getFormattedText(bulletPoints.Points.find(point => point.FirstWordCapital)?.value || '')"
        allShortPoints: "shortPoints.length === bulletPoints.Points.length"
        allLongPoints: "longPoints.length === bulletPoints.Points.length"
        indexedLengthPoints: "bulletPoints.Points.map((point, index) => ({index: index + 1, length: point.NumberChars})).sort((a, b) => a.length - b.length)"
        maxBulletPointIndex: "indexedLengthPoints.at(-1)?.index"
        minBulletPointIndex: "indexedLengthPoints?.[0]?.index"
        minBulletPointChars: "indexedLengthPoints?.[0]?.length"
        maxBulletPointChars: "indexedLengthPoints.at(-1)?.length"
        joinedLongPoints: "longPoints.join(', ')"
        joinedShortPoints: "shortPoints.join(', ')"

    - type: lessThanFiveBulletPoints
      auditRank: 1
      when: "bulletPointCounts > 0 && bulletPointCounts < 5"

    - type: lessThanThousandCharacters
      auditRank: 1
      when: "bulletPointCounts > 0 && totalCharacters < 900"

    - type: longBulletPoints
      auditRank: 1
      when: "longPoints.length > 0 && longPoints.length > shortPoints.length && !allLongPoints"

    - type: allLongBulletPoints
      auditRank: 1
      when: "bulletPointCounts > 0 && allLongPoints"

    - type: shortBulletPoints
      auditRank: 1
      when: "shortPoints.length > 0 && longPoints.length < shortPoints.length && !allShortPoints"
      store:
        joinedShortPoints: "shortPoints.join(', ')"

    - type: allShortBulletPoints
      auditRank: 1
      when: "shortPoints.length > 0 && allShortPoints"

    - type: allCaps
      auditRank: 1
      when: "bulletPointCounts > 0 && allCapsCheck"

    - type: noBulletPoints
      auditRank: 1
      when: "bulletPointCounts <= 0"

  lessThanFiveBulletPoints:
    priority: "Medium"
    logic: "< 5 Bullet Points"
    painPoint: "You have {{bulletPointCounts}} bullet {{(bulletPointCounts === 1) ? `point` : `points`}} instead of 5."
    improvements:
      - "You're not utilizing the full SEO potential of bullet points. Add at least 5 bullet points to do so."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  lessThanThousandCharacters:
    priority: "Medium"
    logic: "<1000 characters total"
    painPoint: "The total character length for bullet points is {{totalCharacters}} which is less than it should be."
    improvements:
      - "Add more information about the product & its features in the bullet points. Have at least 1000 characters in total to maximize SEO."
    benefits:
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  longBulletPoints:
    priority: "Medium"
    logic: "Bullet points have more than 250 chars"
    painPoint: "{{joinedLongPoints}} {{(longPoints.length > 1) ? 'have' : 'has'}} more than 250 characters."
    improvements:
      - "You should decrease the length of each bullet point. The Ideal length is 150-250 characters. Bullet Point {{maxBulletPointIndex}} has {{maxBulletPointChars}} characters."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  shortBulletPoints:
    priority: "Medium"
    logic: "Bullet points have less than 150 Chars"
    painPoint: "{{joinedShortPoints}} {{(shortPoints.length > 1) ? 'have' : 'has'}} less than 150 characters."
    improvements:
      - "You should increase the length of each bullet point. The ideal length is 150-250 characters. Bullet Point {{minBulletPointIndex}} has {{minBulletPointChars}} characters."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  allCaps:
    priority: "Medium"
    logic: "The bullet points are in all caps"
    painPoint: "Your bullet points are in all capital letters"
    improvements:
      - "You should change them to lowercase except the first letter of each bullet point like - {{formattedExample}}"
    benefits:
      - "CVR ↑"

  noBulletPoints:
    priority: "Medium"
    logic: "No Bullet Points"
    painPoint: "You have no bullet points."
    improvements:
      - "You need to utilize the full SEO potential of bullet points. Add at least 5 bullet points to do so."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  allLongBulletPoints:
    logic: "Bullet points have more than 250 chars"
    painPoint: "All bullet points have more than 250 characters."
    improvements:
      - "You should decrease the length of each bullet point. The Ideal length is 150-250 characters."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  allShortBulletPoints:
    logic: "Bullet points have less than 150 Chars"
    painPoint: "All bullet points have less than 150 characters."
    improvements:
      - "You should increase the length of each bullet point. The Ideal length is 150-250 characters. They are all less than 150 characters."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

productTitleReport:
  auditRank: 1
  dataPoint: "Product Title"
  conditions:
    - store:
        guidelines: "titleLength > 100 ? titleLengthGuidelines['Default'] : titleLengthGuidelines[category]"

    - type: shortTitleWithoutGuidelines
      auditRank: 1
      when: "!guidelines && titleLength <= 150"

    - type: averageTitleWithoutGuidelines
      auditRank: 1
      when: "!guidelines && titleLength > 150 && titleLength <= 180"

    - type: badTitle
      auditRank: 1
      when: "guidelines && titleLength <= guidelines.bad[1]"

    - type: averageTitleLessThan
      auditRank: 1
      when: "guidelines && titleLength > guidelines.bad[1] && titleLength <= guidelines.average[1]"

  shortTitleWithoutGuidelines:
    priority: "High"
    logic: "0-150"
    painPoint: "Your title character length is {{titleLength}} which is causing you to miss a lot of traffic opportunities."
    improvements:
      - "Fully squeeze the title character length for that SEO juice."
      - "Add lots of relevant & highly searched keywords."
      - "Move the main keyword to the beginning."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"
      - "Indexing"

  averageTitleWithoutGuidelines:
    priority: "Medium"
    logic: "150-180"
    painPoint: "Your title character length is {{titleLength}} which is causing you to miss some traffic opportunities."
    improvements:
      - "Fully squeeze the title character length for that SEO juice by adding relevant & high search volume keywords."
      - "Move the main keywords within the first 80 characters."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  averageTitleLessThan:
    priority: "Medium"
    logic: "{{guidelines ? JSON.stringify(guidelines.average):'150-180'}}"
    painPoint: "Your title character length is {{titleLength}} which is causing you to miss some traffic opportunities."
    improvements:
      - "Fully squeeze the title character length for that SEO juice."
      - "Add lots of relevant & highly searched keywords."
      - "Move the main keyword to the beginning."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  badTitle:
    priority: "High"
    logic: "{{guidelines ? JSON.stringify(guidelines.bad) : '0-150'}}"
    painPoint: "Your title character length is {{titleLength}} which is causing you to miss a lot of traffic opportunities."
    improvements:
      - "Fully squeeze the title character length for that SEO juice by adding relevant & high search volume keywords."
      - "Move the main keywords within the first 80 characters."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"
      - "Indexing"

aPlusContentReport:
  auditRank: 1
  dataPoint: "A+ Content"
  conditions:
    - store:
        contentData: "data.productData[0].AplusContent"
        prospectRevenue: "Math.round(data.productData[0].price * data.productData[0].sales)"
        revenueIncrease: "Math.round(0.12 * prospectRevenue)"
        hasAPlusContent: "contentData.aplusContentPresent"
        hasAltText: "contentData.allImagesHaveAltText"
        productDescriptionLength: "data.productData[0].textDescription.numOfChars"
        hasBrandStory: "contentData.brandStory?.brandStoryPresent"
        hasPremiumAplus: "contentData?.premiumAPlusPresent"


    - type: notPresent
      auditRank: 1
      when: "!hasAPlusContent && prospectRevenue>0"

    - type: notPresentZeroProspectRevenue
      auditRank: 1
      when: "!hasAPlusContent && prospectRevenue==0"

    - type: shortDescriptionNonZero
      auditRank: 1
      when: "0 < productDescriptionLength && productDescriptionLength < 500"

    - type: shortDescriptionZero
      auditRank: 1
      when: "0 === productDescriptionLength"

    - type: noAltText
      auditRank: 1
      when: "!hasAltText && hasAPlusContent"

    - type: noPremiumAplus
      auditRank: 1
      when: "hasAPlusContent && !hasPremiumAplus"

    - type: noBrandStory
      auditRank: 1
      when: "!hasBrandStory"

  notPresent:
    priority: "Urgent"
    logic: "Not Present"
    painPoint: "There is no A+ content on the listing right now"
    improvements:
      - "Your competitors are using A+ Content and they are getting an algorithm boost. You should also do that as it can increase sales by up to 12%. That is , meaning an increase of {{revenueIncrease}} per month just for this listing."
      - "Also, include Alt-text in the images."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"

  notPresentZeroProspectRevenue:
    priority: "Urgent"
    logic: "Not Present"
    painPoint: "There is no A+ content on the listing right now"
    improvements:
      - "Your competitors are using A+ Content and they are getting an algorithm boost. You should also do that as it can increase sales by up to 12%."
      - "Also, include Alt-text in the images."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      
  shortDescriptionNonZero:
    priority: "Medium"
    logic: "<500+ characters"
    painPoint: "Your text description has {{productDescriptionLength}} characters, this is lesser than it should be."
    improvements:
      - "You should add more information about your product in the text description section and increase it to at least 500+ characters."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"

  shortDescriptionZero:
    priority: "Medium"
    logic: "<500+ characters"
    painPoint: "There is no text description at the end of your A+ content."
    improvements:
      - "You should add information about your product in the text description section."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"

  noAltText:
    priority: "High"
    logic: "Alt Text is not there"
    painPoint: "There is no Alt text being used in the images"
    improvements:
      - "You should add alt-text with relevant keywords as that helps with the algorithm and boosts the listing's SEO"
    benefits:
      - "Indexing"
      - "Organic Rankings ↑"

  noPremiumAplus:
    priority: "Medium"
    logic: "A+ content is present but premium A+ content not present"
    painPoint: "Premium A+ content is not being used right now."
    improvements:
      - "Including Premium A+ content can increase your conversion rate, with Amazon stating it can boost sales by 20%. You should take advantage of this free feature."
    benefits:
      - "CVR ↑"
      - "Sales ↑"

  noBrandStory:
    priority: "Medium"
    logic: "Brand story not present"
    painPoint: "There is no Brand Story in the A+ content."
    improvements:
      - "Adding a Brand Story can help customers connect with your brand better and improve conversion rates."
    benefits:
      - "CVR ↑"
      - "Brand Loyalty ↑"

storefrontReport:
  auditRank: 1
  dataPoint: "Storefront"
  conditions:
    - type: notPresent
      auditRank: 1
      when: "!storefrontStatus"

  notPresent:
    priority: "High"
    logic: "Not Present"
    painPoint: "I don't see a storefront present right now."
    improvements:
      - "You should make a storefront instantly. Include your best sellers at the top."
      - "Get a designer to make it well and according to your brand aesthetic."
      - "Include the “follow tag” so that customers get notified about new launches."
    benefits:
      - "CVR ↑"
      - "Algorithm boost"
      - "Visibility ↑"

ratingsReport:
  auditRank: 1
  dataPoint: "Ratings"
  conditions:
    - type: belowFourWithLowStarReviews
      auditRank: 1
      when: "rating < 4 && lowStarReviews > 0"

    - type: belowFourNoLowStarReviews
      auditRank: 1
      when: "rating < 4 && lowStarReviews == 0"

    - type: belowFourPointThreeWithLowStarReviews
      auditRank: 1
      when: "rating >= 4 && rating < 4.3 && lowStarReviews > 0"

    - type: belowFourPointThreeZeroWithNoLowStarReviews
      auditRank: 1
      when: "rating >= 4 && rating < 4.3 && lowStarReviews == 0"

    - type: belowFourPointFiveWithLowStarReviews
      auditRank: 1
      when: "rating >= 4.3 && rating < 4.5 && lowStarReviews > 0"

    - type: belowFourPointFiveZeroWithNoLowStarReviews
      auditRank: 1
      when: "rating >= 4.3 && rating < 4.5 && lowStarReviews == 0"

    - type: belowFourPointSevenWithLowStarReviews
      auditRank: 1
      when: "rating >= 4.5 && rating < 4.7 && lowStarReviews > 0"

    - type: aboveFourPointSeven
      auditRank: 1
      when: "rating >= 4.7"

  belowFourWithLowStarReviews:
    priority: "High"
    logic: "below 4"
    painPoint: "You have {{rating}} star rating which is low"
    improvements:
      - "On the search page your ratings are shown as {{ratingComesAs}} stars, making it very hard to compete against competition with higher ratings."
      - "You have {{lowStarReviews}}, 1* & 2* reviews, you should get as many of them removed as possible."
      - "Then work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourNoLowStarReviews:
    priority: "High"
    logic: "below 4"
    painPoint: "You have {{rating}} star rating which is low"
    improvements:
      - "On the search page your ratings are shown as {{ratingComesAs}} stars, making it very hard to compete against competition with higher ratings."
      - "Work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointThreeWithLowStarReviews:
    priority: "High"
    logic: "4 - 4.3"
    painPoint: "You have {{rating}} star rating which is decent but can be improved quickly to 4.5 stars."
    improvements:
      - "On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Once you get that number to 4.3 it will show up 4.5 stars."
      - "You have {{lowStarReviews}}, 1* & 2* reviews, you should get as many of them removed as possible."
      - "Work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointThreeZeroWithNoLowStarReviews:
    priority: "High"
    logic: "4 - 4.3"
    painPoint: "You have {{rating}} star rating which is decent but can be improved quickly to 4.5 stars"
    improvements:
      - "On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Once you get that number to 4.3 it will show up 4.5 stars."
      - "You should work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointFiveWithLowStarReviews:
    priority: "High"
    logic: "4.3 - 4.5"
    painPoint: "You have a {{rating}} star rating which is good but this is the threshold to be downgraded to a 4-star rating"
    improvements:
      - "On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Your next aim should be 4.3 stars as it will show up 4.5 stars."
      - "To get this up, you should get as many 1* & 2* star reviews removed as possible, I see currently you have {{lowStarReviews}}."
      - "Work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointFiveZeroWithNoLowStarReviews:
    priority: "High"
    logic: "4.3 - 4.5"
    painPoint: "You have a {{rating}} star rating which is good but this is the threshold to be downgraded to a 4-star rating"
    improvements:
      - "On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Your next aim should be 4.3 stars as it will show up 4.5 stars."
      - "You should work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointSevenWithLowStarReviews:
    priority: "High"
    logic: "4.5 - 4.7"
    painPoint: "You have {{rating}} star rating which is good but can be improved further to 5 stars, making you #1 your category."
    improvements:
      - "On the search page your ratings are shown as 4.5 stars, making it impossible to rank #1. Once you get that number to 4.7 it will show up 5 stars."
      - "You have {{lowStarReviews}}, 1* & 2* reviews, you should get as many of them removed as possible."
      - "Work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointSevenZero:
    priority: "High"
    logic: "4.5 - 4.7"
    painPoint: "You have {{rating}} star rating which is good but can be improved further to 5 stars, making you #1 your category."
    improvements:
      - "On the search page your ratings are shown as 4.5 stars, making it impossible to rank #1. Once you get that number to 4.7 it will show up 5 stars."
      - "You should work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  aboveFourPointSeven:
    priority: "High"
    logic: "4.7+"
    painPoint: "Great job! Your ratings are perfect just make sure they don't fall below 4.7 stars."
    improvements:
      - "If they fall below 4.7 stars your ratings will show up as 4.5 stars on the Amazon search page, greatly affecting the CVR & CTR."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

reviewReport:
  auditRank: 1
  dataPoint: "Reviews"
  conditions:
    - store:
        nextIdealNumber: "calculateNextIdealNumber(reviewCount)"
        lowStarReviewsText: "lowStarReviews > 0 ? `${lowStarReviews}, 1 & 2 star reviews` : `some bad reviews`"

    - type: lessThan30
      auditRank: 1
      when: "reviewCount < 30"

    - type: between30And70
      auditRank: 1
      when: "reviewCount >= 30 && reviewCount < 70"

    - type: moreThan70
      auditRank: 1
      when: "reviewCount >= 70"

  lessThan30:
    priority: "High"
    logic: "<30"
    painPoint: "You have {{reviewCount}} reviews which is significantly affecting your conversions."
    improvements:
      - "70+ reviews are ideal for scaling but the first you should aim for 30."
      - "The best ways to get reviews is enroll in the vine program & insert a review request card in the packaging."
      - "You can see upto 4% review rate per order with the right review funnel"
    benefits:
      - "CVR ↑"
      - "CTR ↑"

  between30And70:
    priority: "Medium"
    logic: "30-70"
    painPoint: "You have {{reviewCount}} reviews which is enough fuel to rev your engine but not enough to hit full speed."
    improvements:
      - "The best ways to get reviews is enroll in the vine program & insert a review request card in the packaging."
      - "You can start scaling but 70+ reviews are ideal before you run ads."
      - "You can see up to 4% review rate per order with the right review funnel"
    benefits:
      - "CVR ↑"
      - "CTR ↑"

  moreThan70:
    priority: "Low"
    logic: "70+"
    painPoint: "Great Job! I see you have {{reviewCount}} reviews but I also see {{lowStarReviewsText}}."
    improvements:
      - "You should identify Non TOS compliant reviews & get them removed. Next, aim for {{nextIdealNumber}}. We usually see a huge jump at that point."
      - "1 & 2 star reviews can completely kill the potential of a listing."
    benefits:
      - "CVR ↑"
      - "CTR ↑"

adsReport:
  auditRank: 1
  dataPoint: "Ads"
  conditions:
    - store:
        improvementMessage: "prospectRevenue >= 20 && prospectRevenue <= 80 ? 'Amazon ads perform best for products that are $20 - $80 (fitting your range).' : 'Our expertise is in running ads for expensive products which are $100+.'"

    - type: notPresent
      auditRank: 1
      when: "!adsStatus"

  notPresent:
    priority: "Urgent"
    logic: "Not Present"
    painPoint: "Was searching your company's name on Amazon from multiple US pincodes but didn't see any ads from you guys."
    improvements:
      - "You should instantly start running ads, they are the best way you can scale on Amazon."
      - "{{improvementMessage}}"
      - "At your stage of the business, I think you should start with Sponsored product ads."
      - "Make sure campaigns are structured properly."
    benefits:
      - "Profit ↑"
      - "Visibility & SEO ↑"

videoReport:
  auditRank: 1
  dataPoint: "Videos"
  conditions:
    - store:
        revenueIncrease: "Math.ceil(prospectRevenue * 0.097)"

    - type: noVideoWithRevenue
      auditRank: 1
      when: "videosCount === 0 && prospectRevenue>0"

    - type: noVideoWithoutRevenue
      auditRank: 1
      when: "videosCount === 0 && prospectRevenue==0"

  noVideoWithRevenue:
    priority: "Urgent"
    logic: "Video not present"
    painPoint: "I don't see any product video on the listing"
    improvements:
      - "You should instantly make a great product video showing benefits & addressing pain points. You can see up to a 9.7% lift in sales. Which is ${{revenueIncrease}} per month just for this listing alone."
      - "Keep it below 40 seconds & you can start by making it in Canva to keep costs low."
    benefits:
      - "CVR ↑"
      - "Algorithm boost"

  noVideoWithoutRevenue:
    priority: "Urgent"
    logic: "Video not present"
    painPoint: "I don't see any product video on the listing"
    improvements:
      - "You should instantly make a great product video showing benefits & addressing pain points. You can see up to a 9.7% lift in sales."
      - "Keep it below 40 seconds & you can start by making it in Canva to keep costs low."
    benefits:
      - "CVR ↑"
      - "Algorithm boost"

imageReport:
  auditRank: 1
  dataPoint: "Images"
  conditions:
    - store:
        hasVideo: "videosCount > 0"
        insufficientImages: "(0 < imagesCount && imagesCount < 6) || (imagesCount === 5 && hasVideo)"

    - type: insufficientImages
      auditRank: 1
      when: "insufficientImages"

    - type: noImages
      auditRank: 1
      when: "imagesCount == 0"

  insufficientImages:
    priority: "Urgent"
    logic: "< 6 images"
    painPoint: "There {{(imagesCount === 1)? 'is': 'are'}} only {{imagesCount}} {{(imagesCount === 1)? 'image': 'images'}} on your listing right now."
    improvements:
      - "You should have at least 6 images including different angles, lifestyle images & infographics."
      - "This helps the consumers understand your product better and leads to higher conversions."
    benefits:
      - "CVR ↑"

  noImages:
    priority: "Urgent"
    logic: "No images"
    painPoint: "There are no Images on your listing right now."
    improvements:
      - "You should have at least 6 images including different angles, lifestyle images & infographics."
      - "This helps the consumers understand your product better and leads to higher conversions."
    benefits:
      - "CVR ↑"
