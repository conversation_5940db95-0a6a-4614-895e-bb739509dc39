const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const chromium = require("@sparticuz/chromium");
const getProxyRoyalPass = require("../multicountry/getProxyPassRoyal");
const path = require("path");

require("dotenv").config();
puppeteer.use(StealthPlugin());

let proxyBrowserInstance = null;
let normalBrowserInstance = null;

// Determine the environment: isLocal is true if BROWSER_ENV is "test"
const isLocal = process.env.BROWSER_ENV === "test";

/**
 * Creates a new browser instance based on the type and environment.
 * @param {string} type - "proxy" or "normal"
 * @param {boolean} isLocal - Whether running in local environment
 * @returns {Promise<Browser>} The Puppeteer browser instance
 */
async function createInstance(type, isLocal) {
  const CACHE_DIR = path.resolve(__dirname, "puppeteer-cache");
  const args = [
    `--disk-cache-dir=${CACHE_DIR}`,
    "--no-sandbox",
    "--disable-setuid-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    ...(type === "proxy" ? ["--proxy-server=geo.iproyal.com:12321"] : []),
    ...(!isLocal ? chromium.args.filter((arg) => typeof arg === "string") : []),
  ];

  return puppeteer.launch({
    args,
    defaultViewport: !isLocal ? chromium.defaultViewport : null,
    executablePath: !isLocal ? await chromium.executablePath() : undefined,
    headless: !isLocal ? chromium.headless : true,
  });
}

/**
 * Returns a Puppeteer browser instance based on the environment and type.
 * Creates a new instance if none exists.
 * @param {string} type - "proxy" for proxy-enabled browser, "normal" for default browser
 * @returns {Promise<Browser>} The Puppeteer browser instance
 */
async function getBrowser(type = "normal") {
  try {
    if (type === "proxy") {
      if (!proxyBrowserInstance) {
        console.log(
          `Launching a new proxy-enabled browser instance (${
            isLocal ? "local" : "prod"
          })...`
        );
        proxyBrowserInstance = await createInstance("proxy", isLocal);
      } else {
        console.log("using existing browser...");
        return proxyBrowserInstance;
      }
    } else if (type === "normal") {
      if (!normalBrowserInstance) {
        console.log(
          `Launching a new normal browser instance (${
            isLocal ? "local" : "prod"
          })...`
        );
        normalBrowserInstance = await createInstance("normal", isLocal);
        return normalBrowserInstance;
      } else {
        console.log("using existing browser...");
        return normalBrowserInstance;
      }
    } else {
      throw new Error(`Invalid browser type: ${type}`);
    }
  } catch (error) {
    console.log("Error creating browser:", error);
  }
}

/**
 * Closes all open pages in a Puppeteer browser instance.
 * @param {Browser} browserInstance - The Puppeteer browser instance
 */
async function closePages(browserInstance) {
  if (browserInstance) {
    const pages = await browserInstance.pages();
    for (const page of pages) {
      try {
        if (!page.isClosed()) {
          await page.close();
        }
      } catch (err) {
        console.error("Error closing page:", err);
      }
    }
    console.log("All pages closed.");
  }
}

/**
 * Closes the Puppeteer browser instances if they exist.
 */
async function closeBrowsers() {
  try {
    console.log("Closing the browser instance...");
    if (normalBrowserInstance) {
      await closePages(normalBrowserInstance);
      await normalBrowserInstance.close();
      normalBrowserInstance = null;
      console.log("Closed Normal Browser Instance...");
    }
    if (proxyBrowserInstance) {
      await closePages(proxyBrowserInstance);
      await proxyBrowserInstance.close();
      proxyBrowserInstance = null;
      console.log("Closed Proxy Browser Instance...");
    }
    console.log("****************************************");
  } catch (error) {
    console.log("ERROR closing broswer:", error);
  }
}

module.exports = { getBrowser, closeBrowsers, closePages };
