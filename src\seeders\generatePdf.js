const prisma = require("../database/prisma/getPrismaClient");
const { generateAuditPdf } = require("../utils/getAmazonAudit");

async function main() {
  // Get all outputData
    const outputData = await prisma.amazonAuditReport.findMany({
        where: {
          pdfUrl:"",
      }
  });
  // Loop through all the outputData
  for (const data of outputData) {
    // Update the slug of each data
    await generateAuditPdf(data.slug);
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
