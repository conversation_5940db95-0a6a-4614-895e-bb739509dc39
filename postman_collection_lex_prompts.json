{"info": {"name": "LEX Prompts & Violation Detection API", "description": "Unified API collection for LEX prompt chain management, execution, and violation detection.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "prompt<PERSON><PERSON><PERSON><PERSON>d", "value": "1", "type": "string"}, {"key": "reviewId", "value": "1", "type": "string"}, {"key": "jobId", "value": "1", "type": "string"}], "item": [{"name": "Prompt Chain Management", "item": [{"name": "Get All Prompt Chains", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts?active_only=false&limit=50", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts"], "query": [{"key": "active_only", "value": "false"}, {"key": "limit", "value": "50"}]}}}, {"name": "Get Specific Prompt Chain", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/{{promptChainId}}", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "{{promptChainId}}"]}}}, {"name": "Create/Update Client Prompt Chain", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Client Analysis Chain\",\n  \"description\": \"A prompt chain for client reviews.\",\n  \"prompt1\": \"Analyze the following review content and provide insights: {{reviewContent}}\",\n  \"prompt2\": \"Based on the previous analysis, identify key themes: {{reviewContent}}\",\n  \"prompt3\": \"Detect any policy violations in this review: {{reviewContent}}\",\n  \"model\": \"azure-gpt4o\",\n  \"isActive\": true,\n  \"isPrimary\": false,\n  \"isClient\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts"]}, "description": "Creates new or updates existing prompt chain for isClient=true. Uses upsert logic."}}, {"name": "Create/Update Non-Client Prompt Chain", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Standard Analysis Chain\",\n  \"description\": \"A prompt chain for standard reviews.\",\n  \"prompt1\": \"Analyze the following review content for general insights: {{reviewContent}}\",\n  \"prompt2\": \"Summarize key points from this review: {{reviewContent}}\",\n  \"prompt3\": \"Check for violations in this review: {{reviewContent}}\",\n  \"model\": \"azure-gpt4o\",\n  \"isActive\": true,\n  \"isPrimary\": false,\n  \"isClient\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts"]}, "description": "Creates new or updates existing prompt chain for isClient=false. Uses upsert logic."}}, {"name": "Update Prompt Chain by ID", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Analysis Chain\",\n  \"prompt1\": \"Updated first prompt: {{reviewContent}}\",\n  \"prompt2\": \"Updated second prompt: {{reviewContent}}\",\n  \"prompt3\": \"Updated third prompt: {{reviewContent}}\",\n  \"isClient\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/{{promptChainId}}", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "{{promptChainId}}"]}}}, {"name": "Update isClient Flag Only", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"isClient\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/{{promptChainId}}", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "{{promptChainId}}"]}, "description": "Update only the isClient flag to switch between client and non-client prompt behavior"}}, {"name": "Delete Prompt Chain", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/{{promptChainId}}", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "{{promptChainId}}"]}}}]}, {"name": "Prompt Chain Execution", "item": [{"name": "Execute Integrated Chain - Single Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"promptChainId\": {{promptChainId}}\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/run-integrated", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "run-integrated"]}, "description": "Execute the full prompt chain (prompt1, prompt2, prompt3) on a single review using specific prompt chain ID."}}, {"name": "Execute Integrated Chain - Multiple Reviews", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [1, 2, 3],\n  \"promptChainId\": {{promptChainId}}\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/run-integrated", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "run-integrated"]}, "description": "Execute the full prompt chain (prompt1, prompt2, prompt3) on multiple reviews using specific prompt chain ID."}}, {"name": "Execute Client Prompt Auto-Select", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"isClient\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/run-integrated", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "run-integrated"]}, "description": "Execute prompt chain automatically selecting the client prompt chain (isClient=true). Runs prompt1, prompt2, prompt3."}}, {"name": "Execute Non-Client Prompt Auto-Select", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"isClient\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/run-integrated", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "run-integrated"]}, "description": "Execute prompt chain automatically selecting the non-client prompt chain (isClient=false). Runs prompt1, prompt2, prompt3."}}, {"name": "Execute Chain with Model Override", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"isClient\": true,\n  \"model\": \"gemini-1.5-flash\"\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/run-integrated", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "run-integrated"]}, "description": "Execute prompt chain with a different model than the default configured in the prompt chain."}}]}, {"name": "Violation Detection (Manual Trigger)", "item": [{"name": "Run Violation Detection - Single Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"model\": \"azure-gpt4o\"\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/run-violation", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "run-violation"]}, "description": "Manually trigger violation detection (prompt3 only) for a single review. Requires the review to have been processed by a prompt chain previously."}}, {"name": "Run Violation Detection - Multiple Reviews (Gemini)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [1, 2, 3, 4, 5],\n  \"model\": \"gemini-1.5-flash\"\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/run-violation", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "run-violation"]}, "description": "Manually trigger violation detection (prompt3 only) for multiple reviews. Requires reviews to have been processed by a prompt chain previously."}}]}, {"name": "Legacy Execution Endpoints", "item": [{"name": "Execute Specific Chain (prompts 1 & 2 only)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"model\": \"azure-gpt4o\"\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/{{promptChainId}}/execute", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "{{promptChainId}}", "execute"]}, "description": "Execute only prompt1 and prompt2 for a specific prompt chain."}}, {"name": "Execute Primary Chain (prompts 1 & 2 only)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"model\": \"azure-gpt4o\"\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/execute", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "execute"]}, "description": "Execute only prompt1 and prompt2 using the primary prompt chain."}}]}, {"name": "Configuration & Utilities", "item": [{"name": "Get Available AI Models", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/models", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "models"]}}}, {"name": "Get Available Variables", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/variables", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "variables"]}}}, {"name": "Get Job Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/jobs/{{jobId}}", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "jobs", "{{jobId}}"]}}}]}, {"name": "CSV Upload & Bulk Processing", "item": [{"name": "Upload CSV for Bulk Review Processing", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "csvFile", "type": "file", "src": "/path/to/your/reviews.csv"}, {"key": "model", "value": "azure-gpt4o", "type": "text", "description": "Optional: Override default model for processing"}]}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/upload-csv", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "upload-csv"]}, "description": "Upload a CSV file with review data. CSV should have columns like reviewID, reviewContent, isClient, etc. Automatically processes reviews with appropriate prompt chains based on isClient flag."}}, {"name": "Get All Bulk Jobs", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/bulk-jobs?status=COMPLETED&limit=20&offset=0", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "bulk-jobs"], "query": [{"key": "status", "value": "COMPLETED", "description": "Optional: Filter by job status (PENDING, IN_PROGRESS, COMPLETED, FAILED)"}, {"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}, "description": "Get all bulk review processing jobs with their status and summary information."}}, {"name": "Download Bulk Job Results CSV", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/bulk-jobs/{{jobId}}/download", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "bulk-jobs", "{{jobId}}", "download"]}, "description": "Download processed results as CSV file. Includes original input data plus AI analysis outputs (prompt1Output, prompt2Output, prompt3Output, violation data, etc.)"}}]}]}