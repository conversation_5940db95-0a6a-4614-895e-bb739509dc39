# Enhanced Prompt Chain System with Lang<PERSON><PERSON> Tracing

This directory contains the enhanced prompt chain system that uses Azure OpenAI with LangSmith tracing for better observability and debugging.

## 🚀 Key Features

- **Azure OpenAI Integration**: Uses existing Azure OpenAI credentials
- **<PERSON><PERSON>mith Tracing**: Full observability with `traceable` and `wrapOpenAI`
- **File-based YAML Prompts**: Organized prompt management
- **Custom Variable Substitution**: Support for multiple variables beyond `{{input}}`
- **Conditional Execution**: Skip prompts based on logic
- **Data Extraction**: Extract and use data between prompt steps
- **Cost Tracking**: Built-in token usage and cost calculation

## 📁 Directory Structure

```
src/prompts/
├── lex/                    # Lex analysis prompts
│   ├── contentAnalysis.yaml
│   ├── complianceCheck.yaml
│   └── violationAnalysis.yaml
├── example/                # Example prompts
│   └── brandAnalysis.yaml
└── README.md              # This file
```

## 🔧 Setup

### 1. Environment Variables
Ensure these are set in your `.env` file:
```env
AZURE_OPENAI_API_KEY=your_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=your_deployment_name
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_MODEL_ID=gpt-4
```

### 2. LangSmith Configuration
Set up LangSmith environment variables:
```env
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your_langsmith_api_key
LANGCHAIN_PROJECT=your_project_name
```

## 📖 Usage Examples

### Basic Prompt Chain
```javascript
const { runPromptChain } = require("../services/spike/runPromptChain");

const result = await runPromptChain(
  "lex/contentAnalysis.yaml",
  {
    productTitle: "Product Name",
    reviewTitle: "Review Title", 
    reviewContent: "Review content..."
  },
  { debug: true, temperature: 0.3 }
);
```

### Lex Analysis (Drop-in Replacement)
```javascript
const { runLexAnalysisChain } = require("../services/spike/runPromptChain");

const result = await runLexAnalysisChain(reviewObject, {
  debug: true,
  temperature: 0.3
});
```

### Custom YAML Prompt
```javascript
const customPrompt = `
- role: system
  content: "You are a helpful assistant."
  
- role: user
  content: |
    Analyze: {{productTitle}}
    Price: {{price}}
    Category: {{category}}
`;

const result = await runPromptChain(customPrompt, {
  productTitle: "Wireless Headphones",
  price: "$79.99", 
  category: "Electronics"
});
```

## 🎯 YAML Prompt Format

```yaml
# Step 1: System message
- role: system
  content: "You are an expert assistant."

# Step 1: User message  
- role: user
  content: |
    Your prompt here with variables:
    Product: {{productTitle}}
    Review: {{reviewContent}}
    
# Step 2: System message (optional)
- role: system
  content: "Process the previous response."
  
# Step 2: User message
- role: user
  content: "{{input}}"  # Uses output from previous step
```

## 🔍 Variable Substitution

- `{{variableName}}` - Custom variables from input object
- `{{input}}` - Output from previous step (for chaining)
- `{{previousOutput}}` - Alias for `{{input}}`

## ⚙️ Configuration Options

```javascript
const options = {
  debug: true,              // Enable detailed logging
  temperature: 0.7,         // Model temperature
  maxTokens: 1000,          // Max tokens per request
  conditionalExecution: fn, // Function to skip steps
  dataExtractor: fn         // Function to extract data between steps
};
```

## 📊 LangSmith Tracing

Each prompt step is automatically traced with:
- Step name: `prompt_chain_step_1`, `prompt_chain_step_2`, etc.
- Input messages and variables
- Output content and token usage
- Execution time and costs

View traces at: https://smith.langchain.com/

## 🧪 Testing

Run the test suite:
```javascript
const { runAllTests } = require("../../examples/testLangSmithIntegration");
await runAllTests();
```

## 🔄 Migration from LangChain

The system now uses Azure OpenAI directly instead of LangChain:

**Before (LangChain):**
```javascript
const chatModel = new AzureChatOpenAI({...});
const response = await chatModel.invoke([systemMsg, userMsg]);
```

**After (Azure OpenAI + LangSmith):**
```javascript
const client = wrapOpenAI(new AzureOpenAI({...}));
const response = await traceable(async () => {
  return await client.chat.completions.create({...});
})();
```

## 📈 Benefits

1. **Better Observability**: Full request/response tracing
2. **Cost Optimization**: Detailed token usage tracking
3. **Easier Debugging**: Step-by-step execution logs
4. **Prompt Management**: Organized YAML files
5. **Backward Compatibility**: Drop-in replacements for existing functions
