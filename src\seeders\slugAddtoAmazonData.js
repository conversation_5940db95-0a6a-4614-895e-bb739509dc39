const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

async function main() {
  // Fetch all AmazonProductData and AmazonAuditReport records
  const amazonProductDataList = await prisma.amazonProductData.findMany();
  const amazonAuditReports = await prisma.amazonAuditReport.findMany();

  // Create a map of audit reports with companyId as the key for quick lookup
  const auditReportMap = amazonAuditReports.reduce((map, report) => {
    map[report.companyId] = report;
    return map;
  }, {});

  // Iterate through AmazonProductData and update slugs
  for (const productData of amazonProductDataList) {
    const report = auditReportMap[productData.companyId];
    if (report && report.slug) {
      await prisma.amazonProductData.update({
        where: { id: productData.id },
        data: { slug: report.slug },
      });
      console.log(`Updated slug for AmazonProductData ID ${productData.id}`);
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
