{"info": {"name": "LEX Violation Detection API", "description": "Complete API collection for LEX violation detection system with multi-model support", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "reviewId", "value": "1", "type": "string"}, {"key": "clientId", "value": "1", "type": "string"}], "item": [{"name": "Run Violation Detection - Single Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"model\": \"azure-gpt4o\",\n  \"isClient\": true,\n  \"clientId\": {{clientId}}\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/run", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "run"]}, "description": "Run violation detection on a single review using Azure OpenAI for a client"}}, {"name": "Run Violation Detection - Multiple Reviews", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [1, 2, 3, 4, 5],\n  \"model\": \"gemini-2.5-flash\",\n  \"isClient\": false,\n  \"clientId\": {{clientId}}\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/run", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "run"]}, "description": "Run violation detection on multiple reviews using Gemini for non-client"}}, {"name": "Run Violation Detection - DeepSeek Model", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [10, 11, 12],\n  \"model\": \"deepseek\",\n  \"isClient\": true,\n  \"clientId\": {{clientId}}\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/run", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "run"]}, "description": "Run violation detection using Azure DeepSeek for advanced reasoning"}}, {"name": "Get Review Violation Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/reviews/{{reviewId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "reviews", "{{reviewId}}", "status"]}, "description": "Get detailed violation detection status for a specific review"}}, {"name": "Get Bulk Review Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/reviews/bulk-status", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "reviews", "bulk-status"]}, "description": "Get violation detection status for multiple reviews at once"}}, {"name": "Update Review Configuration", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"isClient\": true,\n  \"autoPromptEnabled\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/reviews/{{reviewId}}/config", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "reviews", "{{reviewId}}", "config"]}, "description": "Update client status and auto-prompt settings for a specific review"}}, {"name": "Bulk Update Review Configuration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewIds\": [1, 2, 3, 4, 5],\n  \"isClient\": false,\n  \"autoPromptEnabled\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/reviews/bulk-config", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "reviews", "bulk-config"]}, "description": "Update configuration for multiple reviews at once"}}, {"name": "Pause Auto Prompts Globally", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Maintenance window - pausing auto prompts for system updates\"\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/config/pause-auto-prompts", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "config", "pause-auto-prompts"]}, "description": "Pause automatic prompt execution globally for all reviews"}}, {"name": "Resume Auto Prompts Globally", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Maintenance completed - resuming auto prompt execution\"\n}"}, "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/config/resume-auto-prompts", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "config", "resume-auto-prompts"]}, "description": "Resume automatic prompt execution globally for all reviews"}}, {"name": "Get Auto Prompt Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/config/auto-prompt-status", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "config", "auto-prompt-status"]}, "description": "Get global status of auto prompt execution system"}}, {"name": "Export Reviews CSV - All Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/reviews/export", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "reviews", "export"]}, "description": "Export all reviews with violation detection data as CSV"}}, {"name": "Export Reviews CSV - Client Only", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/reviews/export?isClient=true&limit=1000", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "reviews", "export"], "query": [{"key": "isClient", "value": "true", "description": "Filter for client reviews only"}, {"key": "limit", "value": "1000", "description": "Limit results to 1000 reviews"}]}, "description": "Export client reviews only with violation data"}}, {"name": "Export Reviews CSV - Violations Only", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/reviews/export?hasViolation=true&startDate=2024-01-01&endDate=2024-12-31", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "reviews", "export"], "query": [{"key": "hasViolation", "value": "true", "description": "Filter for reviews with violations only"}, {"key": "startDate", "value": "2024-01-01", "description": "Start date filter"}, {"key": "endDate", "value": "2024-12-31", "description": "End date filter"}]}, "description": "Export only reviews that have violations detected"}}, {"name": "Export Reviews CSV - By Model", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/reviews/export?model=azure-gpt4o&status=VIOLATION_DETECTED", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "reviews", "export"], "query": [{"key": "model", "value": "azure-gpt4o", "description": "Filter by detection model used"}, {"key": "status", "value": "VIOLATION_DETECTED", "description": "Filter by review status"}]}, "description": "Export reviews processed by specific model"}}, {"name": "Get Violation Detection Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/stats", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "stats"]}, "description": "Get comprehensive statistics about violation detection"}}, {"name": "Get Statistics - Client vs Non-Client", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lex_prompts/violation-detection/stats?isClient=true&startDate=2024-01-01", "host": ["{{baseUrl}}"], "path": ["api", "lex_prompts", "violation-detection", "stats"], "query": [{"key": "isClient", "value": "true", "description": "Filter statistics for client reviews"}, {"key": "startDate", "value": "2024-01-01", "description": "Start date for statistics"}]}, "description": "Get statistics filtered by client status and date range"}}, {"name": "Get Available AI Models", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/ai/models", "host": ["{{baseUrl}}"], "path": ["api", "ai", "models"]}, "description": "Get comprehensive list of available AI models: azure-gpt4o, gemini-2.5-pro, gemini-2.5-flash, deepseek"}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set dynamic values", "pm.globals.set('timestamp', Date.now());", "", "// Log request details for debugging", "console.log('Making request to:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Response status code is successful', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "// Log response for debugging", "console.log('Response:', pm.response.json());"]}}]}