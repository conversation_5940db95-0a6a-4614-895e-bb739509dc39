# Lex Review Content Analysis Prompt
# This prompt analyzes Amazon review content to understand the product category, 
# claims, opinions, and reviewer intent

- role: system
  content: "You are an Amazon review analysis assistant."

- role: user
  content: |
    You are an Amazon review expert. You are given review title, review content and product title/name as on Amazon.

    INPUTS:
    - Product Title: {{productTitle}}
    - Review Title: {{reviewTitle}}
    - Review Content: {{reviewContent}}

    Based on this: 
    1. Carefully analyze the product category and type based on the title
    2. Determine what constitutes relevant and expected content for reviews of this product type
    3. Identify the main claims, opinions, or experiences described in the review
    4. Consider multiple possible interpretations of ambiguous statements
    5. Evaluate whether the review primarily discusses the product itself or other factors
    6. Assess the reviewer's apparent intent and tone.

    Give an output with the above answers in short but without missing a detail for further processing.
