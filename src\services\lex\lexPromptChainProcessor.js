/**
 * Lex Review Analysis using Enhanced Prompt Chains
 * This module provides a wrapper around the enhanced runPromptChain functionality
 * specifically designed for lex review analysis workflows.
 */

const { runLexAnalysisChain } = require("../spike/runPromptChain");

/**
 * Analyze Amazon reviews using the new prompt chain system
 * This is a drop-in replacement for the original analyzeAmazonReviewsWithCompliance function
 * @param {Array} reviewsArray - Array of review objects
 * @param {Object} options - Configuration options
 * @returns {Promise<Array>} Analysis results
 */
async function analyzeAmazonReviewsWithCompliance(reviewsArray, options = {}) {
  const analyses = [];

  const config = {
    debug: options.debug || false,
    temperature: options.temperature || 0.7,
    ...options,
  };

  for (const [index, review] of reviewsArray.entries()) {
    try {
      if (config.debug) {
        console.log(
          `🔄 Processing review ${index + 1}/${reviewsArray.length}: ${
            review["Review ID"] || review.reviewID
          }`
        );
      }

      // Run the lex analysis chain
      const result = await runLexAnalysisChain(review, config);

      // Format result to match original function output
      const formattedResult = {
        analysis: result.analysis,
        topViolations: {
          GuidelineViolation1: result.topViolations.guidelineViolation1,
          ConfidenceScore1: result.topViolations.confidenceScore1,
          Reason1: result.topViolations.reason1,
          GuidelineViolation2: result.topViolations.guidelineViolation2,
          ConfidenceScore2: result.topViolations.confidenceScore2,
          Reason2: result.topViolations.reason2,
          GuidelineViolation3: result.topViolations.guidelineViolation3,
          ConfidenceScore3: result.topViolations.confidenceScore3,
          Reason3: result.topViolations.reason3,
        },
        violation: result.violation,
        cost: result.cost,
        tokenUsage: result.tokenUsage,
      };

      analyses.push(formattedResult);

      if (config.debug) {
        console.log(
          `✅ Completed review ${index + 1}. Cost: $${(
            result.cost || 0
          ).toFixed(4)}`
        );
      }
    } catch (error) {
      console.error(`❌ Error analyzing review ${index + 1}:`, error.message);

      // Add error result to maintain array consistency
      analyses.push({
        analysis: null,
        topViolations: {
          GuidelineViolation1: null,
          ConfidenceScore1: 0,
          Reason1: null,
          GuidelineViolation2: null,
          ConfidenceScore2: 0,
          Reason2: null,
          GuidelineViolation3: null,
          ConfidenceScore3: 0,
          Reason3: null,
        },
        violation: null,
        cost: 0,
        tokenUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
        error: error.message,
      });
    }
  }

  return analyses;
}

/**
 * Analyze a single review using prompt chains
 * @param {Object} review - Single review object
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Analysis result
 */
async function analyzeSingleReview(review, options = {}) {
  try {
    const result = await runLexAnalysisChain(review, options);
    return result;
  } catch (error) {
    console.error("❌ Error analyzing single review:", error);
    throw error;
  }
}

module.exports = {
  analyzeAmazonReviewsWithCompliance,
  analyzeSingleReview,
  runLexAnalysisChain,
};
