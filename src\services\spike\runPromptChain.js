const { AzureOpenAI } = require("openai");
const { traceable } = require("langsmith/traceable");
const { wrapOpenAI } = require("langsmith/wrappers");
const yaml = require("js-yaml");
const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");
const calculatePricing = require("../../utils/calculatePrice");

dotenv.config();

/**
 * Initialize Azure OpenAI client with LangSmith tracing
 */
console.log({
  apiKey: process.env.AZURE_OPENAI_API_KEY,
  endpoint: process.env.AZURE_OPENAI_ENDPOINT,
  apiVersion: process.env.AZURE_OPENAI_API_VERSION,
});
const baseClient = new AzureOpenAI({
  apiKey: process.env.AZURE_OPENAI_API_KEY,
  endpoint: process.env.AZURE_OPENAI_ENDPOINT,
  apiVersion: process.env.AZURE_OPENAI_API_VERSION,
});

// Wrap the client with LangSmith for tracing
const client = wrapOpenAI(baseClient);

/**
 * Simple test function to verify Azure OpenAI connection
 * @param {string} testMessage - Optional test message
 * @returns {Promise<Object>} Test result
 */
async function testAzureOpenAI(testMessage = "Hello, are you working?") {
  try {
    console.log("🧪 Testing Azure OpenAI connection...");

    const response = await baseClient.chat.completions.create({
      messages: [
        {
          role: "user",
          content: testMessage,
        },
      ],
      temperature: 0.7,
      max_tokens: 50,
    });

    const result = {
      success: true,
      response: response.choices?.[0]?.message?.content || "No response",
      usage: response.usage,
      model: response.model,
    };

    console.log("✅ Azure OpenAI working!");
    console.log("Response:", result.response);
    console.log("Usage:", result.usage);

    return result;
  } catch (error) {
    console.error("❌ Azure OpenAI failed:", error.message);

    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
}

/**
 * Enhanced prompt chain runner with support for:
 * - File-based YAML prompts
 * - Custom variable substitution
 * - Conditional prompt execution
 * - Data extraction between steps
 * - Multiple input variables
 * - LangSmith tracing
 */

/**
 * Load prompt chain from YAML file
 * @param {string} promptPath - Path to YAML file relative to src/prompts/
 * @returns {Array} Parsed YAML steps
 */
function loadPromptFromFile(promptPath) {
  const fullPath = path.join(__dirname, "../../prompts", promptPath);
  const yamlContent = fs.readFileSync(fullPath, "utf8");
  return yaml.load(yamlContent);
}

/**
 * Replace variables in content with provided values
 * @param {string} content - Content with {{variable}} placeholders
 * @param {Object} variables - Key-value pairs for substitution
 * @returns {string} Content with variables replaced
 */
function replaceVariables(content, variables) {
  let result = content;

  // Replace all {{variableName}} with corresponding values
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{${key}}}`, "g");
    result = result.replace(regex, value || "");
  }

  // Also support legacy {{input}} for backward compatibility
  if (variables.input) {
    result = result.replace(/{{input}}/g, variables.input);
  }

  return result;
}

/**
 * Run prompt chain with enhanced features
 * @param {string|Array} promptSource - YAML string, file path, or pre-loaded steps
 * @param {Object|string} inputData - Input data (object for multiple variables, string for simple input)
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Result with output, cost, and token usage
 */
async function runPromptChain(promptSource, inputData, options = {}) {
  // Handle different input types
  let steps;
  if (typeof promptSource === "string" && promptSource.includes(".yaml")) {
    // Load from file
    steps = loadPromptFromFile(promptSource);
  } else if (typeof promptSource === "string") {
    // Parse YAML string
    steps = yaml.load(promptSource);
  } else if (Array.isArray(promptSource)) {
    // Use pre-loaded steps
    steps = promptSource;
  } else {
    throw new Error(
      "Invalid prompt source. Must be YAML string, file path, or array of steps."
    );
  }

  // Normalize input data
  let variables;
  if (typeof inputData === "string") {
    variables = { input: inputData };
  } else if (typeof inputData === "object") {
    variables = { ...inputData };
  } else {
    throw new Error("Input data must be string or object.");
  }

  // Configuration options
  const config = {
    temperature: options.temperature || 0.7,
    maxTokens: options.maxTokens || 1000,
    conditionalExecution: options.conditionalExecution || false,
    dataExtractor: options.dataExtractor || null,
    debug: options.debug || false,
    ...options,
  };

  let totalPromptTokens = 0;
  let totalCompletionTokens = 0;
  let stepResults = [];
  let currentOutput = variables.input || "";

  for (let i = 0; i < steps.length; i += 2) {
    const systemMsg = steps[i];
    const userMsg = steps[i + 1];
    const stepIndex = Math.floor(i / 2) + 1;

    if (config.debug) {
      console.log(`🔄 Executing step ${stepIndex}...`);
    }

    // Check conditional execution
    if (
      config.conditionalExecution &&
      typeof config.conditionalExecution === "function"
    ) {
      const shouldExecute = config.conditionalExecution(
        stepIndex,
        stepResults,
        variables
      );
      if (!shouldExecute) {
        if (config.debug) {
          console.log(`⏭️ Skipping step ${stepIndex} due to conditional logic`);
        }
        continue;
      }
    }

    // Update variables with current output for chaining
    const currentVariables = {
      ...variables,
      input: currentOutput,
      previousOutput: currentOutput,
    };

    // Prepare messages for Azure OpenAI
    const messages = [
      {
        role: "system",
        content: replaceVariables(systemMsg.content, currentVariables),
      },
      {
        role: "user",
        content: replaceVariables(userMsg.content, currentVariables),
      },
    ];

    // Call Azure OpenAI with LangSmith tracing
    const response = await traceable(
      async (messages) => {
        return await client.chat.completions.create({
          messages: messages,
          model: process.env.AZURE_OPENAI_DEPLOYMENT,
          temperature: config.temperature,
          max_tokens: config.maxTokens,
        });
      },
      { name: `prompt_chain_step_${stepIndex}` }
    )(messages);

    const tokenUsage = response?.usage || {
      prompt_tokens: 0,
      completion_tokens: 0,
    };

    totalPromptTokens += tokenUsage.prompt_tokens;
    totalCompletionTokens += tokenUsage.completion_tokens;

    currentOutput = response.choices?.[0]?.message?.content || "";

    // Store step result
    const stepResult = {
      step: stepIndex,
      output: currentOutput,
      tokenUsage: {
        promptTokens: tokenUsage.prompt_tokens,
        completionTokens: tokenUsage.completion_tokens,
        totalTokens: tokenUsage.prompt_tokens + tokenUsage.completion_tokens,
      },
    };

    // Apply data extractor if provided
    if (config.dataExtractor && typeof config.dataExtractor === "function") {
      stepResult.extractedData = config.dataExtractor(currentOutput, stepIndex);

      // Update variables with extracted data
      if (stepResult.extractedData) {
        Object.assign(variables, stepResult.extractedData);
      }
    }

    stepResults.push(stepResult);

    if (config.debug) {
      console.log(
        `✅ Step ${stepIndex} completed. Output length: ${currentOutput.length}`
      );
    }
  }

  // Calculate cost with fallback for unknown models
  const modelName = process.env.AZURE_OPENAI_DEPLOYMENT || "gpt-4";
  const pricingResult = calculatePricing(
    totalPromptTokens,
    totalCompletionTokens,
    modelName
  );
  const cost = pricingResult.cost !== undefined ? pricingResult.cost : 0;

  return {
    output: currentOutput,
    cost,
    tokenUsage: {
      promptTokens: totalPromptTokens,
      completionTokens: totalCompletionTokens,
      totalTokens: totalPromptTokens + totalCompletionTokens,
    },
    stepResults: stepResults,
    finalVariables: variables,
  };
}

/**
 * Specialized function for lex review analysis using prompt chains
 * @param {Object} review - Review object with productTitle, reviewTitle, reviewContent
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Analysis results with all three prompt outputs
 */
async function runLexAnalysisChain(review, options = {}) {
  const config = {
    debug: options.debug || false,
    temperature: options.temperature || 0.7,
    ...options,
  };

  // Data extractor for lex analysis - extracts top violations from compliance output
  const lexDataExtractor = (output, stepIndex) => {
    if (stepIndex === 2) {
      // Compliance check step
      return extractTopViolationsFromOutput(output);
    }
    return null;
  };

  const inputData = {
    productTitle: review.productTitle || "",
    reviewTitle: review["Review Title"] || review.reviewTitle || "",
    reviewContent: review.reviewContent || "",
  };

  try {
    // Step 1: Content Analysis
    const contentResult = await runPromptChain(
      "lex/contentAnalysis.yaml",
      inputData,
      { ...config, debug: config.debug }
    );

    // Step 2: Compliance Check
    const complianceInputData = {
      ...inputData,
      reviewAnalysis: contentResult.output,
    };

    const complianceResult = await runPromptChain(
      "lex/complianceCheck.yaml",
      complianceInputData,
      {
        ...config,
        dataExtractor: lexDataExtractor,
        debug: config.debug,
      }
    );

    // Extract top violations
    const topViolations = extractTopViolationsFromOutput(
      complianceResult.output
    );

    // Step 3: Violation Analysis (conditional)
    let violationResult = null;
    if (topViolations.confidenceScore1 >= 2) {
      const violationInputData = {
        ...inputData,
        reviewAnalysis: contentResult.output,
        guidelineViolation: topViolations.guidelineViolation1,
        reason: topViolations.reason1,
      };

      violationResult = await runPromptChain(
        "lex/violationAnalysis.yaml",
        violationInputData,
        { ...config, debug: config.debug }
      );
    }

    // Calculate total cost and tokens
    const totalCost =
      contentResult.cost + complianceResult.cost + (violationResult?.cost || 0);
    const totalTokens = {
      promptTokens:
        contentResult.tokenUsage.promptTokens +
        complianceResult.tokenUsage.promptTokens +
        (violationResult?.tokenUsage.promptTokens || 0),
      completionTokens:
        contentResult.tokenUsage.completionTokens +
        complianceResult.tokenUsage.completionTokens +
        (violationResult?.tokenUsage.completionTokens || 0),
      totalTokens:
        contentResult.tokenUsage.totalTokens +
        complianceResult.tokenUsage.totalTokens +
        (violationResult?.tokenUsage.totalTokens || 0),
    };

    return {
      analysis: contentResult.output,
      topViolations: topViolations,
      violation: violationResult?.output || null,
      cost: totalCost,
      tokenUsage: totalTokens,
      stepResults: {
        contentAnalysis: contentResult,
        complianceCheck: complianceResult,
        violationAnalysis: violationResult,
      },
    };
  } catch (error) {
    console.error("❌ Error in lex analysis chain:", error);
    throw error;
  }
}

/**
 * Extract top violations from compliance check output
 * @param {string} complianceOutput - Raw output from compliance check
 * @returns {Object} Extracted violation data
 */
function extractTopViolationsFromOutput(complianceOutput) {
  const topViolations = {
    guidelineViolation1: null,
    confidenceScore1: 0,
    reason1: null,
    guidelineViolation2: null,
    confidenceScore2: 0,
    reason2: null,
    guidelineViolation3: null,
    confidenceScore3: 0,
    reason3: null,
  };

  // Enhanced regex to handle multiple formats
  const topRegex =
    /(?:[-*'"\s]*)?(?:\*+)?TOP\s*([1-3])(?:\*+)?(?::|-|\s)\s*G(\d+)\s*[-–—]{1,2}\s*(\d+)\s*[-–—]{1,2}\s*(.+?)(?=(?:\n.*?(?:[-*'"\s]*)?(?:\*+)?TOP\s*[1-3])|\n*$|'.*?\+|`.*?\+)/gis;

  const matches = [...complianceOutput.matchAll(topRegex)];

  for (const match of matches) {
    const index = parseInt(match[1]); // 1, 2, or 3
    const guideline = `G${match[2]}`;
    const score = parseInt(match[3], 10);

    // Clean up the reason
    const reason = match[4]
      .trim()
      .replace(/['"`]+/g, "") // Remove quotes
      .replace(/\*+$/g, "") // Remove trailing asterisks
      .replace(/^\*+/g, "") // Remove leading asterisks
      .replace(/\s*\+\s*$/g, "") // Remove trailing + symbols
      .replace(/\s+/g, " ") // Normalize whitespace
      .replace(/\n/g, " ") // Replace line breaks with spaces
      .trim();

    topViolations[`guidelineViolation${index}`] = guideline;
    topViolations[`confidenceScore${index}`] = score;
    topViolations[`reason${index}`] = reason;
  }

  return topViolations;
}

module.exports = {
  runPromptChain,
  runLexAnalysisChain,
  loadPromptFromFile,
  extractTopViolationsFromOutput,
  testAzureOpenAI,
};
