const cron = require("node-cron");
const { PrismaClient } = require("@prisma/client");
const { ReviewStatus } = require("@prisma/client");
const {
  analyzeAmazonReviewsWithCompliance,
} = require("../services/lex/lexPromptChainProcessor");

const prisma = new PrismaClient();

class LexAiAnalysisWorker {
  constructor() {
    this.isRunning = false;
    this.batchSize = 10; // Process 10 reviews at a time
  }

  async start() {
    console.log("🤖 Starting Lex AI Analysis Worker...");

    // Run every 2 minutes to process scraped reviews
    cron.schedule("*/2 * * * *", async () => {
      if (this.isRunning) {
        console.log("⏳ AI analysis worker already running, skipping...");
        return;
      }

      try {
        this.isRunning = true;
        await this.processReviews();
      } catch (error) {
        console.error("❌ Error in AI analysis worker:", error);
      } finally {
        this.isRunning = false;
      }
    });

    console.log("✅ Lex AI Analysis Worker scheduled to run every 2 minutes");
  }

  async processReviews() {
    console.log("🔄 Processing AI analysis for scraped reviews...");

    try {
      // Get reviews that have been scraped but not yet analyzed
      const unanalyzedReviews = await this.getUnanalyzedReviews();

      if (unanalyzedReviews.length === 0) {
        console.log("📭 No unanalyzed reviews found");
        return;
      }

      console.log(`🤖 Found ${unanalyzedReviews.length} reviews to analyze`);

      // Process reviews in batches
      const batches = this.createBatches(unanalyzedReviews, this.batchSize);

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(
          `📊 Processing batch ${i + 1}/${batches.length} (${
            batch.length
          } reviews)`
        );

        await this.processBatch(batch);

        // Small delay between batches to avoid overwhelming the AI service
        if (i < batches.length - 1) {
          await this.delay(3000); // 3 second delay
        }
      }

      console.log(
        `✅ Completed AI analysis for ${unanalyzedReviews.length} reviews`
      );
    } catch (error) {
      console.error("❌ Error processing AI analysis:", error);
    }
  }

  async getUnanalyzedReviews() {
    try {
      // Get reviews that have been scraped but don't have AI analysis yet
      const reviews = await prisma.lexReview.findMany({
        where: {
          AND: [
            {
              OR: [
                { status: ReviewStatus.DATA_SCRAPED },
              ],
            },
            {
              OR: [
                { prompt1Output: null },
                { prompt2Output: null },
                { prompt3Output: null },
              ],
            },
            {
              reviewContent: {
                not: null,
              },
            },
          ],
        },
        orderBy: {
          createdAt: "asc",
        },
        take: 50, // Limit to prevent overwhelming the system
      });

      return reviews;
    } catch (error) {
      console.error("❌ Error getting unanalyzed reviews:", error);
      return [];
    }
  }

  createBatches(reviews, batchSize) {
    const batches = [];
    for (let i = 0; i < reviews.length; i += batchSize) {
      batches.push(reviews.slice(i, i + batchSize));
    }
    return batches;
  }

  async processBatch(reviewBatch) {
    try {
      // Process each review individually like the original lexAiAnalysisWorker.js
      for (let i = 0; i < reviewBatch.length; i++) {
        const review = reviewBatch[i];

        try {
          console.log(
            `🔍 Analyzing review ${i + 1}/${reviewBatch.length}: ${
              review.reviewID
            }`
          );

          // Update status to AI_ANALYSIS_PENDING before processing
          await prisma.lexReview.update({
            where: { id: review.id },
            data: {
              status: ReviewStatus.AI_ANALYSIS_PENDING,
              updatedAt: new Date(),
            },
          });

          // Convert review to format expected by AI analysis
          const reviewForAnalysis = {
            "Review ID": review.reviewID,
            "Review Title": review.reviewTitle,
            reviewContent: review.reviewContent,
            ASIN: review.asin,
            productTitle: review.productTitle,
            productLink: review.productLink,
            "Review URL": review.reviewLink,
            reviewer: review.reviewer,
            reviewerLink: review.reviewerLink,
            reviewDate: review.reviewDate?.toISOString(),
            reviewScore: review.reviewScore,
          };

          // Run AI analysis on single review
          const analysisResults = await analyzeAmazonReviewsWithCompliance([
            reviewForAnalysis,
          ]);
          const analysisResult = analysisResults[0] || {};

          const violationBoolean = !!(
            analysisResult.violation &&
            analysisResult.violation.trim() !== "" &&
            analysisResult.violation.trim().toLowerCase() !== "false positive"
          );

          // Update review with AI analysis results
          await prisma.lexReview.update({
            where: { id: review.id },
            data: {
              prompt1Output: analysisResult.analysis || null,
              prompt2Output: analysisResult.topViolations
                ? JSON.stringify(analysisResult.topViolations)
                : null,
              prompt3Output: analysisResult.violation || null,
              violation: violationBoolean,
              status: ReviewStatus.COMPLETED,
              updatedAt: new Date(),
            },
          });

          console.log(`✅ Successfully analyzed review: ${review.reviewID}`);
        } catch (analysisError) {
          console.error(
            `❌ Failed to analyze review ${review.reviewID}:`,
            analysisError
          );

          try {
            await prisma.lexReview.update({
              where: { id: review.id },
              data: {
                status: ReviewStatus.FAILED,
                updatedAt: new Date(),
              },
            });
          } catch (updateError) {
            console.error(
              `Failed to update failed review status:`,
              updateError
            );
          }
        }

        // Small delay between analyses to avoid rate limiting
        if (i < reviewBatch.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      console.log(`💾 Completed processing ${reviewBatch.length} reviews`);
    } catch (error) {
      console.error("❌ Error processing batch:", error);

      // Mark any remaining reviews as failed if there's a batch-level error
      for (const review of reviewBatch) {
        try {
          await prisma.lexReview.update({
            where: { id: review.id },
            data: {
              status: ReviewStatus.FAILED,
              updatedAt: new Date(),
            },
          });
        } catch (updateError) {
          console.error(
            `❌ Error marking review ${review.id} as failed:`,
            updateError
          );
        }
      }
    }
  }

  async processSpecificJob(jobId) {
    try {
      console.log(`🎯 Processing AI analysis for specific job: ${jobId}`);

      // Get reviews for the specific job that need analysis
      const jobReviews = await prisma.lexReview.findMany({
        where: {
          jobId: jobId,
          status: ReviewStatus.DATA_SCRAPED,
          reviewContent: {
            not: null,
          },
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      if (jobReviews.length === 0) {
        console.log(`📭 No reviews to analyze for job ${jobId}`);
        return;
      }

      console.log(
        `🤖 Found ${jobReviews.length} reviews to analyze for job ${jobId}`
      );

      // Process in batches
      const batches = this.createBatches(jobReviews, this.batchSize);

      for (const batch of batches) {
        await this.processBatch(batch);
        await this.delay(2000); // 2 second delay between batches
      }

      console.log(`✅ Completed AI analysis for job ${jobId}`);
    } catch (error) {
      console.error(`❌ Error processing AI analysis for job ${jobId}:`, error);
    }
  }

  async getAnalysisStats() {
    try {
      const stats = await prisma.lexReview.groupBy({
        by: ["status"],
        _count: {
          status: true,
        },
      });

      const violationStats = await prisma.lexReview.groupBy({
        by: ["violation"],
        _count: {
          violation: true,
        },
        where: {
          violation: {
            not: null,
          },
        },
      });

      return {
        statusBreakdown: stats,
        violationBreakdown: violationStats,
        totalReviews: await prisma.lexReview.count(),
      };
    } catch (error) {
      console.error("❌ Error getting analysis stats:", error);
      return null;
    }
  }

  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async stop() {
    console.log("🛑 Stopping Lex AI Analysis Worker...");
    // Graceful shutdown logic here
  }
}

// Initialize and start worker if this file is run directly
if (require.main === module) {
  const worker = new LexAiAnalysisWorker();
  worker.start();

  // Graceful shutdown
  process.on("SIGINT", async () => {
    await worker.stop();
    await prisma.$disconnect();
    process.exit(0);
  });
}

module.exports = LexAiAnalysisWorker;
