// Function to generate email HTML content
function generateJobTimeOutEmailHtml(jobs) {
    let htmlStart = `
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            table {
                font-family: Arial, sans-serif;
                border-collapse: collapse;
                width: 100%;
            }
            th, td {
                border: 1px solid #dddddd;
                text-align: left;
                padding: 8px;
            }
            th {
                background-color: #f2f2f2;
            }
        </style>
    </head>
    <body>
        <p>This is a reminder for the following jobs that have been created over 6 hours ago and are still pending:</p>
        <table>
            <tr>
                <th>ID</th>
                <th>Job Name</th>
                <th>File Name</th>
                <th>Created At</th>
            </tr>
    `;

    let htmlEnd = `
        </table>
        <p>Please take the necessary actions as soon as possible.</p>
    </body>
    </html>
    `;

    let htmlRows = '';
    for (const job of jobs) {
        const d = new Date(job.createdAt)
        htmlRows += `
        <tr>
            <td>${job.id}</td>
            <td>${job.jobName}</td>
            <td>${job.fileName}</td>
            <td>${d.toLocaleString()}</td>
        </tr>
        `;
    }

    return htmlStart + htmlRows + htmlEnd;
}

module.exports = generateJobTimeOutEmailHtml
