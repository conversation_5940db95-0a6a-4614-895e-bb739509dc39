const express = require("express");
const prisma = require("../../database/prisma/getPrismaClient");
const { scrapeAsinsFromSellerId } = require("./scrapeAsinsFromSellerId");

const submitSellerHandler = async (req, res) => {
  const { sellerId, countryCode } = req.body;
  if (!sellerId || !countryCode)
    return res.status(400).json({ error: "Missing fields" });

  try {
    // Step 1: Check or create seller
    let seller = await prisma.lexSeller.findUnique({ where: { sellerId } });
    if (!seller) {
      seller = await prisma.lexSeller.create({
        data: { sellerId, countryCode },
      });
    }

    // Step 2: Scrape ASINs for this seller
    const asinResults = await scrapeAsinsFromSellerId(sellerId, countryCode);
    // Expects [{ asin, title, image, productLink, avgRating, totalReviews }, ...]

    // Step 3: Save ASINs into DB
    const savedAsins = [];

    const chunkSize = 20;
    for (let i = 0; i < asinResults.length; i += chunkSize) {
      const chunk = asinResults.slice(i, i + chunkSize);

      const results = await Promise.all(
        chunk.map((asinData) =>
          prisma.lexASIN.upsert({
            where: { asin: asinData.asin,countryCode: countryCode||"US" },
            update: { ...asinData, sellerId: seller.sellerId },
            create: { ...asinData, sellerId: seller.sellerId },
          })
        )
      );

      savedAsins.push(...results);
    }

    // Step 4: Create job (link ASINs later, or here if you want)
    // await prisma.lexReviewScraperJob.create({
    //   data: {
    //     name: `Job_Seller_${sellerId}`,
    //     status: "PENDING",
    //     asins: { connect: [] }, // you can also connect savedAsins here if needed
    //   },
    // });

    // Step 5: Return seller + ASINs
    return res.status(200).json({
      message: "Seller and ASINs submitted successfully",
      seller,
      asins: savedAsins,
    });
  } catch (err) {
    console.error("Error in POST /api/lex/seller:", err);
    return res.status(500).json({ error: "Internal server error" });
  }
};

module.exports = { submitSellerHandler };
