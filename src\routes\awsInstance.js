const express = require('express');
const awsInstanceController = require('../controllers/awsInstanceController');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authMiddleware.verifyToken);
router.use(authMiddleware.isAdmin); // Only admin users can access these endpoints

// Get all instances
router.get('/api/aws/instances', awsInstanceController.listInstances);

// Start an instance
router.post('/api/aws/instances/:instanceId/start', awsInstanceController.startInstance);

// Stop an instance
router.post('/api/aws/instances/:instanceId/stop', awsInstanceController.stopInstance);

// Reboot an instance
router.post('/api/aws/instances/:instanceId/reboot', awsInstanceController.rebootInstance);

module.exports = router; 