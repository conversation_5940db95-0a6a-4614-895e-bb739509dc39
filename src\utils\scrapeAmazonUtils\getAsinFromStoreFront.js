const cheerio = require("cheerio");
const { selectors } = require("../../services/scrapeAmazon/selectors");
const Sentry = require("@sentry/node");
const { getHtmlByProxy } = require("../getHtmlByProxy");

function getAsinFromStoreFront({ htmlData, numOfProducts }) {
  try {
    const $ = cheerio.load(htmlData);
    const products = [];

    $(selectors.productContainer).each((index, container) => {
      const asin = $(container).attr("data-asin");
      const noOfRatings =
        parseInt(
          $(container)
            .find(selectors.numOfRatings)
            .text()
            .trim()
            .replace(",", "")
        ) || 0;

      const ratingText = $(container).find(".a-icon-alt").text();
      const rating = parseFloat(ratingText.split(" ")[0]);

      const monthlySalesText = $(container)
        .find(selectors.sales)
        .first()
        .text();
      const monthlySales = monthlySalesText.includes("100+")
        ? 100
        : parseInt(monthlySalesText) || 0;

      const priceWhole = $(container).find(selectors.priceWhole).text().trim();
      const priceFraction = $(container)
        .find(selectors.priceFraction)
        .text()
        .trim();
      const price = parseFloat(priceWhole + priceFraction) || 0;
      products.push({
        asin,
        noOfRatings,
        rating,
        monthlySales,
        price,
        revenue: price * monthlySales || 0,
      });
    });

    const sortedByReviews = products.sort(
      (a, b) => a.noOfRatings - b.noOfRatings
    );

    // Primary filter: Select products with rating between 3.9 and 4.7, and revenue > 0
    let selectedProducts = sortedByReviews.filter(
      (product) =>
        product.rating >= 3.9 && product.rating <= 4.7 && product.revenue > 0
    );

    // Secondary filter if primary filter does not yield enough products
    if (selectedProducts.length < numOfProducts) {
      const filteredProducts = products.filter(
        (product) =>
          product.noOfRatings < 1000 &&
          product.rating >= 3.9 &&
          product.rating <= 4.7
      );

      const avgReviewCount =
        filteredProducts.reduce((sum, p) => sum + p.noOfRatings, 0) /
        filteredProducts.length;

      const closestToAvg = filteredProducts.sort(
        (a, b) =>
          Math.abs(a.noOfRatings - avgReviewCount) -
          Math.abs(b.noOfRatings - avgReviewCount)
      );

      selectedProducts = selectedProducts.concat(
        closestToAvg.slice(0, numOfProducts - selectedProducts.length)
      );
    }

    // Fallback: If no products match the criteria, select at least one product
    if (selectedProducts.length === 0 && products.length > 0) {
      selectedProducts = [products[0]];
    }

    return selectedProducts
      .slice(0, numOfProducts)
      .map((product) => product.asin);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error Getting Product Asins:", error);
    return [];
  }
}

module.exports = getAsinFromStoreFront;

async function Example(params) {
  const url =
    "https://www.amazon.com/s?me=AA5JU6HN1NCQB&marketplaceID=ATVPDKIKX0DER";
  const htmlData = await getHtmlByProxy(url, 1);
  const data = getAsinFromStoreFront({
    htmlData,
    numOfProducts: 1,
  });
  console.log({ data });
}

// Example();
