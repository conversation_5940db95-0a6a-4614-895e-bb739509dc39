const { Queue } = require("bullmq");
const { connection } = require("./connection");

/**
 * Get comprehensive queue statistics including active and pending job counts
 * @param {string} queueName - Name of the queue (e.g., "mainQueue", "lexQueue")
 * @returns {Promise<Object>} Queue statistics
 */
async function getQueueStats(queueName) {
    try {
        const queue = new Queue(queueName, { connection });

        // Get job counts for different states
        const [
            waiting,
            active,
            completed,
            failed,
            delayed,
            paused
        ] = await Promise.all([
            queue.getWaiting(),
            queue.getActive(),
            queue.getCompleted(),
            queue.getFailed(),
            queue.getDelayed(),
        ]);

        // Get counts
        const stats = {
            queueName,
            timestamp: new Date().toISOString(),
            counts: {
                waiting: waiting.length,
                active: active.length,
                completed: completed.length,
                failed: failed.length,
                delayed: delayed.length,
                total: waiting.length + active.length + completed.length + failed.length + delayed.length
            },
            jobs: {
                waiting: waiting.slice(0, 10).map(job => ({
                    id: job.id,
                    name: job.name,
                    data: job.data,
                    timestamp: job.timestamp
                })),
                active: active.slice(0, 10).map(job => ({
                    id: job.id,
                    name: job.name,
                    data: job.data,
                    timestamp: job.timestamp,
                    processedOn: job.processedOn
                }))
            }
        };

        return stats;
    } catch (error) {
        console.error(`❌ Error getting stats for queue ${queueName}:`, error);
        throw error;
    }
}

/**
 * Get stats for all queues
 * @returns {Promise<Object>} Stats for all queues
 */
async function getAllQueueStats() {
    try {
        const queueNames = ["mainQueue", "lexQueue"];
        const stats = {};

        for (const queueName of queueNames) {
            stats[queueName] = await getQueueStats(queueName);
        }

        return {
            timestamp: new Date().toISOString(),
            queues: stats,
            summary: {
                totalWaiting: Object.values(stats).reduce((sum, queue) => sum + queue.counts.waiting, 0),
                totalActive: Object.values(stats).reduce((sum, queue) => sum + queue.counts.active, 0),
                totalDelayed: Object.values(stats).reduce((sum, queue) => sum + queue.counts.delayed, 0),
                totalFailed: Object.values(stats).reduce((sum, queue) => sum + queue.counts.failed, 0)
            }
        };
    } catch (error) {
        console.error("❌ Error getting all queue stats:", error);
        throw error;
    }
}

/**
 * Get only the counts (lightweight version)
 * @param {string} queueName - Name of the queue
 * @returns {Promise<Object>} Just the counts
 */
async function getQueueCounts(queueName) {
    try {
        const queue = new Queue(queueName, { connection });

        const [waiting, active, delayed, failed] = await Promise.all([
            queue.getWaiting(),
            queue.getActive(),
            queue.getDelayed(),
            queue.getFailed()
        ]);

        return {
            queueName,
            waiting: waiting.length,
            active: active.length,
            delayed: delayed.length,
            failed: failed.length,
            total: waiting.length + active.length + delayed.length + failed.length
        };
    } catch (error) {
        console.error(`❌ Error getting counts for queue ${queueName}:`, error);
        throw error;
    }
}

module.exports = {
    getQueueStats,
    getAllQueueStats,
    getQueueCounts
}; 
