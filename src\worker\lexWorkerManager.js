const LexJobCreationWorker = require("./lexJobCreationWorker");
const LexReviewFetchWorker = require("./lexReviewFetchWorker");
const LexAiAnalysisWorker = require("./lexAiAnalysisWorker");
const cron = require("node-cron");

class LexWorkerManager {
  constructor() {
    this.serverRestarting = false;
    this.jobCreationWorker = new LexJobCreationWorker(this); // Pass reference to this manager
    this.reviewFetchWorker = new LexReviewFetchWorker(this); // Pass reference to this manager
    this.aiWorker = new LexAiAnalysisWorker();
    this.isRunning = false;
    this.isProcessingCycle = false; // Flag to prevent overlapping cycles
    this.cronJob = null; // Store the cron job reference
  }
  getServerRestarting() {
    return this.serverRestarting;
  }

  setServerRestarting(value) {
    this.serverRestarting = value;
  }

  async start() {
    if (this.isRunning) {
      console.log("⚠️ Worker manager is already running");
      return;
    }

    console.log("🚀 Starting Unified Lex Worker Manager...");
    console.log("📋 This manager will run a coordinated workflow:");
    console.log("  - Single cron job every 2 minutes");
    console.log(
      "  - Sequential execution: Job Creation → Review Fetch → AI Analysis"
    );
    console.log("  - No race conditions between workers");
    console.log("");

    try {
      // Initialize workers without starting their individual crons
      await this.initializeWorkers();

      // Start the unified cron orchestrator
      this.startUnifiedCron();

      this.isRunning = true;
      console.log("✅ Unified worker orchestrator started successfully!");
      console.log("");
      console.log("📊 Orchestrator Status:");
      console.log("  - Unified Cron: Running (every 2 minutes)");
      console.log("  - Job Creation Worker: Initialized (runs on-demand)");
      console.log("  - Review Fetch Worker: Initialized (runs on-demand)");
      console.log("  - AI Analysis Worker: Initialized (runs on-demand)");
      console.log("");
      console.log(
        "🔄 Execution Order: Job Creation → Review Fetch → AI Analysis"
      );
      console.log("Press Ctrl+C to stop all workers gracefully...");
    } catch (error) {
      console.error("❌ Error starting unified worker manager:", error);
      this.isRunning = false;
    }
  }

  /**
   * Initialize workers without starting their individual cron jobs
   */
  async initializeWorkers() {
    console.log("🔧 Initializing workers...");
    // Workers are already initialized in constructor
    // We don't call their start() methods to avoid individual cron jobs
    console.log("✅ Workers initialized successfully");
  }

  /**
   * Start the unified cron job that orchestrates all workers sequentially
   */
  startUnifiedCron() {
    console.log("⏰ Starting unified cron orchestrator...");

    // Run every 2 minutes - this replaces all individual worker crons
    this.cronJob = cron.schedule("*/2 * * * *", async () => {
      if (this.isProcessingCycle) {
        console.log("⏳ Previous worker cycle still running, skipping...");
        return;
      }

      if (this.serverRestarting) {
        console.log("🔄 Server restart in progress, skipping worker cycle...");
        return;
      }

      try {
        this.isProcessingCycle = true;
        await this.runWorkerCycle();
      } catch (error) {
        console.error("❌ Error in unified worker cycle:", error);
      } finally {
        this.isProcessingCycle = false;
      }
    });

    console.log("✅ Unified cron orchestrator scheduled (every 2 minutes)");
  }

  /**
   * Run a complete worker cycle: Job Creation → Review Fetch → AI Analysis
   */
  async runWorkerCycle() {
    const cycleStart = Date.now();
    console.log("\n🔄 Starting unified worker cycle...");

    try {
      // Step 1: Job Creation (creates new scraping jobs)
      console.log("📝 Step 1: Running Job Creation Worker...");
      await this.jobCreationWorker.processJobs();
      console.log("✅ Job Creation completed");

      // Step 2: Review Fetch (processes existing jobs and fetches reviews)
      console.log("📥 Step 2: Running Review Fetch Worker...");
      await this.reviewFetchWorker.processReviews();
      console.log("✅ Review Fetch completed");

      // Step 3: AI Analysis (analyzes fetched reviews)
      console.log("🤖 Step 3: Running AI Analysis Worker...");
      await this.aiWorker.processReviews();
      console.log("✅ AI Analysis completed");

      const cycleTime = ((Date.now() - cycleStart) / 1000).toFixed(2);
      console.log(`🎯 Worker cycle completed successfully in ${cycleTime}s\n`);
    } catch (error) {
      const cycleTime = ((Date.now() - cycleStart) / 1000).toFixed(2);
      console.error(
        `❌ Worker cycle failed after ${cycleTime}s:`,
        error.message
      );
      throw error;
    }
  }

  async stop() {
    if (!this.isRunning) {
      console.log("⚠️ Worker manager is not running");
      return;
    }

    console.log("🛑 Stopping Lex Worker Manager...");

    try {
      await this.jobCreationWorker.stop();
      await this.reviewFetchWorker.stop();
      await this.aiWorker.stop();

      this.isRunning = false;
      console.log("✅ All workers stopped gracefully");
    } catch (error) {
      console.error("❌ Error stopping workers:", error);
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      workers: {
        jobCreation: !!this.jobCreationWorker,
        reviewFetch: !!this.reviewFetchWorker,
        aiAnalysis: !!this.aiWorker,
      },
    };
  }

  async getAnalysisStats() {
    if (this.aiWorker) {
      return await this.aiWorker.getAnalysisStats();
    }
    return null;
  }
}

// Worker manager is now controlled by the scheduler
// No direct execution when this file is run

module.exports = LexWorkerManager;
