const LexJobCreationWorker = require("./lexJobCreationWorker");
const LexReviewFetchWorker = require("./lexReviewFetchWorker");
const LexAiAnalysisWorker = require("./lexAiAnalysisWorker");

class LexWorkerManager {
  constructor() {
    this.jobCreationWorker = new LexJobCreationWorker();
    this.reviewFetchWorker = new LexReviewFetchWorker();
    this.aiWorker = new LexAiAnalysisWorker();
    this.isRunning = false;
    this.serverRestarting = false;
  }

  async start() {
    if (this.isRunning) {
      console.log("⚠️ Worker manager is already running");
      return;
    }

    console.log("🚀 Starting Lex Worker Manager...");
    console.log("📋 This manager will run:");
    console.log("  - Job Creation Worker (every 3 minutes)");
    console.log("  - Review Fetch Worker (every minute)");
    console.log("  - AI Analysis Worker (every 2 minutes)");
    console.log("");

    try {
      // Start all workers
      await this.jobCreationWorker.start();
      await this.reviewFetchWorker.start();
      await this.aiWorker.start();

      this.isRunning = true;
      console.log("✅ All workers started successfully!");
      console.log("");
      console.log("📊 Worker Status:");
      console.log(
        "  - Job Creation Worker: Running (creates jobs every 3 minutes)"
      );
      console.log(
        "  - Review Fetch Worker: Running (checks for reviews every minute)"
      );
      console.log(
        "  - AI Analysis Worker: Running (analyzes reviews every 2 minutes)"
      );
      console.log("");
      console.log("Press Ctrl+C to stop all workers gracefully...");
    } catch (error) {
      console.error("❌ Error starting workers:", error);
      this.isRunning = false;
    }
  }

  async stop() {
    if (!this.isRunning) {
      console.log("⚠️ Worker manager is not running");
      return;
    }

    console.log("🛑 Stopping Lex Worker Manager...");

    try {
      await this.jobCreationWorker.stop();
      await this.reviewFetchWorker.stop();
      await this.aiWorker.stop();

      this.isRunning = false;
      console.log("✅ All workers stopped gracefully");
    } catch (error) {
      console.error("❌ Error stopping workers:", error);
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      workers: {
        jobCreation: !!this.jobCreationWorker,
        reviewFetch: !!this.reviewFetchWorker,
        aiAnalysis: !!this.aiWorker,
      },
    };
  }

  async getAnalysisStats() {
    if (this.aiWorker) {
      return await this.aiWorker.getAnalysisStats();
    }
    return null;
  }
}

// Worker manager is now controlled by the scheduler
// No direct execution when this file is run

module.exports = LexWorkerManager;
