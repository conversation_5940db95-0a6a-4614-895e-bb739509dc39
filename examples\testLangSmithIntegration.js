/**
 * Test file to verify Lang<PERSON>mith integration with Azure OpenAI
 * This file demonstrates the new prompt chain functionality with tracing
 */

const {
  runPrompt<PERSON>hain,
  runLexAnalysisChain,
} = require("../src/services/spike/runPromptChain");
require("dotenv").config();

// Sample review for testing
const testReview = {
  productTitle: "Amazon Basics 24-Pack AA Alkaline Batteries",
  "Review Title": "Poor customer service",
  reviewContent:
    "The batteries work fine but the seller's customer service was terrible. They never responded to my emails and took weeks to process my return request. Very disappointed with the service.",
  "Review ID": "TEST123",
};

async function testBasicPromptChain() {
  console.log("🧪 Testing basic prompt chain with LangSmith tracing...\n");

  try {
    const simplePrompt = `
- role: system
  content: "You are a helpful assistant that analyzes product reviews."
  
- role: user
  content: |
    Analyze this review and determine if it's primarily about the product or the service:
    
    Product: {{productTitle}}
    Review: {{reviewContent}}
    
    Respond with either "PRODUCT" or "SERVICE" and a brief explanation.
`;

    const result = await runPrompt<PERSON>hain(
      simplePrompt,
      {
        productTitle: testReview.productTitle,
        reviewContent: testReview.reviewContent,
      },
      {
        debug: true,
        temperature: 0.7,
      }
    );

    console.log("✅ Basic prompt chain test completed:");
    console.log("Output:", result.output);
    console.log("Cost:", `$${(result.cost || 0).toFixed(4)}`);
    console.log("Tokens:", result.tokenUsage);
    console.log("\n" + "=".repeat(50) + "\n");

    return result;
  } catch (error) {
    console.error(error);
    console.error("❌ Error in basic prompt chain test:", error.message);
    throw error;
  }
}

async function testLexAnalysisChain() {
  console.log("🧪 Testing lex analysis chain with LangSmith tracing...\n");

  try {
    const result = await runLexAnalysisChain(testReview, {
      debug: true,
      temperature: 0.3,
    });

    console.log("✅ Lex analysis chain test completed:");
    console.log("Analysis preview:", result.analysis.substring(0, 150) + "...");
    console.log("Top violation:", result.topViolations.guidelineViolation1);
    console.log("Confidence score:", result.topViolations.confidenceScore1);
    console.log("Has violation output:", result.violation ? "Yes" : "No");
    console.log("Total cost:", `$${(result.cost || 0).toFixed(4)}`);
    console.log("Total tokens:", result.tokenUsage.totalTokens);
    console.log("\n" + "=".repeat(50) + "\n");

    return result;
  } catch (error) {
    console.error("❌ Error in lex analysis chain test:", error.message);
    throw error;
  }
}

async function testFileBasedPrompts() {
  console.log("🧪 Testing file-based prompts with LangSmith tracing...\n");

  try {
    const result = await runPromptChain(
      "lex/contentAnalysis.yaml",
      {
        productTitle: testReview.productTitle,
        reviewTitle: testReview["Review Title"],
        reviewContent: testReview.reviewContent,
      },
      {
        debug: true,
        temperature: 0.3,
      }
    );

    console.log("✅ File-based prompt test completed:");
    console.log("Output preview:", result.output.substring(0, 200) + "...");
    console.log("Cost:", `$${(result.cost || 0).toFixed(4)}`);
    console.log("Tokens:", result.tokenUsage);
    console.log("\n" + "=".repeat(50) + "\n");

    return result;
  } catch (error) {
    console.error("❌ Error in file-based prompt test:", error.message);
    throw error;
  }
}

async function runAllTests() {
  console.log("🚀 Starting LangSmith Integration Tests\n");
  console.log("=".repeat(60) + "\n");

  try {
    await testBasicPromptChain();
    await testFileBasedPrompts();
    await testLexAnalysisChain();

    console.log("🎉 All tests completed successfully!");
    console.log(
      "\n📊 LangSmith traces should be visible in your LangSmith dashboard"
    );
    console.log("🔗 Check: https://smith.langchain.com/");
  } catch (error) {
    console.error("💥 Test suite failed:", error.message);
    process.exit(1);
  }
}

// Export functions for individual testing
module.exports = {
  testBasicPromptChain,
  testLexAnalysisChain,
  testFileBasedPrompts,
  runAllTests,
};

// Uncomment to run tests
runAllTests().catch(console.error);
