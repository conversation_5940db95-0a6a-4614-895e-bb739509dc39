const express = require("express");
const multer = require("multer");
const csv = require("csv-parser");
const fs = require("fs");
const prisma = require("../database/prisma/getPrismaClient");
const {
  generateSellerJobName,
  generateAsinJobName,
} = require("../utils/lexUtils/jobUtils");
const { addToQueue } = require("../utils/bull/bull");
const { ScrapingStatus } = require("@prisma/client");

const router = express.Router();
const uploadDir = "LexUploads";
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const upload = multer({ dest: uploadDir });

// SELLER ROUTES
// GET /api/lex/seller?seller_id=abc&seller_name=xyz,abc,def&status=SCRAPED (optional filters)
router.get("/api/lex/seller", async (req, res) => {
  try {
    const { seller_id, seller_name, status, limit = 20 } = req.query;

    if (seller_id) {
      // Return specific seller with detailed information
      const seller = await prisma.lexSeller.findUnique({
        where: { sellerId: seller_id },
        include: {
          asins: {
            select: {
              asin: true,
              title: true,
              image: true,
              productLink: true,
              avgRating: true,
              totalReviews: true,
              status: true,
              sellerId: true,
              countryCode: true,
              createdAt: true,
              updatedAt: true,
            },
            orderBy: { totalReviews: "desc" },
          },
        },
      });

      if (!seller) {
        return res.status(404).json({
          success: false,
          error: "Seller not found",
        });
      }

      return res.status(200).json({
        success: true,
        seller: {
          id: seller.id,
          sellerId: seller.sellerId,
          name: seller.name,
          countryCode: seller.countryCode,
          status: seller.status,
          createdAt: seller.createdAt,
          updatedAt: seller.updatedAt,
          totalAsins: seller.asins.length,
          asins: seller.asins,
          recentJobs: seller.jobs,
        },
      });
    } else {
      // Build where clause for filtering
      let whereClause = {};

      // Add seller name filter - support multiple names
      if (seller_name) {
        const sellerNames = Array.isArray(seller_name)
          ? seller_name
          : seller_name.split(",").map((name) => name.trim());

        if (sellerNames.length === 1) {
          // Single seller name - use contains for partial matching
          whereClause.name = {
            contains: sellerNames[0],
            mode: "insensitive",
          };
        } else {
          // Multiple seller names - use OR with contains for each
          whereClause.OR = sellerNames.map((name) => ({
            name: {
              contains: name,
              mode: "insensitive",
            },
          }));
        }
      }

      // Add status filter
      if (status) {
        whereClause.status = status.toUpperCase();
      }

      // Return all sellers with ASIN count
      const sellers = await prisma.lexSeller.findMany({
        where: whereClause,
        take: parseInt(limit),
        orderBy: { updatedAt: "desc" },
        include: {
          _count: {
            select: {
              asins: true,
            },
          },
        },
      });

      // Format sellers with total ASIN count
      const formattedSellers = sellers.map((seller) => ({
        id: seller.id,
        sellerId: seller.sellerId,
        name: seller.name,
        countryCode: seller.countryCode,
        status: seller.status,
        createdAt: seller.createdAt,
        updatedAt: seller.updatedAt,
        totalAsins: seller._count.asins,
      }));

      return res.status(200).json({
        success: true,
        sellers: formattedSellers,
        count: sellers.length,
        filters: {
          seller_name: Array.isArray(seller_name)
            ? seller_name
            : seller_name
            ? seller_name.split(",")
            : null,
          status: status || null,
          limit: parseInt(limit),
        },
      });
    }
  } catch (error) {
    console.error("Error in GET /api/lex/seller:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// POST /api/lex/seller - CREATE SELLER JOB
router.post("/api/lex/seller", async (req, res) => {
  try {
    const { seller_id, country_code = "US" } = req.body;

    if (!seller_id) {
      return res.status(400).json({
        success: false,
        error: "seller_id is required",
      });
    }

    // Create or update seller record and mark as PENDING
    const sellerRecord = await prisma.lexSeller.upsert({
      where: { sellerId: seller_id },
      update: {
        countryCode: country_code,
        status: ScrapingStatus.PENDING,
        updatedAt: new Date(),
      },
      create: {
        sellerId: seller_id,
        countryCode: country_code,
        name: null,
        status: ScrapingStatus.PENDING,
      },
    });

    console.log(`✅ Seller marked as PENDING: ${seller_id}`);

    // Push job to Bull queue
    const queueJob = await addToQueue("singleLexSeller", {
      sellerId: sellerRecord.id,
      countryCode: country_code,
    });

    console.log(`✅ Job pushed to Bull: singleLexSeller`);

    return res.status(201).json({
      success: true,
      message: "Seller processing job queued successfully.",
      seller: {
        id: sellerRecord.id,
        sellerId: sellerRecord.sellerId,
        name: sellerRecord.name,
        countryCode: sellerRecord.countryCode,
        status: sellerRecord.status,
      },
      queueJobId: queueJob.id,
    });
  } catch (error) {
    console.error("Error in POST /api/lex/seller:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// ASIN ROUTES
// GET /api/lex/asin?seller_id=abc,def&seller_name=xyz,abc&reviews=gt300&status=SCRAPED&asin=B123,B456
router.get("/api/lex/asin", async (req, res) => {
  try {
    const {
      seller_id,
      seller_name,
      reviews,
      filter,
      asin,
      status,
      page = 1,
      limit = 20,
      type,
    } = req.query;

    let whereClause = {};

    // Handle multiple seller IDs
    if (seller_id) {
      const sellerIds = Array.isArray(seller_id)
        ? seller_id
        : seller_id.split(",").map((id) => id.trim());

      if (sellerIds.length > 0) {
        const sellers = await prisma.lexSeller.findMany({
          where: { sellerId: { in: sellerIds } },
          select: { id: true, sellerId: true },
        });

        if (sellers.length === 0) {
          return res.status(404).json({
            success: false,
            error: `No sellers found with IDs: ${sellerIds.join(", ")}`,
          });
        }

        const foundSellerIds = sellers.map((s) => s.sellerId);
        const notFoundSellerIds = sellerIds.filter(
          (id) => !foundSellerIds.includes(id)
        );

        if (notFoundSellerIds.length > 0) {
          console.warn(`Seller IDs not found: ${notFoundSellerIds.join(", ")}`);
        }

        whereClause.LexSellerId = { in: sellers.map((s) => s.id) };
      }
    }

    // Handle multiple seller names - improved logic
    if (seller_name) {
      const sellerNames = Array.isArray(seller_name)
        ? seller_name
        : seller_name.split(",").map((name) => name.trim());

      const sellerNameConditions = sellerNames.flatMap((name) => [
        {
          sellerName: {
            contains: name,
            mode: "insensitive",
          },
        },
        {
          seller: {
            name: {
              contains: name,
              mode: "insensitive",
            },
          },
        },
      ]);

      if (whereClause.LexSellerId) {
        // If we already have seller ID filter, combine with AND
        whereClause.AND = [
          { LexSellerId: whereClause.LexSellerId },
          { OR: sellerNameConditions },
        ];
        delete whereClause.LexSellerId;
      } else {
        // If no seller ID filter, just use seller name conditions
        whereClause.OR = sellerNameConditions;
      }
    }

    // Handle multiple ASINs
    if (asin) {
      const asinArray = Array.isArray(asin)
        ? asin
        : asin.split(",").map((a) => a.trim());
      if (asinArray.length > 0) {
        whereClause.asin = { in: asinArray };
      }
    }

    // Handle reviews filter
    if (reviews) {
      const reviewsFilter = parseReviewsFilter(reviews);
      if (reviewsFilter) {
        whereClause.totalReviews = reviewsFilter;
      }
    }

    // Handle status filter
    if (status) {
      whereClause.status = status.toUpperCase();
    }

    // handle asin-type filter
    if (type) {
      whereClause.type = type;
    }

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Get total count for pagination
    const totalCount = await prisma.lexASIN.count({
      where: whereClause,
    });

    const asins = await prisma.lexASIN.findMany({
      where: whereClause,
      select: {
        asin: true,
        title: true,
        image: true,
        productLink: true,
        avgRating: true,
        totalReviews: true,
        status: true,
        type: true,
        sellerId: true,
        sellerName: true,
        countryCode: true,
        LexSellerId: true,
        createdAt: true,
        updatedAt: true,
        latestData: true,
        seller: {
          select: {
            sellerId: true,
            name: true,
            status: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limitNum,
    });
    const totalPages = Math.ceil(totalCount / limitNum);

    return res.status(200).json({
      success: true,
      asins,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1,
      },
      count: asins.length,
      filters: {
        seller_id: seller_id
          ? Array.isArray(seller_id)
            ? seller_id
            : seller_id.split(",")
          : null,
        seller_name: seller_name
          ? Array.isArray(seller_name)
            ? seller_name
            : seller_name.split(",")
          : null,
        asin: asin ? (Array.isArray(asin) ? asin : asin.split(",")) : null,
        reviews,
        status: status || null,
        type: type || null,
        filter,
      },
    });
  } catch (error) {
    console.error("Error in GET /api/lex/asin:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// POST /api/lex/asin - SINGLE ASIN JOB
router.post("/api/lex/asin", async (req, res) => {
  try {
    let { asin, country_code = "US" } = req.body;

    if (!asin) {
      return res.status(400).json({
        success: false,
        error: "asin is required",
      });
    }

    if (Array.isArray(asin)) {
      return res.status(400).json({
        success: false,
        error:
          "Use /api/lex/asin/bulk for multiple ASINs. This endpoint accepts single ASIN only.",
      });
    }

    const asinRecord = await prisma.lexASIN.upsert({
      where: {
        asin_countryCode: {
          asin: asin,
          countryCode: country_code,
        },
      },
      update: {
        status: "PENDING",
        countryCode: country_code,
        updatedAt: new Date(),
      },
      create: {
        asin: asin,
        status: "PENDING",
        countryCode: country_code,
      },
    });

    const job = await addToQueue("singleLexAsin", {
      asin: asinRecord.id,
      countryCode: country_code,
    });

    console.log(
      `✅ Queued Single ASIN Job for ${asinRecord.asin} → JobID: ${job.id}`
    );

    return res.status(201).json({
      success: true,
      message: "Single ASIN scraping job queued successfully.",
      jobId: job.id,
    });
  } catch (error) {
    console.error("Error in POST /api/lex/asin:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

router.patch("/api/lex/asin", async (req, res) => {
  try {
    const { asin, type } = req.body;

    if (!asin) {
      return res.status(400).json({
        success: false,
        error: "asin is required",
      });
    }

    if (type && type !== "CLIENT") {
      return res.status(400).json({
        success: false,
        error: "Invalid type value. Allowed: 'CLIENT' or null.",
      });
    }

    const updatedAsin = await prisma.lexASIN.updateMany({
      where: { asin },
      data: {
        type: type === "CLIENT" ? "CLIENT" : null,
        updatedAt: new Date(),
      },
    });

    return res.status(200).json({
      success: true,
      message: "ASIN updated successfully.",
      data: updatedAsin,
    });
  } catch (error) {
    console.error("Error in PATCH /api/lex/asin:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// POST /api/lex/asin/bulk - BULK ASIN JOB
router.post("/api/lex/asin/bulk", upload.single("file"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: "CSV file is required",
      });
    }

    const csvFilePath = req.file.path;
    const asinsToProcess = [];

    // Parse CSV
    await new Promise((resolve, reject) => {
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on("data", (row) => {
          // Look for ASIN column (case insensitive)
          const asinKey = Object.keys(row).find((key) =>
            key.toLowerCase().includes("asin")
          );

          // Look for country/country_code column (case insensitive)
          const countryKey = Object.keys(row).find(
            (key) =>
              key.toLowerCase().includes("country") ||
              key.toLowerCase().includes("code") ||
              key.toLowerCase() === "country_code"
          );

          if (asinKey && row[asinKey] && row[asinKey].trim()) {
            const asin = row[asinKey].trim();
            const countryCode =
              countryKey && row[countryKey]
                ? row[countryKey].trim().toUpperCase()
                : "US";

            asinsToProcess.push({
              asin: asin,
              countryCode: countryCode,
            });
          }
        })
        .on("end", resolve)
        .on("error", reject);
    });

    // Clean up uploaded file
    try {
      fs.unlinkSync(csvFilePath);
    } catch (cleanupError) {
      console.error("Error cleaning up uploaded file:", cleanupError);
    }

    if (asinsToProcess.length === 0) {
      return res.status(400).json({
        success: false,
        error: "No valid ASINs found in CSV file",
      });
    }

    console.log(`📦 Processing ${asinsToProcess.length} ASINs from CSV`);

    // Remove duplicates based on asin + countryCode combination
    const uniqueAsins = asinsToProcess.reduce((acc, current) => {
      const key = `${current.asin}_${current.countryCode}`;
      if (!acc.has(key)) {
        acc.set(key, current);
      }
      return acc;
    }, new Map());

    const uniqueAsinArray = Array.from(uniqueAsins.values());

    const asinRecords = [];
    const failedAsins = [];

    // Create/update ASIN records in database
    for (const { asin, countryCode } of uniqueAsinArray) {
      try {
        const asinRecord = await prisma.lexASIN.upsert({
          where: {
            asin_countryCode: {
              asin: asin,
              countryCode: countryCode,
            },
          },
          update: {
            status: "PENDING",
            updatedAt: new Date(),
          },
          create: {
            asin: asin,
            countryCode: countryCode,
            status: "PENDING",
          },
        });

        asinRecords.push(asinRecord);
        console.log(`✅ Created/Updated ASIN: ${asin} (${countryCode})`);
      } catch (error) {
        console.error(
          `❌ Error processing ASIN ${asin} (${countryCode}):`,
          error.message
        );
        failedAsins.push({
          asin,
          countryCode,
          reason: error.message,
        });
      }
    }

    if (asinRecords.length === 0) {
      return res.status(400).json({
        success: false,
        error: "No ASINs could be processed successfully",
        failedAsins,
      });
    }

    // Queue individual jobs for each ASIN
    const queuedJobs = [];
    const failedQueues = [];

    for (const asinRecord of asinRecords) {
      try {
        const job = await addToQueue("singleLexAsin", {
          asin: asinRecord.id,
          countryCode: asinRecord.countryCode,
        });

        queuedJobs.push({
          asin: asinRecord.asin,
          countryCode: asinRecord.countryCode,
          jobId: job.id,
        });

        console.log(
          `✅ Queued: ${asinRecord.asin} (${asinRecord.countryCode}) → Job #${job.id}`
        );
      } catch (queueError) {
        console.error(
          `❌ Failed to queue ASIN ${asinRecord.asin}:`,
          queueError.message
        );
        failedQueues.push({
          asin: asinRecord.asin,
          countryCode: asinRecord.countryCode,
          reason: queueError.message,
        });
      }
    }

    return res.status(201).json({
      success: true,
      message: `Successfully queued ${queuedJobs.length} ASIN scraping jobs.`,
      summary: {
        totalFromCSV: asinsToProcess.length,
        uniqueCombinations: uniqueAsinArray.length,
        processed: asinRecords.length,
        queued: queuedJobs.length,
        failed: failedAsins.length + failedQueues.length,
      },
      queuedJobs,
      ...(failedAsins.length > 0 && { failedAsins }),
      ...(failedQueues.length > 0 && { failedQueues }),
    });
  } catch (error) {
    console.error("Error in POST /api/lex/asin/bulk:", error);

    // Clean up uploaded file in case of error
    if (req.file?.path && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        console.error("Error cleaning up uploaded file:", cleanupError);
      }
    }

    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// POST /api/lex/asin/download - DOWNLOAD ASIN DATA AS CSV
router.post("/api/lex/asin/download", async (req, res) => {
  try {
    const {
      seller_id,
      seller_name,
      reviews,
      filter,
      asin,
      status,
      page = 1,
      limit = 1000,
    } = req.body;

    let whereClause = {};

    // Handle multiple seller IDs
    if (seller_id) {
      const sellerIds = Array.isArray(seller_id)
        ? seller_id
        : seller_id.split(",").map((id) => id.trim());

      if (sellerIds.length > 0) {
        const sellers = await prisma.lexSeller.findMany({
          where: { sellerId: { in: sellerIds } },
          select: { id: true, sellerId: true },
        });

        if (sellers.length === 0) {
          return res.status(404).json({
            success: false,
            error: `No sellers found with IDs: ${sellerIds.join(", ")}`,
          });
        }

        const foundSellerIds = sellers.map((s) => s.sellerId);
        const notFoundSellerIds = sellerIds.filter(
          (id) => !foundSellerIds.includes(id)
        );

        if (notFoundSellerIds.length > 0) {
          console.warn(`Seller IDs not found: ${notFoundSellerIds.join(", ")}`);
        }

        whereClause.LexSellerId = { in: sellers.map((s) => s.id) };
      }
    }

    // Handle multiple seller names - same logic as GET route
    if (seller_name) {
      const sellerNames = Array.isArray(seller_name)
        ? seller_name
        : seller_name.split(",").map((name) => name.trim());

      const sellerNameConditions = sellerNames.flatMap((name) => [
        {
          sellerName: {
            contains: name,
            mode: "insensitive",
          },
        },
        {
          seller: {
            name: {
              contains: name,
              mode: "insensitive",
            },
          },
        },
      ]);

      if (whereClause.LexSellerId) {
        // If we already have seller ID filter, combine with AND
        whereClause.AND = [
          { LexSellerId: whereClause.LexSellerId },
          { OR: sellerNameConditions },
        ];
        delete whereClause.LexSellerId;
      } else {
        // If no seller ID filter, just use seller name conditions
        whereClause.OR = sellerNameConditions;
      }
    }

    // Handle multiple ASINs
    if (asin) {
      const asinArray = Array.isArray(asin)
        ? asin
        : asin.split(",").map((a) => a.trim());
      if (asinArray.length > 0) {
        whereClause.asin = { in: asinArray };
      }
    }

    // Handle reviews filter
    if (reviews) {
      const reviewsFilter = parseReviewsFilter(reviews);
      if (reviewsFilter) {
        whereClause.totalReviews = reviewsFilter;
      }
    }

    // Handle status filter
    if (status) {
      whereClause.status = status.toUpperCase();
    }

    console.log(`Downloading ASIN data with filters:`, {
      seller_id,
      seller_name,
      reviews,
      asin,
      status,
      whereClause: JSON.stringify(whereClause, null, 2),
    });

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const asinData = await prisma.lexASIN.findMany({
      where: whereClause,
      include: {
        seller: {
          select: {
            sellerId: true,
            name: true,
            status: true,
          },
        },
      },
      orderBy: {
        totalReviews: "desc",
      },
      skip,
      take: limitNum,
    });

    if (asinData.length === 0) {
      return res.status(404).json({
        success: false,
        error: "No ASIN data found for the provided filters",
        appliedFilters: {
          seller_id: seller_id || null,
          seller_name: seller_name
            ? Array.isArray(seller_name)
              ? seller_name
              : seller_name.split(",")
            : null,
          reviews: reviews || null,
          asin: asin || null,
          status: status || null,
          page: pageNum,
          limit: limitNum,
        },
      });
    }

    console.log(`✅ Found data for ${asinData.length} ASINs`);

    // Updated CSV headers
    const csvHeaders = [
      "ASIN",
      "Title",
      "Seller ID",
      "Seller Name",
      "Country Code",
      "ASIN Status",
      "Category",
      "Average Rating",
      "Total Reviews",
      "Image URL",
      "Product Link",
      "Review Counts",
      "Latest Avg Rating",
      "Latest Total Reviews",
      "Latest Reviews Count",
      "Created At",
      "Updated At",
    ];

    // Updated CSV rows
    const csvRows = asinData.map((asin) => [
      asin.asin || "",
      `"${(asin.title || "").replace(/"/g, '""')}"`,
      asin.sellerId || "",
      `"${(asin.sellerName || "").replace(/"/g, '""')}"`,
      asin.countryCode || "",
      asin.status || "",
      `"${(asin.category || "").replace(/"/g, '""')}"`,
      asin.avgRating || "",
      asin.totalReviews || 0,
      asin.image || "",
      asin.productLink || "",
      asin.reviewCounts
        ? `"${JSON.stringify(asin.reviewCounts).replace(/"/g, '""')}"`
        : "",
      asin.latestData?.avgRating ? `"${asin?.latestData?.avgRating}"` : "",
      asin.latestData?.totalReviews
        ? `"${asin?.latestData?.totalReviews}"`
        : "",
      asin.latestData?.reviewCounts
        ? `"${asin?.latestData?.reviewCounts}"`
        : "",
      asin.createdAt ? asin.createdAt.toISOString() : "",
      asin.updatedAt ? asin.updatedAt.toISOString() : "",
    ]);

    const csvContent = [
      csvHeaders.join(","),
      ...csvRows.map((row) => row.join(",")),
    ].join("\n");

    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .split("T")[0];

    let filenameParts = ["asin_data", timestamp];
    if (seller_name) {
      const names = Array.isArray(seller_name)
        ? seller_name
        : seller_name.split(",");
      filenameParts.push(`sellers_${names.length}`);
    }
    if (seller_id)
      filenameParts.push(
        `sellerID_${Array.isArray(seller_id) ? seller_id.join("-") : seller_id}`
      );
    if (reviews) filenameParts.push(`reviews_${reviews}`);
    if (asin)
      filenameParts.push(
        `asins_${Array.isArray(asin) ? asin.length : asin.split(",").length}`
      );
    filenameParts.push(`${asinData.length}_items`);

    const filename = `${filenameParts.join("_")}.csv`;

    console.log(`Generating CSV file: ${filename}`);

    res.setHeader("Content-Type", "text/csv");
    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
    res.setHeader("Content-Length", Buffer.byteLength(csvContent));

    return res.status(200).send(csvContent);
  } catch (error) {
    console.error("Error in POST /api/lex/asin/download:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});
// JOB STATUS ROUTES

// GET /api/lex/jobs - Get all jobs with filtering
router.get("/api/lex/jobs", async (req, res) => {
  try {
    const { type, status, seller_id, asin, limit = 10 } = req.query;

    let whereClause = {};
    if (type) whereClause.type = type;
    if (status) whereClause.status = status;
    if (seller_id) whereClause.sellerId = seller_id;
    if (asin) whereClause.asin = asin;

    const jobs = await prisma.lexJob.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
      take: parseInt(limit),
      include: {
        asins: {
          select: {
            asin: true,
            status: true,
          },
          take: 5, // Limit ASIN details for performance
        },
      },
    });

    const formattedJobs = jobs.map((job) => ({
      id: job.id,
      name: job.name,
      type: job.type,
      status: job.status,
      sellerId: job.sellerId,
      asin: job.asin,
      countryCode: job.countryCode,
      totalAsins: job.totalAsins,
      totalReviews: job.totalReviews,
      errorMessage: job.errorMessage,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
      ...(job.type === "BULK_ASIN" && {
        itemStats: {
          total: job.asins.length,
          pending: job.asins.filter((asin) => asin.status === "PENDING").length,
          scraped: job.asins.filter((asin) => asin.status === "SCRAPED").length,
          failed: job.asins.filter((asin) => asin.status === "FAILED").length,
        },
      }),
    }));

    return res.status(200).json({
      success: true,
      jobs: formattedJobs,
      count: jobs.length,
    });
  } catch (error) {
    console.error("Error in GET /api/lex/jobs:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// GET /api/lex/jobs/stats - Get job statistics
router.get("/api/lex/jobs/stats", async (req, res) => {
  try {
    // Get job stats by type and status
    const jobStats = await prisma.lexJob.groupBy({
      by: ["type", "status"],
      _count: { id: true },
    });

    // Get recent jobs
    const recentJobs = await prisma.lexJob.findMany({
      orderBy: { createdAt: "desc" },
      take: 10,
      select: {
        id: true,
        name: true,
        type: true,
        status: true,
        sellerId: true,
        asin: true,
        createdAt: true,
      },
    });

    // Format stats for easier consumption
    const formattedStats = {};
    jobStats.forEach((stat) => {
      if (!formattedStats[stat.type]) {
        formattedStats[stat.type] = {};
      }
      formattedStats[stat.type][stat.status] = stat._count.id;
    });

    return res.status(200).json({
      success: true,
      stats: formattedStats,
      recentJobs,
    });
  } catch (error) {
    console.error("Error in GET /api/lex/jobs/stats:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// GET /api/lex/jobs/:jobId - Get specific job details
router.get("/api/lex/jobs/:jobId", async (req, res) => {
  try {
    const { jobId } = req.params;

    const job = await prisma.lexJob.findUnique({
      where: { id: parseInt(jobId) },
      include: {
        asins: {
          select: {
            id: true,
            asin: true,
            title: true,
            status: true,
            avgRating: true,
            totalReviews: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: { createdAt: "asc" },
        },
        reviews: {
          select: {
            id: true,
            reviewID: true,
            reviewScore: true,
            violation: true,
            status: true,
          },
          take: 10,
        },
      },
    });

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    // Calculate statistics based on job type
    let stats = {};
    if (job.type === "BULK_ASIN" && job.asins.length > 0) {
      stats = {
        total: job.asins.length,
        pending: job.asins.filter((asin) => asin.status === "PENDING").length,
        scraped: job.asins.filter((asin) => asin.status === "SCRAPED").length,
        failed: job.asins.filter((asin) => asin.status === "FAILED").length,
      };
    } else if (
      (job.type === "SINGLE_REVIEW" || job.type === "BULK_REVIEW") &&
      job.reviews.length > 0
    ) {
      stats = {
        total: job.reviews.length,
        pending: job.reviews.filter((review) => review.status === "PENDING")
          .length,
        processed: job.reviews.filter((review) => review.status === "COMPLETED")
          .length,
        violations: job.reviews.filter((review) => review.violation === true)
          .length,
      };
    }

    return res.status(200).json({
      success: true,
      job: {
        id: job.id,
        name: job.name,
        type: job.type,
        status: job.status,
        sellerId: job.sellerId,
        asin: job.asin,
        totalAsins: job.totalAsins,
        totalReviews: job.totalReviews,
        countryCode: job.countryCode,
        errorMessage: job.errorMessage,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
      },
      asins: job.asins,
      reviews: job.reviews,
      stats,
    });
  } catch (error) {
    console.error("Error in GET /api/lex/jobs/:jobId:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// COOKIE MANAGEMENT ROUTES
// API route to add cookies
router.post("/api/lex/add-cookies", async (req, res) => {
  try {
    const { emailId, cookiesData, countryCode } = req.body;
    const normalizedCountryCode = normalizeCountryCode(countryCode);

    if (!emailId) {
      return res.status(400).json({
        success: false,
        message: "Email ID is required",
      });
    }

    // Validate country code format
    // Check if email ID + country code combination already exists
    const existingCookie = await prisma.lexReviewScraperCookies.findFirst({
      where: {
        emailId: emailId,
        countryCode: normalizedCountryCode,
      },
    });

    if (existingCookie) {
      return res.status(409).json({
        success: false,
        message: `Cookies for email ID '${emailId}' and country '${normalizedCountryCode}' already exist. Use Edit cookie to update existing cookies.`,
        existingRecord: {
          id: existingCookie.id,
          emailId: existingCookie.emailId,
          countryCode: existingCookie.countryCode,
          status: existingCookie.cookieStatus,
          active: existingCookie.active,
          createdAt: existingCookie.createdAt,
          updatedAt: existingCookie.updatedAt,
        },
        suggestion: "Use Edit cookie to update existing cookies",
      });
    }

    let cookieArray = [];

    if (Array.isArray(cookiesData)) {
      // Direct array of cookie objects - store as it is
      cookieArray = cookiesData;
    } else if (typeof cookiesData === "string") {
      try {
        const parsedCookies = JSON.parse(cookiesData);
        if (Array.isArray(parsedCookies)) {
          cookieArray = parsedCookies;
        } else {
          return res.status(400).json({
            success: false,
            message: "Parsed cookies data must be an array",
          });
        }
      } catch (parseError) {
        return res.status(400).json({
          success: false,
          message: "Invalid JSON format for cookies data",
        });
      }
    } else {
      return res.status(400).json({
        success: false,
        message: "Cookie data must be provided as an array or JSON string",
      });
    }

    if (cookieArray.length === 0) {
      return res.status(400).json({
        success: false,
        message: "No cookies found in the provided data",
      });
    }

    // Store the entire cookie array as JSON string
    const cookieJsonString = JSON.stringify(cookieArray);

    const newCookie = await prisma.lexReviewScraperCookies.create({
      data: {
        emailId: emailId,
        cookieKey: cookieJsonString,
        cookieStatus: "ACTIVE",
        countryCode: normalizedCountryCode,
        active: true,
      },
    });

    res.status(201).json({
      success: true,
      message: `Successfully added ${cookieArray.length} cookies for email ${emailId} (${normalizedCountryCode})`,
      cookieId: newCookie.id,
      emailId: newCookie.emailId,
      countryCode: newCookie.countryCode,
      cookieCount: cookieArray.length,
      cookiePreview: cookieArray
        .slice(0, 3)
        .map(
          (c) => `${c.name || "unknown"}=${(c.value || "").substring(0, 20)}...`
        ),
      createdAt: newCookie.createdAt,
    });
  } catch (error) {
    console.error("Error in /add-cookies endpoint:", error);
    res.status(500).json({
      success: false,
      message: `Error adding cookies: ${error.message}`,
    });
  }
});

// API route to update cookies
router.put("/api/lex/update-cookies", async (req, res) => {
  try {
    const { emailId, cookiesData, countryCode } = req.body;
    const normalizedCountryCode = normalizeCountryCode(countryCode);

    const existingCookie = await prisma.lexReviewScraperCookies.findFirst({
      where: {
        emailId: emailId,
        countryCode: normalizedCountryCode,
      },
    });

    if (!existingCookie) {
      return res.status(404).json({
        success: false,
        message: `No cookies found for email ID ${emailId} and country ${normalizedCountryCode}`,
        suggestion: `Use POST /api/lex/add-cookies to create new cookies for this email and country combination`,
      });
    }

    let cookieArray = [];

    if (Array.isArray(cookiesData)) {
      // Direct array of cookie objects - store as it is
      cookieArray = cookiesData;
    } else if (typeof cookiesData === "string") {
      try {
        const parsedCookies = JSON.parse(cookiesData);
        if (Array.isArray(parsedCookies)) {
          cookieArray = parsedCookies;
        } else {
          return res.status(400).json({
            success: false,
            message: "Parsed cookies data must be an array",
          });
        }
      } catch (parseError) {
        return res.status(400).json({
          success: false,
          message: "Invalid JSON format for cookies data",
        });
      }
    } else {
      return res.status(400).json({
        success: false,
        message: "Cookie data must be provided as an array or string",
      });
    }

    if (cookieArray.length === 0) {
      return res.status(400).json({
        success: false,
        message: "No cookies found in the provided data",
      });
    }

    // Store the entire cookie array as JSON string
    const cookieJsonString = JSON.stringify(cookieArray);

    const updatedCookie = await prisma.lexReviewScraperCookies.update({
      where: { id: existingCookie.id },
      data: {
        cookieKey: cookieJsonString,
        cookieStatus: "ACTIVE",
        active: true,
        updatedAt: new Date(),
      },
    });

    res.status(200).json({
      success: true,
      message: `Updated ${cookieArray.length} cookies for email ${emailId} (${normalizedCountryCode})`,
      cookieId: updatedCookie.id,
      emailId: updatedCookie.emailId,
      countryCode: updatedCookie.countryCode,
      cookieCount: cookieArray.length,
      cookiePreview: cookieArray
        .slice(0, 3)
        .map(
          (c) => `${c.name || "unknown"}=${(c.value || "").substring(0, 20)}...`
        ),
      updatedAt: updatedCookie.updatedAt,
    });
  } catch (error) {
    console.error("Error in /update-cookies endpoint:", error);
    res.status(500).json({
      success: false,
      message: `Error updating cookies: ${error.message}`,
    });
  }
});

// GET /api/lex/cookies - Get all cookies with email, country code, cookies data, and status
router.get("/api/lex/cookies", async (req, res) => {
  try {
    const { status, countryCode, emailId, limit = 50 } = req.query;

    let whereClause = {};
    if (status) {
      whereClause.cookieStatus = status.toUpperCase();
    }
    if (countryCode && countryCode.trim() !== "") {
      whereClause.countryCode = countryCode.trim().toUpperCase();
    }
    if (emailId) {
      whereClause.emailId = {
        contains: emailId,
        mode: "insensitive",
      };
    }

    const cookieRecords = await prisma.lexReviewScraperCookies.findMany({
      where: whereClause,
      orderBy: {
        updatedAt: "desc",
      },
      take: parseInt(limit),
      select: {
        id: true,
        emailId: true,
        cookieKey: true,
        cookieStatus: true,
        countryCode: true,
        active: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    const formattedCookies = cookieRecords.map((record) => {
      let parsedCookies = [];
      let cookieCount = 0;
      let parseError = null;

      try {
        parsedCookies = JSON.parse(record.cookieKey);
        cookieCount = Array.isArray(parsedCookies) ? parsedCookies.length : 0;
      } catch (error) {
        parseError = "Failed to parse cookies";
        console.error(`Error parsing cookies for ${record.emailId}:`, error);
      }

      return {
        id: record.id,
        emailId: record.emailId,
        countryCode: record.countryCode,
        cookies: parseError ? null : parsedCookies,
        cookieCount: cookieCount,
        status: record.cookieStatus,
        active: record.active,
        parseError: parseError,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      };
    });

    const totalCount = await prisma.lexReviewScraperCookies.count({
      where: whereClause,
    });

    // Group by country for summary
    const countryStats = await prisma.lexReviewScraperCookies.groupBy({
      by: ["countryCode"],
      where: whereClause,
      _count: { id: true },
    });

    res.status(200).json({
      success: true,
      cookies: formattedCookies,
      count: formattedCookies.length,
      totalCount: totalCount,
      countryStats: countryStats.map((stat) => ({
        countryCode: stat.countryCode,
        count: stat._count.id,
      })),
      filters: {
        status: status || "all",
        countryCode: countryCode || "all",
        emailId: emailId || "all",
        limit: parseInt(limit),
      },
    });
  } catch (error) {
    console.error("Error in GET /api/lex/cookies endpoint:", error);
    res.status(500).json({
      success: false,
      message: `Error retrieving cookies: ${error.message}`,
    });
  }
});

// GET /api/lex/cookies/:emailId - Get cookies for a specific email (optionally filtered by country)
router.get("/api/lex/cookies/:emailId", async (req, res) => {
  try {
    const { emailId } = req.params;
    const { countryCode } = req.query;

    let whereClause = {
      emailId: emailId,
    };

    if (countryCode && countryCode.trim() !== "") {
      whereClause.countryCode = countryCode.trim().toUpperCase();
    }

    const cookieRecords = await prisma.lexReviewScraperCookies.findMany({
      where: whereClause,
      orderBy: {
        updatedAt: "desc",
      },
    });

    if (cookieRecords.length === 0) {
      return res.status(404).json({
        success: false,
        message: countryCode
          ? `No cookies found for email ID ${emailId} and country ${countryCode.toUpperCase()}`
          : `No cookies found for email ID ${emailId}`,
      });
    }

    const formattedRecords = cookieRecords.map((record) => {
      let cookieArray = [];
      try {
        cookieArray = JSON.parse(record.cookieKey);
      } catch (parseError) {
        console.error("Error parsing stored cookies:", parseError);
        cookieArray = [];
      }

      return {
        id: record.id,
        emailId: record.emailId,
        countryCode: record.countryCode,
        cookieStatus: record.cookieStatus,
        active: record.active,
        cookieCount: cookieArray.length,
        cookies: cookieArray,
        createdAt: record.createdAt,
        lastUpdated: record.updatedAt,
      };
    });

    res.status(200).json({
      success: true,
      emailId: emailId,
      records: formattedRecords,
      totalRecords: formattedRecords.length,
      countries: formattedRecords.map((r) => r.countryCode),
    });
  } catch (error) {
    console.error("Error in GET /cookies/:emailId endpoint:", error);
    res.status(500).json({
      success: false,
      message: `Error retrieving cookies: ${error.message}`,
    });
  }
});

// HELPER FUNCTIONS
function parseReviewsFilter(reviewsParam) {
  const match = reviewsParam.match(/^(gt|lt|gte|lte|eq)(\d+)$/);
  if (!match) return null;

  const [, operator, value] = match;
  const numValue = parseInt(value);

  switch (operator) {
    case "gt":
      return { gt: numValue };
    case "gte":
      return { gte: numValue };
    case "lt":
      return { lt: numValue };
    case "lte":
      return { lte: numValue };
    case "eq":
      return { equals: numValue };
    default:
      return null;
  }
}

// Helper function to normalize country code
function normalizeCountryCode(countryCode) {
  // Handle empty string, null, undefined, or whitespace-only strings
  if (
    !countryCode ||
    typeof countryCode !== "string" ||
    countryCode.trim() === ""
  ) {
    return "US";
  }
  return countryCode.trim().toUpperCase();
}

module.exports = router;
