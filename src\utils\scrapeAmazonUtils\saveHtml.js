const fs = require("fs");
const path = require("path");

async function saveHtml(htmlData, filename) {
      const directory = "html";
      const filePath = path.join(__dirname, directory, filename);
    try {
       await fs.promises.mkdir(path.join(__dirname, directory), {
         recursive: true,
       });
    await fs.promises.writeFile(filePath, htmlData); 
    console.log(`HTML data successfully saved to ${"searchPageData.html"}`);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error saving HTML data:", error);
  }
}


module.exports = saveHtml