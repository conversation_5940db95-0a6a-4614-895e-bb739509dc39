const prisma = require("../../database/prisma/getPrismaClient");

const countryMap = {
  com: "us",
  "co.uk": "uk",
  de: "de",
  ca: "ca",
  fr: "fr",
  in: "in",
};

const userNameMap = {
  us: "scraperapi.country_code=us.render=true.wait_for_selector=._cr-ratings-histogram_style_histogram-row-container__Vh7Di",
  uk: "scraperapi.country_code=uk.render=true.wait_for_selector=._cr-ratings-histogram_style_histogram-row-container__Vh7Di",
  de: "scraperapi.country_code=de.render=true.wait_for_selector=._cr-ratings-histogram_style_histogram-row-container__Vh7Di",
  fr: "scraperapi.country_code=fr.render=true.wait_for_selector=._cr-ratings-histogram_style_histogram-row-container__Vh7Di",
  in: "scraperapi.country_code=in.render=true.wait_for_selector=._cr-ratings-histogram_style_histogram-row-container__Vh7Di",
};

async function getProxyUsername(company_id) {
  // Extract the domain from the storefront URL
  const data = await prisma.company.findFirst({
    where: {
      id: company_id,
    },
  });
  if (!data) {
    return "scraperapi.country_code=de";
  }
  const url = data.storeFrontURL || data.productUrl || data.searchUrl;

  // Use a regex that matches the domain suffix (e.g., .com, .co.uk)
  const domainMatch = url.match(/amazon\.([a-z\.]+)(?:\/|$)/);
  if (!domainMatch) return "us"; // Default to 'us' if no match is found

  const domainSuffix = domainMatch[1]; // Extract just the suffix (e.g., 'co.uk')
  const country_code = countryMap[domainSuffix] || "us";
  // console.log(userNameMap[country_code] || "us");
  return userNameMap[country_code] || "us";
}

module.exports = getProxyUsername;

// Example usage
// getProxyUsername(2);
