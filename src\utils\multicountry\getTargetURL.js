const prisma = require("../../database/prisma/getPrismaClient");

async function getTargetURL(company_id) {
  // Extract the domain from the storefront URL
  const data = await prisma.company.findFirst({
    where: {
      id: company_id,
    },
  });
  if (!data) {
    return "https://www.amazon.com";
  }
  const url = data.storeFrontURL || data.productUrl || data.searchUrl;

  const domainMatch = url.match(/^https?:\/\/(www\.[^/]+)/);

  // Default to amazon.com if no domain is found
  const targetURL = domainMatch
    ? `https://${domainMatch[1]}`  
    : "https://www.amazon.com";
  // console.log({ url, targetURL });
  return targetURL;
}

module.exports = getTargetURL;

// Example usage
// const company_id = 2;
// getTargetURL(2);
