const prisma = require("../../database/prisma/getPrismaClient");
const csvtojson = require("csvtojson");
const Joi = require("joi");

const rowSchema = Joi.object({
  Seller: Joi.string().allow("").optional(),
  "Review ID": Joi.string().required(),
  "Review URL": Joi.string().uri().required(),
  "Brand Name": Joi.string().allow("").optional(),
  "ASIN": Joi.string().allow("").optional(),
}).unknown(true);

async function processLexCSV(jsonArray, job) {
  try {
    for (let i = 0; i < jsonArray.length; i++) {
      console.log("Processing row:", i + 1);
      await processJsonData(jsonArray[i], job);
    }
    console.log("CSV Parsing and Insertion complete");
  } catch (e) {
    console.log("Error in processLexCSV", e);
  }
}


async function processJsonData(row, job) {
  const { error } = rowSchema.validate(row, { allowUnknown: true });
  if (error) {
    console.warn("Invalid row, skipping:", error.message);
    return;
  }

  const reviewId = row["Review ID"];
  const reviewUrl = row["Review URL"];
  const brandName = row["Brand Name"];
  const asin = row["ASIN"];
  // Extract all Violation Tags
  const tags = Object.keys(row)
    .filter((key) => key.startsWith("Violation Tag") && row[key]?.trim())
    .map((key) => row[key].trim());

  if (tags.length === 0) {
    console.warn(
      `No valid violation tags found for review ID ${reviewId}, skipping`
    );
    return;
  }

  // Fetch matching tags from DB
  const matchedTags = await prisma.lexViolationTag.findMany({
    where: {
      name: {
        in: tags,
        mode: "insensitive",
      },
    },
  });

  let tagIds = [];
  let tagNames = [];

  if (matchedTags.length === 0) {
    console.warn(
      `No matching tags found in DB for review ID ${reviewId}, using default tag ID 1`
    );

    const defaultTag = await prisma.lexViolationTag.findUnique({
      where: { id: 1 },
    });

    if (!defaultTag) {
      console.warn("Default tag with ID 1 not found in DB, skipping row");
      return;
    }

    tagIds = [defaultTag.id];
    tagNames = [defaultTag.name];
  } else {
    tagIds = matchedTags.map((tag) => tag.id);
    tagNames = matchedTags.map((tag) => tag.name);
  }

  try {
    let review = null;
    if (reviewId) {
      review = await prisma.lexImageGenReview.findUnique({
        where: { reviewId },
      });
    }
    if (!review) {
      review = await prisma.lexImageGenReview.create({
        data: {
          reviewId,
          reviewUrl,
          violationTagId: tagIds,
          violationTag: tagNames,
          asin,
          brandName,
          status: "PENDING",
          inputData: row,
        },
      });
    }
    await prisma.lexImageGenOutputData.create({
      data: {
        revId: review?.id,
        reviewUrl: review?.reviewUrl,
        jobId: job.id,
        violationTagId: tagIds,
        violationTag: tagNames,
      },
    });
  } catch (e) {
    console.log("Error inserting review", e);
  }
}

module.exports = { processLexCSV };
