const prisma = require("../../database/prisma/getPrismaClient");

async function processAboutData(userPrompt, csvData, dataFlag) {
  try {
    let aboutData = await prisma.aboutData.findFirst({
      where: {
        companyId: csvData.companyId,
      },
    });

    if (!aboutData) {
      // Marking the All Data Flag as false
      // dataFlag[0] = dataFlag[0] && false;
      return { status: "pending" };
    }
    //TODO: Check for the char size then update this limit to 5000
    if (aboutData.aboutData && aboutData.aboutData !== "No Text Found") {
      userPrompt["Web Data"]["About Data"] = aboutData.aboutData.slice(0, 5000);
      csvData["aboutDataStatus"] = "Data Exists";
      userPrompt["Web Data"]["About URLs"] = aboutData.aboutUrls;
    }
    if (aboutData.homepageData && aboutData.homepageData !== "No Text Found") {
      if (!userPrompt["Web Data"]["About Data"]) {
        userPrompt["Web Data"]["Homepage Data"] = aboutData.homepageData.slice(
          0,
          5000
        );
        csvData["aboutDataStatus"] = "About Data Not Found";
      }
      if (aboutData.status !== "completed") {
        aboutData.status = "completed";
      }
      csvData["homepageDataStatus"] = "Data Exists";
    }
    return aboutData;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in processing About Data:", error);
    console.error("Error in processing About Data:", error);
  }
}

module.exports = processAboutData;
