const countryToTLD = {
  US: "com",
  UK: "co.uk",
  DE: "de",
  CA: "ca",
  FR: "fr",
  IN: "in",
};

const tldToCountry = Object.entries(countryToTLD).reduce(
  (acc, [country, tld]) => {
    acc[tld] = country.toLowerCase();
    return acc;
  },
  {}
);

/**
 * Get Amazon top-level domain (TLD) for a given country code.
 * @param {string} countryCode - e.g., "US", "UK"
 * @returns {string} - e.g., "com", "co.uk"
 */
function getAmazonTLD(countryCode = "US") {
  const upper = countryCode.toUpperCase();
  return countryToTLD[upper] || "com";
}

module.exports = {
  getAmazonTLD,
  countryToTLD,
  tldToCountry,
};
