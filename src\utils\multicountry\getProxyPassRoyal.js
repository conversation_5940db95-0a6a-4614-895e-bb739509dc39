const getStoreCountryCode = require("./getCountryCode");

const ipRoyalPass = "BN6c4dFPggNqqZi4";

async function getProxyRoyalPass(company_id) {
  // Extract the domain from the storefront URL
  const country_code = await getStoreCountryCode(company_id);
  // console.log(country_code || "us");
  return `${ipRoyalPass}_country-${country_code}`;
}

module.exports = getProxyRoyalPass;

// Example usage
// getProxyRoyalPass(690);
