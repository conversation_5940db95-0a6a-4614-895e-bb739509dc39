const NodeCache = require("node-cache");
const myCache = new NodeCache();
const prisma = require("../../database/prisma/getPrismaClient");

// Function to get data with caching
async function getFromCache(key) {
  // Try to get data from cache
  let value = myCache.get(key);
  if (value) {
    console.log("Cache hit for key:", key);
    const companyData = await prisma.outputData.findFirst({
      where: {
        id: value,
      },
    });
    return companyData;
  }
  return null;
}

async function cache(key, value) {
    myCache.set(key, value, 36000); // Cache for 10 hour
}



async function main() {
    clearCache();
  const key = "unique-key";
    let data = await getFromCache(key);
    if (!data) {
        data = { data: "some data" };
        await cache(key, data);
    }
    console.log(getFromCache(key));
}

// Function to clear the cache
function clearCache() {
    myCache.flushAll();
    console.log("Cache cleared successfully!");
  }

// main();
module.exports = { getFromCache, cache, clearCache };