/*
  Warnings:

  - A unique constraint covering the columns `[asin,countryCode]` on the table `LexASIN` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `asinId` to the `LexReview` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "LexJob" DROP CONSTRAINT "LexJob_asin_fkey";

-- DropForeignKey
ALTER TABLE "LexReview" DROP CONSTRAINT "LexReview_asin_fkey";

-- DropIndex
DROP INDEX "LexASIN_asin_key";

-- AlterTable
ALTER TABLE "LexReview" ADD COLUMN     "asinId" INTEGER NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "LexASIN_asin_countryCode_key" ON "LexASIN"("asin", "countryCode");

-- AddForeignKey
ALTER TABLE "LexReview" ADD CONSTRAINT "LexReview_asinId_fkey" FOREIGN KEY ("asinId") REFERENCES "LexASIN"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
