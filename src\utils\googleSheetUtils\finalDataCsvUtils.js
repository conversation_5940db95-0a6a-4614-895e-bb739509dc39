const prisma = require("../../database/prisma/getPrismaClient");
const converter = require("json-2-csv");

const lexFinalDataCsvFormatter = async (data) => {
    try {
        if (!Array.isArray(data) || data.length === 0) {
            return new Error("Invalid input data");
        }

        const finalData = await Promise.all(
            data.map(async (item) => {
                try {
                    const review = await prisma.lexImageGenReview.findUnique({
                        where: {
                            id: item.revId,
                        },
                    });

                    return {
                        "Review ID": review?.reviewId || "",
                        "Review URL": item?.reviewUrl || "",
                        "ASIN": item?.asin || "",
                        "Status": item?.status || "",
                        "Image URL": item?.imageUrl || "",
                        "Violation Tag": Array.isArray(item?.violationTag)
                            ? item.violationTag.join(', ')
                            : (item?.violationTag || ""),
                        "Brand Name": item?.brandName || "",
                        "Created At": item?.createdAt || "",
                        "Updated At": item?.updatedAt || "",
                    };
                } catch (error) {
                    console.error("Error processing item:", error);
                    return null;
                }
            })
        );

        // Filter out any null values from failed processing
        const validData = finalData.filter(item => item !== null);

        if (validData.length === 0) {
            return new Error("No valid data after processing");
        }

        return validData;
    } catch (error) {
        console.error("Error in [lexFinalDataCsvFormatter] formatting data:", error);
        return new Error("Error formatting data");
    }
}

module.exports = {
    lexFinalDataCsvFormatter
}