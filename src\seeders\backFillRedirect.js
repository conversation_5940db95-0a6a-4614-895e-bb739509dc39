const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

async function addArray(data, type, clientName, id, source) {
  try {
    console.log("Adding link:", data);

    // Check if the UUID already exists
    const existingEntry = await prisma.uTMSync.findUnique({
      where: {
        uuid: id,
        clientName
      },
    });

    if (existingEntry) {
      console.log(`Duplicate found: ID "${id}" already exists. Skipping.`);
      return;
    }

    // Insert the new entry into the uTMSync table
    await prisma.uTMSync.create({
      data: {
        uuid: id,
        url: data,
        type: type,
        clientName: clientName,
      },
    });

    console.log(`Added: ID "${id}" created for URL "${data}"`);

    // Fetch the current user data
    const user = await prisma.user.findUnique({
      where: { id: 1 }, // Replace with the appropriate user ID
      select: { testimonials: true }, // Only fetch the JSON field
    });

    if (user && user.testimonials) {
      // console.log("TESTI:", user.testimonials);

      // Ensure `user.testimonials` is a valid JSON string
      let testimonials;
      try {
        testimonials =
          typeof user.testimonials === "string"
            ? JSON.parse(user.testimonials)
            : user.testimonials;
      } catch (parseError) {
        console.error("Failed to parse testimonials JSON:", parseError.message);
        return;
      }

      testimonials.forEach((testimonial) => {
        if (testimonial.video === data) {
          testimonial.video = `https://www.equalcollective.com?utm_uuid=${id}&utm_content=${clientName}&utm_medium=${type}&utm_source=${source}`;
        }
        if (testimonial.photo === data) {
          testimonial.photo = `https://www.equalcollective.com?utm_uuid=${id}&utm_content=${clientName}&utm_medium=${type}&utm_source=${source}`;
        }
      });

      // Update the user table with the modified JSON
      await prisma.user.update({
        where: { id: 1 }, // Replace with the appropriate user ID
        data: { testimonials: JSON.stringify(testimonials) },
      });

      console.log("Updated testimonials in the user table.");
    }
  } catch (error) {
    console.log("Error adding UTM data:", error.message);
  }
}

async function backfillRedirect() {
  try {
    const clientId = 1; // You can change this to any client ID you want
    const client = await prisma.user.findFirst({
      where: { id: clientId },
      select: {
        ctaLink: true,
        testimonials: true,
        caseStudies: true,
        name: true,
      },
    });

    if (!client) {
      console.log(`Client with ID "${clientId}" not found.`);
      return;
    }

    let ctaCounter = 1; // Counter for ctaLink
    let testimonialVideoCounter = 1; // Counter for testimonial videos
    let testimonialPhotoCounter = 1; // Counter for testimonial photos

    // Add CTA Link
    if (client.ctaLink) {
      await addArray(
        client.ctaLink,
        "ctaLink",
        client.name,
        `ctaLink-${ctaCounter++}`,
        "pdf"
      );
    }

    // Add Testimonial Links
    if (client.testimonials) {
      let testimonials;
      try {
        testimonials =
          typeof client.testimonials === "string"
            ? JSON.parse(client.testimonials)
            : client.testimonials;
      } catch (parseError) {
        console.error("Failed to parse testimonials JSON:", parseError.message);
        return;
      }

      if (Array.isArray(testimonials)) {
        for (const testimonial of testimonials) {
          if (testimonial.video) {
            await addArray(
              testimonial.video,
              "testimonials",
              client.name,
              `testimonials-video-${testimonialVideoCounter++}`,
              "pdf"
            );
          }
          if (testimonial.photo) {
            await addArray(
              testimonial.photo,
              "testimonials",
              client.name,
              `testimonials-photo-${testimonialPhotoCounter++}`,
              "pdf"
            );
          }
        }
      } else {
        console.error("Testimonials is not an array:", testimonials);
      }
    }
  } catch (error) {
    console.log("ERROR:", error.message);
  }
}

backfillRedirect();
