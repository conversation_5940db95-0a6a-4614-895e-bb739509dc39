const {
  S3Client,
  PutO<PERSON>Command,
  HeadObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} = require("@aws-sdk/client-s3");
const { fromEnv } = require("@aws-sdk/credential-provider-env");
require("dotenv").config();

const bucketName = "storefront-pages";
const dataFolder = "jeff-storefront/"; // Adding the missing dataFolder variable

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: fromEnv(),
  signatureVersion: "v4",
});

/**
 * Sanitizes a filename to be compatible with S3 object keys
 * @param {string} filename - The original filename
 * @returns {string} - Sanitized filename
 */
function sanitizeFilename(filename) {
  // Replace characters that S3 doesn't support or might cause issues
  return filename
    .replace(/[^a-zA-Z0-9._-]/g, "_") // Replace special chars with underscore
    .replace(/_{2,}/g, "_"); // Replace multiple underscores with single
}

class S3Service {
  /**
   * Uploads any data to S3 (text, JSON, etc.)
   * @param {any} data - The data to upload (object, string, etc.)
   * @param {string} key - The key (filename) to use
   * @param {boolean} isJson - Whether to stringify the data as JSON
   * @param {Object} metadata - Optional metadata to store with the object
   * @returns {Promise<string>} - The S3 URL of the uploaded content
   */
  async uploadToS3(data, key, isJson = false, metadata = {}) {
    const sanitizedKey = sanitizeFilename(key);
    const fullKey = `${dataFolder}${sanitizedKey}`;

    let contentToUpload;
    let contentType;

    if (isJson) {
      // If it's JSON data, stringify it
      contentToUpload = typeof data === "string" ? data : JSON.stringify(data);
      contentType = "application/json";
    } else {
      // For any other data, convert to string if needed
      contentToUpload = typeof data === "string" ? data : String(data);
      contentType = "text/plain";
    }

    // Add timestamp metadata if not provided
    if (!metadata["scraped-timestamp"]) {
      metadata["scraped-timestamp"] = new Date().toISOString();
    }

    const params = {
      Bucket: bucketName,
      Key: fullKey,
      Body: contentToUpload,
      ContentType: contentType,
      Metadata: metadata,
    };

    try {
      await s3Client.send(new PutObjectCommand(params));
      return `https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/${fullKey}`;
    } catch (error) {
      console.error("Error uploading data to S3:", error);
      throw error;
    }
  }

  /**
   * Retrieves data from S3
   * @param {string} key - The key (filename) to retrieve
   * @param {boolean} parseJson - Whether to parse the retrieved data as JSON
   * @param {boolean} includeMetadata - Whether to include metadata in the response
   * @returns {Promise<any>} - The retrieved data (parsed if JSON) and metadata if requested
   */
  async getFromS3(key, parseJson = false, includeMetadata = false) {
    const sanitizedKey = sanitizeFilename(key);
    const fullKey = `${dataFolder}${sanitizedKey}`;

    const params = {
      Bucket: bucketName,
      Key: fullKey,
    };

    try {
      const response = await s3Client.send(new GetObjectCommand(params));
      const dataString = await streamToString(response.Body);

      let data;
      if (parseJson) {
        try {
          data = JSON.parse(dataString);
        } catch (parseError) {
          console.error("Error parsing JSON from S3:", parseError);
          data = dataString; // Return as string if parsing fails
        }
      } else {
        data = dataString;
      }

      if (includeMetadata) {
        return {
          data,
          metadata: response.Metadata || {},
        };
      }

      return data;
    } catch (error) {
      console.error("Error retrieving data from S3:", error);
      throw error;
    }
  }

  /**
   * Checks if an object exists in S3
   * @param {string} key - The key to check
   * @returns {Promise<boolean>} - Whether the object exists
   */
  async objectExists(key) {
    const sanitizedKey = sanitizeFilename(key);
    const fullKey = `${dataFolder}${sanitizedKey}`;

    const params = {
      Bucket: bucketName,
      Key: fullKey,
    };

    try {
      await s3Client.send(new HeadObjectCommand(params));
      return true;
    } catch (error) {
      if (error.name === "NotFound") {
        return false;
      }
      throw error;
    }
  }

  /**
   * Checks if an object exists and is still valid (not expired)
   * @param {string} key - The key to check
   * @param {number} ttlHours - Time-to-live in hours
   * @returns {Promise<boolean>} - Whether valid object exists
   */
  async objectIsValid(key, ttlHours = 24) {
    const sanitizedKey = sanitizeFilename(key);
    const fullKey = `${dataFolder}${sanitizedKey}`;

    const params = {
      Bucket: bucketName,
      Key: fullKey,
    };

    try {
      const response = await s3Client.send(new HeadObjectCommand(params));
      const metadata = response.Metadata || {};

      if (!metadata["scraped-timestamp"]) {
        // If no timestamp, consider it invalid
        return false;
      }

      // Check if the timestamp is within the TTL
      const scrapedTime = new Date(metadata["scraped-timestamp"]);
      const now = new Date();
      const ageHours = (now - scrapedTime) / (1000 * 60 * 60);

      return ageHours < ttlHours;
    } catch (error) {
      if (error.name === "NotFound") {
        return false;
      }
      console.error("Error checking validity of object:", error);
      throw error;
    }
  }

  /**
   * Deletes an object from S3
   * @param {string} key - The key to delete
   * @returns {Promise<void>}
   */
  async deleteFromS3(key) {
    const sanitizedKey = sanitizeFilename(key);
    const fullKey = `${dataFolder}${sanitizedKey}`;

    const params = {
      Bucket: bucketName,
      Key: fullKey,
    };

    try {
      await s3Client.send(new DeleteObjectCommand(params));
    } catch (error) {
      console.error("Error deleting from S3:", error);
      throw error;
    }
  }
}

/**
 * Helper function to convert a readable stream to a string
 * @param {ReadableStream} stream - The stream to convert
 * @returns {Promise<string>} - The string content
 */
function streamToString(stream) {
  return new Promise((resolve, reject) => {
    const chunks = [];
    stream.on("data", (chunk) => chunks.push(chunk));
    stream.on("error", reject);
    stream.on("end", () => resolve(Buffer.concat(chunks).toString("utf8")));
  });
}

module.exports = {
  S3Service: new S3Service(),
  sanitizeFilename,
};
