const express = require("express");
const prisma = require("../database/prisma/getPrismaClient"); // Adjust the path as necessary
const { userAuth } = require("../middlewares/jwt");
const multer = require("multer");
const converter = require("json-2-csv");
const upload = multer({ dest: "uploads/" });
const { processLexCSV } = require("../utils/lexUtils/lexCsvParser");
const {
  uploadImage,
  isValidS3Link,
  deleteImage,
} = require("../services/aws/s3");
const { closePages } = require("../utils/puppeteer/browserHelper");
const { addToQueue } = require("../utils/bull/bull");
const router = express.Router();
const csvtojson = require("csvtojson");

router.post(
  "/api/lex/add-violation-tag",
  userAuth,
  upload.single("tagImage"),
  async (req, res) => {
    try {
      const { name, description } = req.body;

      if (!name || !req.file) {
        return res.status(400).json({ message: "Name and image are required" });
      }

      // Check if tag with same name exists
      const existingTag = await prisma.lexViolationTag.findFirst({
        where: { name },
      });
      if (existingTag) {
        return res.status(400).json({ message: "Tag already exists" });
      }

      // Upload image to S3
      const imageBuffer = req.file.buffer;
      const imageName = `${name.toLowerCase().replace(/\s+/g, '-')}.png`;
      const folder = "violation_tags/";
      const bucketName = "eq-lex";

      const s3Response = await uploadImage(imageBuffer, imageName, folder, bucketName);
      console.log({ s3Response });
      const imageUrl = `https://${bucketName}.s3.ap-south-1.amazonaws.com/${folder}${imageName}`;

      // Create the tag in database
      const tag = await prisma.lexViolationTag.create({
        data: {
          name,
          imageUrl,
          description: description || "",
        },
      });

      res.status(201).json(tag);
    } catch (error) {
      console.log("Error adding violation tag:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  }
);

router.get("/api/lex-image-gen/jobs", userAuth, async (req, res) => {
  try {
    const jobs = await prisma.lexImageGenJob.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
    if (jobs.length === 0) {
      return res.status(404).json({ message: "No jobs found" });
    }
    res.status(200).json(jobs);
  } catch (error) {
    console.log("Error fetching Lex jobs:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});


router.post(
  "/api/lex-image-gen/jobs",
  userAuth,
  upload.single("csvFile"),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file provided" });
      }

      const csvFilePath = req.file.path;
      const originalFileName = req.file.originalname;

      // Parse entire CSV to JSON array
      const jsonArray = await csvtojson().fromFile(csvFilePath);

      const batchSize = 400;
      const totalBatches = Math.ceil(jsonArray.length / batchSize);

      for (let i = 0; i < totalBatches; i++) {
        const batchRows = jsonArray.slice(i * batchSize, (i + 1) * batchSize);

        const job = await prisma.lexImageGenJob.create({
          data: {
            name: `${originalFileName} - Batch ${i + 1}`,
            clientId: 1,
          },
        });
        // Process batch with existing processor (slightly modified to accept rows directly)
        await processLexCSV(batchRows, job);
        // Create a job for each batch
        await addToQueue(
          "lexImageGen",
          {
            originalFileName: `${originalFileName} - Batch ${i + 1}`,
            jobId: job.id,
            clientId: 1,
          },
          {
            targetQueue: "lex",
          }
        );
      }

      res.json({
        message: "File uploaded and jobs created successfully",
        totalBatches,
      });
    } catch (error) {
      console.error("Error processing CSV:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  }
);

router.get("/api/lex-image-gen/output/:id", userAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const job = await prisma.lexImageGenJob.findUnique({
      where: { id: parseInt(id) },
    });
    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }
    const outputData = await prisma.lexImageGenOutputData.findMany({
      where: {
        jobId: parseInt(id),
      },
    });
    if (!outputData.length) {
      return res.status(404).json({ error: "No data found for this job" });
    };

    const finalData = await Promise.all(
      outputData.map(async (item) => {
        const review = await prisma.lexImageGenReview.findUnique({
          where: {
            id: item.revId,
          },
        });

        return {
          reviewId: review?.reviewId || "",
          reviewUrl: item?.reviewUrl || "",
          asin: item?.asin || "",
          status: item?.status || "",
          imageUrl: item?.imageUrl || "",
          violationTag: item?.violationTag || "",
          brandName: item?.brandName || "",
          createdAt: item?.createdAt || "",
          updatedAt: item?.updatedAt || "",
        };
      })
    );

    const csvContent = converter.json2csv(finalData);
    const lexOutputFileName = `lex_job_data_${job.id}.csv`;
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${lexOutputFileName}`
    );
    res.setHeader("Content-Type", "text/csv");
    res.status(200).send(csvContent);
  } catch (error) {
    console.log("Error fetching Lex job:", error);
  }
});

router.get("/api/lex-image-gen/violation-tags", userAuth, async (req, res) => {
  try {
    const violationTags = await prisma.lexViolationTag.findMany();
    res.status(200).json(violationTags);
  } catch (error) {
    console.log("Error fetching Lex violation tags:", error);
  }
});

module.exports = router;
