/**
 * Lex Review Analysis using Enhanced Prompt Chains
 * This module provides a wrapper around the enhanced runPromptChain functionality
 * specifically designed for lex review analysis workflows.
 */

const { runLexAnalysisChain } = require("../spike/runPromptChain");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;

/**
 * Analyze Amazon reviews using the new prompt chain system
 * This is a drop-in replacement for the original analyzeAmazonReviewsWithCompliance function
 * @param {Array} reviewsArray - Array of review objects
 * @param {Object} options - Configuration options
 * @returns {Promise<Array>} Analysis results
 */
async function analyzeAmazonReviewsWithCompliance(reviewsArray, options = {}) {
  const analyses = [];
  
  const config = {
    debug: options.debug || false,
    temperature: options.temperature || 0.7,
    ...options
  };

  for (const [index, review] of reviewsArray.entries()) {
    try {
      if (config.debug) {
        console.log(`🔄 Processing review ${index + 1}/${reviewsArray.length}: ${review["Review ID"] || review.reviewID}`);
      }

      // Run the lex analysis chain
      const result = await runLexAnalysisChain(review, config);

      // Format result to match original function output structure
      const formattedResult = {
        reviewId: review["Review ID"],
        asin: review.ASIN,
        productTitle: review.productTitle,
        productLink: review.productLink,
        reviewTitle: review["Review Title"],
        reviewContent: review.reviewContent,
        reviewLink: review["Review URL"],
        reviewer: review.reviewer,
        reviewerLink: review.reviewerLink,
        reviewDate: review.reviewDate,
        rating: review.reviewScore,
        analysis: result.analysis,
        compliance: result.compliance || "", // Add compliance field for backward compatibility
        topViolations: {
          GuidelineViolation1: result.topViolations.guidelineViolation1,
          ConfidenceScore1: result.topViolations.confidenceScore1,
          Reason1: result.topViolations.reason1,
          GuidelineViolation2: result.topViolations.guidelineViolation2,
          ConfidenceScore2: result.topViolations.confidenceScore2,
          Reason2: result.topViolations.reason2,
          GuidelineViolation3: result.topViolations.guidelineViolation3,
          ConfidenceScore3: result.topViolations.confidenceScore3,
          Reason3: result.topViolations.reason3,
        },
        violation: result.violation,
        cost: result.cost,
        tokenUsage: result.tokenUsage
      };

      analyses.push(formattedResult);

      if (config.debug) {
        console.log(`✅ Completed review ${index + 1}. Cost: $${(result.cost || 0).toFixed(4)}`);
      }

    } catch (error) {
      console.error(`❌ Error analyzing review ${index + 1}:`, error.message);
      
      // Add error result to maintain array consistency
      analyses.push({
        reviewId: review["Review ID"],
        asin: review.ASIN,
        productTitle: review.productTitle,
        productLink: review.productLink,
        reviewTitle: review["Review Title"],
        reviewContent: review.reviewContent,
        reviewLink: review["Review URL"],
        reviewer: review.reviewer,
        reviewerLink: review.reviewerLink,
        reviewDate: review.reviewDate,
        rating: review.reviewScore,
        analysis: null,
        compliance: "",
        topViolations: {
          GuidelineViolation1: null,
          ConfidenceScore1: 0,
          Reason1: null,
          GuidelineViolation2: null,
          ConfidenceScore2: 0,
          Reason2: null,
          GuidelineViolation3: null,
          ConfidenceScore3: 0,
          Reason3: null,
        },
        violation: null,
        cost: 0,
        tokenUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
        error: error.message
      });
    }
  }

  return analyses;
}

/**
 * Analyze a single review using prompt chains
 * @param {Object} review - Single review object
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Analysis result
 */
async function analyzeSingleReview(review, options = {}) {
  try {
    const result = await runLexAnalysisChain(review, options);
    return result;
  } catch (error) {
    console.error("❌ Error analyzing single review:", error);
    throw error;
  }
}

/**
 * Write analysis results to a CSV file
 * @param {Array} results - The analysis results
 */
async function writeToCsv(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const csvWriter = createCsvWriter({
    path: `output_${timestamp}.csv`,
    header: [
      { id: "asin", title: "ASIN" },
      { id: "reviewId", title: "Review ID" },
      { id: "productLink", title: "Product Link" },
      { id: "reviewLink", title: "Review Link" },
      { id: "reviewContent", title: "Review Content" },
      { id: "prompt1Output", title: "Prompt 1 Output" },
      { id: "prompt2ViolationCode", title: "Prompt 2 Violation Code" },
      { id: "prompt2ConfidenceScore", title: "Prompt 2 Confidence Score" },
      { id: "prompt2Reason", title: "Prompt 2 Reason" },
      { id: "prompt3Output", title: "Prompt 3 Output" },
    ],
  });

  const records = results.map((result) => ({
    asin: result.asin,
    reviewId: result.reviewId,
    productLink: result.productLink,
    reviewLink: result.reviewLink,
    reviewContent: result.reviewContent || "",
    prompt1Output: result.analysis || "",
    prompt2ViolationCode: result.topViolations?.GuidelineViolation1 || "",
    prompt2ConfidenceScore: result.topViolations?.ConfidenceScore1 || 0,
    prompt2Reason: result.topViolations?.Reason1 || "",
    prompt3Output: result.violation || "",
  }));

  await csvWriter.writeRecords(records);
  console.log(`✅ CSV results saved to: output_${timestamp}.csv`);
}

module.exports = {
  analyzeAmazonReviewsWithCompliance,
  analyzeSingleReview,
  writeToCsv,
  runLexAnalysisChain
};
