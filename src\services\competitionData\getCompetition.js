const prisma = require("../../database/prisma/getPrismaClient");
const Sentry = require("@sentry/node");

const getTargetURL = require("../../utils/multicountry/getTargetURL.js");
const fetchCompAmazonData = require("./fetchCompAmazonData.js");
const fetchCompAboutData = require("./fetchCompAboutData.js");

async function getCompetition({
  amazonData,
  aboutData,
  competitorUrl,
  company_name,
  companyId,
  usage,
  clientId,
}) {
  try {
    console.log("Processing competition data for company:", company_name);
    console.log({ competitorUrl });
    let result = null;
    const amazonDataJSON = amazonData.data;
    const TARGET_URL = await getTargetURL(companyId);

    if (amazonDataJSON && amazonDataJSON.amazon_existence) {
      result = await fetchCompAmazonData(
        amazonDataJSON,
        company_name,
        companyId,
        usage,
        clientId,
        TARGET_URL,
        competitorUrl
      );
    } else if (
      aboutData &&
      (aboutData.aboutData !== "No Text Found" ||
        aboutData.homepageData !== "No Text Found")
    ) {
      result = await fetchCompAboutData(
        aboutData,
        company_name,
        companyId,
        usage,
        clientId,
        TARGET_URL
      );
    }

    if (result) {
      console.log("Storing Competition Data...");
      await storeCompetitionData(result);
    } else {
      console.log("No Comp data available");
    }

    return result;
  } catch (error) {
    Sentry.captureException("Error in competition data: " + error);
    return "ERROR IN COMPETITION:", error;
  }
}

async function storeCompetitionData(data) {
  try {
    const compData = await prisma.competitionData.findMany({
      where: {
        companyId: data.companyId,
      },
    });
    if (compData.length > 0) {
      await prisma.competitionData.updateMany({
        where: {
          id: compData[0].id,
        },
        data: data
      });
    } else {
      await prisma.competitionData.create({
        data: data,
      });
    }
  } catch (error) {
    console.log("Error Stack:", error.stack);
    Sentry.captureException("Error in storing competition data: " + error);
    console.error("Error in storing competition data:", error);
  }
}

module.exports = getCompetition;
