const express = require("express");
const router = express.Router();
const prisma = require("../database/prisma/getPrismaClient");

const {
  sendCalendlyReply,
  generateEmail,
} = require("../services/singleAuditSender/sendCalendlyReply");
const auditGenerator = require("../services/singleAuditSender/singleAuditGenerator");

router.post("/api/get-single-audit", async (req, res) => {
  try {
    console.log("Calendly Webhook Hit");
    console.log("Headers:", req.headers);
    console.log("Body:", req.body);

    const payload = req.body.payload;
    const calendlyUri = req.body.created_by;
    const calendlyId = calendlyUri.split("/").pop();
    const user = await prisma.user.findFirst({
      where: {
        calendlyId,
      },
    });

    // Step 1: Extract invitee email + product URL from Calendly payload
    const inviteeEmail = payload?.email;
    const inviteeName = payload?.name;
    const productUrl = payload.questions_and_answers.find(
      (qa) => qa.question === "Product URL?"
    )?.answer;

    console.log("Product URL:", productUrl);

    if (!inviteeEmail || !productUrl) {
      console.log("Missing email or product URL");
      return res.status(400).json({ error: "Missing email or product URL" });
    }

    // Step 2: Generate audit (your logic)

    const auditLink = await auditGenerator(productUrl, user.id);
    // const auditLink = "test.com";
    console.log("Generating Audit Mail");

    const email = generateEmail(auditLink, inviteeName);

    // Step 3: Email audit link
    await sendCalendlyReply({
      to: inviteeEmail,
      subject: email.subject,
      body: email.body,
      from: "<EMAIL>",
    });
    console.log("Sending Mail");

    res.status(200).json({ success: true, message: "Audit sent to user." });
  } catch (error) {
    console.error("Audit error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;
