# Cookie Usage Tracking Implementation Guide

## Overview
This implementation adds a `lastUsed` datetime column to the `LexReviewScraperCookies` table and implements least-recently-used (LRU) cookie selection across all workers. This ensures fair rotation of cookies and prevents overuse of any single cookie.

## Changes Made

### 1. Database Schema Changes
- **Added `lastUsed` column**: Tracks when each cookie was last used
- **Added indexes**: For efficient querying by `lastUsed` and composite filtering
- **Migration file**: `src/database/prisma/migrations/20250623000000_add_lastused_to_cookies/migration.sql`

### 2. Updated Workers

#### `lexJobCreationWorker.js`
- **Enhanced `getActiveCookies()`**: Now selects least recently used cookie
- **Added `markCookieAsUsed()`**: Updates `lastUsed` timestamp when cookie is selected
- **Improved logging**: Shows which cookie was selected and when it was last used

#### `lexReviewsWorker.js`
- **Updated `getActiveCookies()`**: Implements LRU selection logic
- **Added `markCookieAsUsed()`**: Marks cookie as used when selected
- **Country-aware selection**: Filters cookies by country code

#### `lexReviewsAPIWorker.js`
- **Updated `getActiveCookies()`**: Implements LRU selection logic
- **Added `markCookieAsUsed()`**: Marks cookie as used when selected
- **Country-aware selection**: Filters cookies by country code

### 3. Monitoring Tools
- **Cookie Usage Monitor**: `src/database/scripts/cookie_usage_monitor.js`
- **Comprehensive statistics**: Usage patterns, rotation health, next selection preview
- **Simulation tools**: Test cookie selection without actual usage
- **Reset capabilities**: Reset usage tracking for testing

## How It Works

### Cookie Selection Algorithm
1. **Filter**: Get active cookies for specific country
2. **Sort**: Order by `lastUsed` ASC (NULL values first), then `createdAt` ASC
3. **Select**: Take the first cookie (least recently used)
4. **Mark**: Update `lastUsed` timestamp immediately
5. **Return**: Provide cookie data to scraper

### Benefits
- **Fair rotation**: All cookies get used equally
- **Prevents overuse**: No single cookie gets hammered
- **Country-specific**: Proper cookie selection per region
- **Monitoring**: Track usage patterns and health
- **Debugging**: Clear logging of which cookie is selected

## Migration Steps

### Step 1: Apply Database Migration
```bash
# Option A: Use Prisma migrate
npx prisma migrate dev --name add_lastused_to_cookies

# Option B: Apply manually
psql -d your_database -f src/database/prisma/migrations/20250623000000_add_lastused_to_cookies/migration.sql
```

### Step 2: Restart Workers
All workers need to be restarted to use the new cookie selection logic:
```bash
# Restart your application/workers
pm2 restart all
# or
systemctl restart your-app
```

### Step 3: Monitor Cookie Usage
```bash
# Check usage statistics
node src/database/scripts/cookie_usage_monitor.js stats

# Check rotation health
node src/database/scripts/cookie_usage_monitor.js health

# Simulate cookie selection
node src/database/scripts/cookie_usage_monitor.js simulate US 5
```

## Monitoring Commands

### View Usage Statistics
```bash
node src/database/scripts/cookie_usage_monitor.js stats
```
Shows:
- Cookie counts by status and country
- Usage patterns with last used times
- Next cookie that would be selected

### Check Rotation Health
```bash
node src/database/scripts/cookie_usage_monitor.js health
```
Shows:
- Used vs never-used cookies
- Average time since last use
- Rotation balance assessment

### Simulate Cookie Selection
```bash
# Simulate 5 selections for US
node src/database/scripts/cookie_usage_monitor.js simulate US 5

# Simulate 3 selections for UK
node src/database/scripts/cookie_usage_monitor.js simulate UK 3
```

### Reset Cookie Usage
```bash
# Reset all cookies
node src/database/scripts/cookie_usage_monitor.js reset

# Reset specific cookie
node src/database/scripts/cookie_usage_monitor.js reset 123
```

## Expected Behavior

### Initial State
- All existing cookies have `lastUsed = NULL`
- NULL values are treated as "never used" and selected first
- Cookies are ordered by creation date as secondary sort

### After Usage
- Each cookie gets a `lastUsed` timestamp when selected
- Next selection will pick the cookie with oldest `lastUsed` time
- Fair rotation ensures all cookies get used before any cookie is reused

### Logging Output
```
🍪 Found 1 active cookie(s) for country US, selected cookie ID: 5 (last used: never)
🍪 Marked cookie 5 as used at 2025-06-23T12:34:56.789Z
```

## Troubleshooting

### Issue: No Cookies Selected
**Problem**: Workers can't find cookies for specific country
**Solution**: 
- Check if cookies have correct `countryCode` set
- Verify cookies are `active: true` and `cookieStatus: 'ACTIVE'`
- Use monitoring script to check cookie status

### Issue: Uneven Cookie Usage
**Problem**: Some cookies used more than others
**Solution**:
- Check if workers are properly calling `markCookieAsUsed()`
- Verify database indexes are created
- Reset usage tracking and monitor

### Issue: Performance Issues
**Problem**: Cookie selection is slow
**Solution**:
- Ensure indexes are created: `lastUsed`, `countryCode_active_cookieStatus`
- Check database query performance
- Consider limiting number of cookies per country

## Database Queries

### Manual Cookie Selection (for testing)
```sql
-- Get next cookie for US
SELECT id, emailId, lastUsed, createdAt 
FROM "LexReviewScraperCookies" 
WHERE active = true 
  AND "cookieStatus" = 'ACTIVE' 
  AND "countryCode" = 'US'
ORDER BY "lastUsed" ASC NULLS FIRST, "createdAt" ASC 
LIMIT 1;

-- Mark cookie as used
UPDATE "LexReviewScraperCookies" 
SET "lastUsed" = NOW(), "updatedAt" = NOW() 
WHERE id = 123;
```

### Check Usage Distribution
```sql
-- Usage statistics by country
SELECT 
  "countryCode",
  COUNT(*) as total_cookies,
  COUNT("lastUsed") as used_cookies,
  COUNT(*) - COUNT("lastUsed") as never_used,
  AVG(EXTRACT(EPOCH FROM (NOW() - "lastUsed"))/60) as avg_minutes_since_use
FROM "LexReviewScraperCookies" 
WHERE active = true AND "cookieStatus" = 'ACTIVE'
GROUP BY "countryCode";
```

## Best Practices

1. **Monitor Regularly**: Check cookie usage patterns weekly
2. **Balance Load**: Ensure you have enough cookies per country
3. **Clean Up**: Remove expired/inactive cookies regularly
4. **Test Changes**: Use simulation before deploying changes
5. **Log Analysis**: Monitor worker logs for cookie selection patterns

## Future Enhancements

- **Cookie Health Scoring**: Track success rates per cookie
- **Automatic Rotation**: Force rotation after X uses
- **Geographic Load Balancing**: Distribute load across regions
- **Usage Analytics**: Detailed reporting and alerting
