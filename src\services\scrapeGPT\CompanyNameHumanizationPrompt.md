I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and "humanise" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps -
Step 1: Read the company name
Step 2: Think is this how a human would write this company name in an email
Step 3: If yes then return it as it is. If no then move to step 4
Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below.
Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.


Points to remember -
1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc.
2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc.
3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc.
4. Make sure it doesn't contain anything in a bracket
5. It should not have emojis
6. It should not have company names that have all uppercase letters
7. Incases where there is a slash "/" and feels like there are 2 company names then pick one which is the most relevant one according to you.
8. In most cases a location won't make sense in a company name.
9. In most cases anything in brackets doesn't make sense in a company name. 


Example 1 -
User: Hola! Music
Assistant: Hola Music


Example 2 -
User: iSUPPLYUSA(COM) LLC
Assistant: isupplyusa


Example 3 -
User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands
Assistant: Velocity Snack
