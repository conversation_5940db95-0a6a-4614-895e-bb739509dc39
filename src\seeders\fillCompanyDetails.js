// Import the Prisma client
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const { TARGET_URL } = require("../services/scrapeAmazon/constant");

async function main() {
  // Get all outputData

  const outputData = await prisma.outputData.findMany();
  const totalLeads = outputData.length;
  let index = 0;

  // Loop through all the outputData
  for (const data of outputData) {
    // Update the slug of each data
    // await generateAuditPdf(data.slug);
    console.log(`Processing ${index + 1} of ${totalLeads}`);
    if (data.companyId) {
      const company = await prisma.company.findFirst({
        where: {
          id: data.companyId,
        },
      });
      const formatSearchKeyword = company.name
        .trim()
        .replace(/[^\w\s-]/g, "")
        .replace(/\s+/g, "+")
        .replace(/^-+|-+$/g, "");
      let searchUrl = `${TARGET_URL}/s?k=${formatSearchKeyword}`;
      await prisma.company.update({
        where: {
          id: company.id,
        },
        data: {
          searchUrl: company.name? searchUrl : "",
          productUrl: data.prospectDetails.bestSellingProductURL || "",
          storeFrontURL: data.prospectDetails.storeFrontURL || "",
        },
      });
    }
    index++;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
