function updateBulletPointReport(data, report) {
  if (!data.productData || !data.productData[0].bulletPoints) {
    return;
  }
  const bulletPoints = data.productData[0].bulletPoints;
  const totalCharacters = bulletPoints.TotalChars;
  const bulletPointCounts = bulletPoints.Points.length;
  const allCapsCheck = bulletPoints.isItAllCaps;
  const firstWordCapitalCheck = bulletPoints.Points.every(
    (point) => point.FirstWordCapital
  );
  const firstWordCapitalPoint = bulletPoints.Points.find(
    (point) => point.FirstWordCapital
  );
  const exampleImprovement = firstWordCapitalPoint
    ? getFormattedText(firstWordCapitalPoint.value)
    : "";
  // console.log("Bullet Points: ", bulletPoints);
  // console.log("First Word Capital Check: ", firstWordCapitalCheck);

  if (bulletPointCounts > 0) {
    // Less than 5 bullet points
    if (bulletPointCounts < 5) {
      report.push({
        DATA_POINT: "Bullet Points",
        PRIORITY: "Medium",
        Logic: "< 5 Bullet Points",
        PAIN_POINT: `You have ${bulletPointCounts} bullet points instead of 5.`,
        Improvements: [
          "You're not utilizing the full SEO potential of bullet points. Add at least 5 bullet points to do so.",
        ],
        Benefits: ["CVR ↑", "Visibility & SEO ↑", "Organic Rankings ↑"],
      });
    }
    // Total characters in bullet points less than 1000
    if (totalCharacters < 900) {
      report.push({
        DATA_POINT: "Bullet Points",
        PRIORITY: "Medium",
        Logic: "<1000 characters total",
        PAIN_POINT: `The total character length for bullet points is ${totalCharacters} which is less than it should be.`,
        Improvements: [
          "Add more information about the product & its features in the bullet points. Have at least 1000 characters in total to maximize SEO.",
        ],
        Benefits: ["Visibility & SEO ↑", "Organic Rankings ↑"],
      });
    }

    // Bullet points having more than 250 characters
    let longBulletPoints = bulletPoints.Points.map((point, index) =>
      point.NumberChars > 270 ? index + 1 : null
    ).filter((index) => index);
    let maxBulletPointIndex = Math.max(...longBulletPoints);
    // console.log("Long Bullet Points: ", longBulletPoints);
    if (longBulletPoints.length === bulletPoints.Points.length) {
      longBulletPoints = ["All"];
    }
    // const longBulletPointExample = bulletPoints.Points.f
    // console.log("Long Bullet Points: ", longBulletPoints);

    // Bullet points having less than 150 characters
    let shortBulletPoints = bulletPoints.Points.map((point, index) =>
      point.NumberChars < 130 ? index + 1 : null
    ).filter((index) => index);
    let minBulletPointIndex = Math.min(...shortBulletPoints);
    if (shortBulletPoints.length === bulletPoints.Points.length) {
      shortBulletPoints = ["All"];
    }

    if (
      longBulletPoints.length > shortBulletPoints.length &&
      longBulletPoints.length > 0
    ) {
      report.push({
        DATA_POINT: "Bullet Points",
        PRIORITY: "Medium",
        Logic: "Bullet points have more than 250 chars",
        PAIN_POINT: `Bullet Point${
          longBulletPoints.length > 1 ? "s" : ""
        }: ${longBulletPoints.join(", ")} ${
          longBulletPoints.length > 1 ? "have" : "has"
        } more than 250 characters.`,
        Improvements: [
          longBulletPoints[0] === "All"
            ? `You should decrease the length of each bullet point. Ideal length is 150-250 characters.`
            : `You should decrease the length of each bullet point. Ideal length is 150-250 characters. Bullet Point ${maxBulletPointIndex} has ${bulletPoints.Points[maxBulletPointIndex-1].NumberChars} characters. `,
        ],
        Benefits: ["CVR ↑", "Visibility & SEO ↑", "Organic Rankings ↑"],
      });
    } else if (shortBulletPoints.length > 0) {
      report.push({
        DATA_POINT: "Bullet Points",
        PRIORITY: "Medium",
        Logic: "Bullet points have less than 150 Chars",
        PAIN_POINT: `Bullet Point${
          shortBulletPoints.length > 1 ? "s" : ""
        }: ${shortBulletPoints.join(", ")} ${
          shortBulletPoints.length > 1 ? "have" : "has"
        } less than 150 characters.`,
        Improvements: [
          shortBulletPoints[0] === "All"
            ? `You should increase the length of each bullet point. The ideal length is 150-250 characters. They are all less than 150 characters.`
            : `You should increase the length of each bullet point. The ideal length is 150-250 characters.Bullet Point ${minBulletPointIndex} has ${
                bulletPoints.Points[minBulletPointIndex - 1].NumberChars
              } characters. `,
        ],
        Benefits: ["CVR ↑", "Visibility & SEO ↑", "Organic Rankings ↑"],
      });
    }

    // First word of each bullet point is capitalized
    // if (firstWordCapitalCheck) {
    //   report.push({
    //     DATA_POINT: "Bullet Points",
    //     PRIORITY: "Medium",
    //     Logic: "First word of bullet each points is capital = yes",
    //     PAIN_POINT: "The first word of all your bullet points are capitalized.",
    //     Improvements: [
    //       `Make these letters lowercase like - ${exampleImprovement}`,
    //     ],
    //     Benefits: ["CVR ↑"],
    //   });
    // }

    // Bullet points should not be in all caps
    if (allCapsCheck) {
      report.push({
        DATA_POINT: "Bullet Points",
        PRIORITY: "Medium",
        Logic: "The bullet points are in all caps",
        PAIN_POINT: "Your bullet points are in all capital letters",
        Improvements: [
          `You should change them to lowercase except the first letter of each bullet point like -` +
            `${exampleImprovement}`,
        ],
        Benefits: ["CVR ↑"],
      });
    }
  } else {
    report.push({
      DATA_POINT: "Bullet Points",
      PRIORITY: "Medium",
      Logic: "< 5 Bullet Points",
      PAIN_POINT: `You have no bullet points.`,
      Improvements: [
        "You need to utilize the full SEO potential of bullet points. Add at least 5 bullet points to do so.",
      ],
      Benefits: ["CVR ↑", "Visibility & SEO ↑", "Organic Rankings ↑"],
    });
  }
}

function getFormattedText(text) {
  // console.log("Text: ", text);

  // Split the text into words
  let words = text.split(" ").slice(0,5);

  // Find the first word that contains a letter and capitalize the first letter found
  for (let i = 0; i < words.length; i++) {
    let word = words[i];
    for (let j = 0; j < word.length; j++) {
      if (/^[a-zA-Z]$/.test(word.charAt(j))) {
        words[i] =
          word.slice(0, j) +
          word.charAt(j).toUpperCase() +
          word.slice(j + 1).toLowerCase();
        break;
      }
    }
    // if (words[i] !== word) {
    //   break;
    // }
  }

  // Join the words back into a single string and add "..."
  text = words.join(" ") + " ...";

  // console.log("Formatted Text: ", text);
  return text;
}

module.exports = updateBulletPointReport;
