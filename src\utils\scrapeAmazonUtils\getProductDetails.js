const cheerio = require("cheerio");
const fs = require("fs");

const { selectors } = require("../../services/scrapeAmazon/selectors");
const { getRating } = require("./getRating");
const getReviews = require("./getReviews");
const getCategoryAndRank = require("./getCategoryAndRank");
const getStoreFront = require("./getStoreFront");
const { MAX_NO_OF_REVIEWS } = require("../../services/scrapeAmazon/constant");
const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");
const getBulletPoints = require("./getBulletPoints");
const getBrandNameFromProductPage = require("./getBrandNameFromProductPage");

async function getProductDetails(htmlData, company_name, url) {
  const $ = cheerio.load(htmlData);

  const titleElement = $(selectors.nameElement);
  if (titleElement.length === 0) {
    return null;
  }
  //  ----Taking and Saving Screen Shots ----
  // const screenshots = await getAmazonScreenshots(url, `${company_name}`);
  // console.log(screenshots);

  //  ---- Product Title ----
  const title = $(selectors.nameElement).first().text().trim();
  const titleNumOfChars = title.length;
  const titleApprox200Chars = titleNumOfChars >= 180 && titleNumOfChars <= 220;
  const titleUnder150Chars = titleNumOfChars <= 150;
  const productTitle = {
    value: title,
    numOfChars: titleNumOfChars,
    titleApprox200Chars: titleApprox200Chars,
    titleUnder150Chars: titleUnder150Chars,
    // titleImage: screenshots?.titleImagURL,
  };

  // ---- Bullet Points ----
  const bulletPoints = getBulletPoints($);

  // ---- Brand Story -----

  const brandStory = $(".apm-brand-story-background-image img");
  const brandStoryImgArray = brandStory
    .map((index, element) => {
      return $(element).attr("data-src"); // Extracts the data-src attribute
    })
    .get(); // Convert the Cheerio object to a plain array
  const brandStoryExtraImages = $(".apm-brand-story-image-cell img");

  const brandStoryExtraImgArray = brandStoryExtraImages
    .map((index, element) => {
      return $(element).attr("data-src"); // Extracts the data-src attribute
    })
    .get();
  const finalBrandStoryArray = brandStoryImgArray.concat(
    brandStoryExtraImgArray
  );
  const brandStoryPresent = finalBrandStoryArray.length > 0;
  const brandStoryObj = {
    brandStoryPresent,
    finalBrandStoryArray,
    // imageURl: screenshots.brandStoryURL,
  };
  // console.log(finalBrandStoryArray);

  // --- A+ Content ----

  const aplusContentPresent = $("#aplus").length > 0;
  let allImagesHaveAltText = true;
  let premiumAPlusPresent = false;

  if (aplusContentPresent) {
    // Get all img tags within the aplus section
    $("#aplus img").each((index, element) => {
      const altText = $(element).attr("alt");
      if (!altText) {
        allImagesHaveAltText = false;
      }
      const premiumAPlusElement = $(element).find(".aplus-premium");
      if (premiumAPlusElement) {
        premiumAPlusPresent = true;
      }
    });
  }
  if (!aplusContentPresent) {
    allImagesHaveAltText = false;
  }

  // const aplusImageElements = $("#aplus .aplus-v2.desktop.celwidget img");
  // // const aplusVideoElements = $("#aplus .aplus-v2.desktop.celwidget video");
  // const aplusImages = aplusImageElements
  //   .map((_, img) => {
  //     const thumbnailUrl = $(img).attr("data-src");

  //     return thumbnailUrl;
  //   })
  //   .get();

  // console.log(fullImageLinks);

  // ---- Text Description ----
  let description = $(selectors.descriptionElement).text().trim() || "N/A";
  if (!description || description === "N/A") {
    description = $('div[data-expanded="false"].a-expander-content')
      .first()
      .text()
      .trim();
  }
  const textDescriptionElement = $("#productDescription").text().trim();
  const textDescriptionChars = textDescriptionElement.length;
  const textDescription = {
    value: textDescriptionElement,
    numOfChars: textDescriptionChars,
  };
  const AplusContent = aplusContentPresent
    ? {
        aplusContentPresent,
        premiumAPlusPresent,
        allImagesHaveAltText,
        // aplusImages,
        brandStory: brandStoryObj.brandStoryPresent ? brandStoryObj : false,
      }
    : false;

  // -------Product Images/videos-------

  const noOfImagesRaw = $("#imageBlock .a-unordered-list img").length;
  let noOfVideos = 0;
  // noOfVideos = parseInt($(".video-count").text().split(" ")[0] || 0);
  const videoCountElement = $(".video-count");
  //videoCountElementv2 is for rare case when it doesnt have ".video-count" selector, it is yet to fix bcz we not getting this selector by proxy yet(tho it appear while rending in browser)
  const videoCountElementv2 = $("#altImages .videoThumbnail");
  // console.log(`VideoEle2: ${videoCountElementv2.length}`);
  if (videoCountElement) {
    if (videoCountElement.length > 0) {
      const videoCountText = videoCountElement.text().trim();
      // console.log(`videoCountTextElement1: ${videoCountText}`)
      if (videoCountText.toLowerCase() === "video") {
        noOfVideos = 1;
      } else {
        noOfVideos = parseInt(videoCountText.split(" ")[0]) || 0;
      }
  }
}
  else if(videoCountElementv2){
    const videoCountText = videoCountElement.text().trim();
    // console.log(`videoCountTextElement2: ${videoCountText}`)
    noOfVideos = 1;
  }

  console.log(`Number of videos: ${noOfVideos}`);

  const noOfImages = Math.abs(noOfImagesRaw - 2 - (noOfVideos ? 1 : 0));
  // const noOfImage = $("#altImages .imageThumbnail");
  // const noOfImages = noOfImage.length;

  const images = {
    noOfImages,
    noOfVideos,
    noOfImagesRaw,
  };

  const productMainImage = $(
    '#main-image-container li[data-csa-c-action="image-block-main-image-hover"] img'
  ).attr("src");

  // Replace your previous image code with this
  // const imageElements = $("#imageBlock .a-unordered-list img");
  // const secondaryImages = imageElements
  //   .map((_, img) => {
  //     const thumbnailUrl =
  //       $(img).attr("src") || $(img).attr("data-a-dynamic-image");

  //     // Check if it's a JSON format
  //     if (thumbnailUrl && thumbnailUrl.startsWith("{")) {
  //       // Parse JSON string for data-a-dynamic-image attribute
  //       const parsedData = JSON.parse(thumbnailUrl);
  //       return Object.keys(parsedData)[0]; // First key is the largest image URL
  //     }

  //     // Attempt to replace thumbnail size parameter for larger image
  //     return thumbnailUrl ? thumbnailUrl.replace(/_.*?_\.jpg$/, ".jpg") : null;
  //   })
  //   .get()
  //   // Filter out small or placeholder images
  //   .filter(
  //     (url) =>
  //       url && !url.includes("transparent-pixel") && !url.endsWith(".gif")
  //   );

  // ---- Ratings/Reviews ----

  const rating = getRating($, selectors.ratingElement);
  const review = getReviews($, MAX_NO_OF_REVIEWS);

  // ---- Price/Sales -----
  const priceString = $(selectors.priceElement).first().text().trim();
  const price = parseFloat(priceString.replace(/[^\d.-]/g, "")) || 0;
  const salesString = $(selectors.lastMonthSale).text().trim() || "N/A";
  let sales = 0;
  if (salesString !== "N/A") {
    const numberMatch = salesString.match(/(\d+(\.\d+)?)([K]?)\+/);
    if (numberMatch) {
      let number = parseFloat(numberMatch[1]);
      const unit = numberMatch[3];
      if (unit === "K") {
        number *= 1000;
      }
      // Add a random number between 1 and 9
      sales = Math.floor(number) + Math.floor(Math.random() * 9) + 1;
    }
  }

  // ----Misc -----

  const outOfStock = $(selectors.outOfStock).length ? true : false;
  const categoryAndRank = getCategoryAndRank($);
  const store = getStoreFront($);
  const brandName = await getBrandNameFromProductPage($);

  return {
    url,
    company_name,
    brandName,
    productTitle,
    bulletPoints,
    textDescription,
    AplusContent,
    title: title,
    // screenshots,
    // productMainImage,
    // secondaryImages,
    description: description,
    price: price,
    priceString: priceString,
    rating: rating,
    sales: sales,
    images,
    // aplusContent: aplusContent,
    // productVideo: productVideo,
    outOfStock,
    categoryAndRank: categoryAndRank,
    store: store,
    review,
  };
}


module.exports = { getProductDetails };

async function getProductData() {
  const clientId = 1;
  const url = "https://www.amazon.com/dp/B0DGZRG39D"
  // const url = "https://www.amazon.com/dp/B0B6D4S1MR"
  const htmlData = await getHtmlByProxy(url, clientId);
  // fs.writeFileSync("product.html", htmlData);
  const company_name = "SENEZER";
  const data = await getProductDetails(htmlData, company_name, url);
  fs.writeFileSync("output.json", JSON.stringify(data));
  console.log("------done-----");
  console.log({ data });
}
// getProductData();
