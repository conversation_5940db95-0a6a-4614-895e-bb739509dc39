const { selectors } = require("../../services/scrapeAmazon/selectors");


async function getBrandNameFromProductPage($) {
    selectors
  //takes product asin and return the brand;
  let brand = "";
  brand = $(selectors.brandName)
    .text()
    .replace("Brand", "")
    .trim()
    .toLowerCase();

  if (brand == "") {
    const storeName = $("#bylineInfo")
      .text()
      .replace("Visit the", "")
      .replace("Store", "")
      .trim();
    brand = storeName;
  }
  if (brand == "") {
    const manufacturer = $('span:contains("Manufacturer")')
      .next("span")
      .text()
      .trim();
    brand = manufacturer.toLowerCase();
  }
  console.log({ brand });
  return brand;
}

module.exports = getBrandNameFromProductPage;
