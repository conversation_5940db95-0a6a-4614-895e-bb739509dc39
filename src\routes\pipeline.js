const express = require("express");
const Sentry = require("@sentry/node");
const { adminAuth } = require("../middlewares/jwt");
const multer = require("multer");
const Joi = require("joi");
const {
  uploadImage,
  isValidS3Link,
  deleteImage,
} = require("../services/aws/s3");
const { generateSlug } = require("../utils/generateSlug");
const { getKeywordsForAsins } = require("../services/jungleScoutData/process");
const getRedirectUrl = require("../utils/utmHelpers/getRedirectUrl");
const utmCounter = require("../utils/utmHelpers/utmCounter");
const getRedirectUrlwithUUId = require("../utils/utmHelpers/getRedirectUrlwithUUID");
const upload = multer();

const router = express.Router();

// Route to upload image
router.post(
  "/api/upload-image",
  adminAuth,
  upload.single("image"),
  async (req, res) => {
    try {
      if (req.file) {
        const imageBuffer = req.file.buffer;
        const imageName = generateSlug(req.body.imageName);
        const folder = req.body.folderName;
        console.log({ imageName, folder });
        const s3Response = await uploadImage(imageBuffer, imageName, folder);
        const imageUrl = `https://eq--assets.s3.ap-south-1.amazonaws.com/${folder}${imageName}`;

        if (await isValidS3Link(imageUrl)) {
          res.status(200).json({
            imageUrl,
            s3Response,
          });
        }
      } else {
        res.status(400).json({ message: "Image not uploaded" });
      }
    } catch (error) {
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }
);

// Route to delete image
router.post("/api/delete-image", adminAuth, async (req, res) => {
  try {
    const imageUrl = req.body.imageUrl;
    if (!imageUrl) {
      return res.status(400).json({ message: "Image name is required" });
    }
    const isValid = await isValidS3Link(imageUrl);

    // Extract the folder name and image name
    const urlParts = imageUrl.split("/");
    const imageName = urlParts[urlParts.length - 1]; // last part is the image name
    const folder = `${urlParts.slice(3, -1).join("/")}/`; // second to last part is the folder name

    if (isValid) {
      const s3Response = await deleteImage(imageName, folder);
      res.status(200).json({
        s3Response,
      });
    } else {
      return res.status(400).json({ message: "Not a Valid S3 Link", imageUrl });
    }
  } catch (error) {
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
});

// health check api
router.get("/api/health", (req, res) => {
  res.status(200).json({ message: "Server is running" });
});

router.post("/api/get-keywords", async (req, res) => {
  try {
    console.log("Getting keywords for asins");
    const { asins, marketplace, sort, pageSize, pageCursor } = req.body;

    // Call the getKeywordsForAsins function
    const response = await getKeywordsForAsins(
      asins,
      marketplace,
      sort,
      pageSize,
      pageCursor
    );

    console.log({ response });
    // return response;
    // // Send the response back to the client
    res.status(200).json(response);
  } catch (error) {
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
});

router.get("/api/redirect", async (req, res) => {
  const {
    n_uuid,
    utm_uuid,
    utm_content,
    utm_term,
    utm_medium,
    utm_source,
    utm_campaign,
    utm_email,
    utm_sellerId
  } = req.query;
  console.log("Request Query:", req.query); // Logs the query parameters

  if (n_uuid) {
    console.log("New System");
    let generatedUrl = await getRedirectUrlwithUUId(n_uuid);
    if (!generatedUrl || !generatedUrl.url) {
      return res.status(404).json({ error: "URL not found" });
    }
    if (!generatedUrl.campaign) {
      generatedUrl.campaign = utm_campaign;
    }
    if (!generatedUrl.email) {
      generatedUrl.email = utm_email;
    }
    if (!generatedUrl.sellerId) {
      generatedUrl.sellerId = utm_term || utm_sellerId;
    }
    // utmCounter({
    //   n_uuid: n_uuid,
    //   clientName: generatedUrl.clientName,
    //   sellerId: generatedUrl.sellerId,
    //   type: generatedUrl.type,
    //   source: utm_source,
    //   campaign: generatedUrl.campaign || utm_campaign,
    // });

    return res.status(200).json({
      message: "Redirect URL generated successfully",
      redirectUrl: generatedUrl,
    });
  } else {
    const queryParams = req.query;
    const redirectSchema = Joi.object({
      utm_content: Joi.string().messages({
        "any.required": "'clientName' is required",
        "string.empty": "'clientName' cannot be empty",
      }),
      utm_term: Joi.string().messages({
        "any.required": "'sellerId' is required",
        "string.empty": "'sellerId' cannot be empty",
      }),
      utm_medium: Joi.string().messages({
        "any.required": "'type' is required",
        "string.empty": "'type' cannot be empty",
      }),
      utm_uuid: Joi.string().messages({
        "any.required": "'uuid' is required",
        "string.empty": "'uuid' cannot be empty",
      }),
      utm_campaign: Joi.string().optional(), // Optional field
      utm_source: Joi.string().optional(), // Optional field
      n_uuid: Joi.string().optional(),
    });

    const { error, value } = redirectSchema.validate(queryParams);

    if (error) {
      console.error("Validation Error:", error.details[0].message);
      return res.status(400).json({ error: error.details[0].message });
    }

    // Successful validation
    // console.log("Validation Success:", value);

    const generatedUrl = await getRedirectUrl(
      utm_uuid,
      utm_content,
      utm_term,
      utm_medium,
      utm_campaign
    );
    if (!generatedUrl || !generatedUrl.url) {
      return res.status(404).json({ error: "URL not found" });
    }
    // utmCounter({
    //   uuid: utm_uuid,
    //   clientName: utm_content,
    //   sellerId: utm_term,
    //   type: utm_medium,
    //   source: utm_source,
    //   campaign: utm_campaign,
    // });
    // console.log("Redirecting to:", generatedUrl);
    // res.redirect(302, generatedUrl.url);

    res.status(200).json({
      message: "Redirect URL generated successfully",
      redirectUrl: generatedUrl,
    });
  }
});

module.exports = router;
