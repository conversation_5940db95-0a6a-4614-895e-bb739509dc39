const nodemailer = require("nodemailer");

const mailList = {
  admins: ["<EMAIL>", "<EMAIL>"],
  manager: [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ],
  devs: ["<EMAIL>", "<EMAIL>"],
  from: "<EMAIL>",
};
// Create Nodemailer transporter using Amazon SES
const transporter = nodemailer.createTransport({
  host: "email-smtp.ap-south-1.amazonaws.com", // SES SMTP endpoint for your region
  port: 465, // SMTP port (Amazon SES uses 465 for SMTP over SSL)
  secure: true, // true for 465, false for other ports
  auth: {
    user: "AKIA4MTWJN5KYH37COXK", // Your SES SMTP username
    pass: "BDcXHtrGj8r0cfsXLabfC7bG2qHwLQmU2/Teuca83nWt", // Your SES SMTP password
  },
});

let lastProxyError = new Date(1970, 0, 1); // January 1, 1970
let lastGPTCreditError = new Date(1970, 11, 31); // December 31, 1970
let lastJungleScoutCreditError = new Date(1970, 11, 31); // December 31, 1970

function sendEmail(mailOptions) {
  console.log(mailOptions.to);
  if (!mailOptions.to) {
    console.error("No recipients defined in mailOptions:", mailOptions);
    return;
  }
  transporter.sendMail(mailOptions, (error, info) => {
    if (error) {
      console.error("Error occurred:", error);
    } else {
      console.log("Email sent successfully!");
      console.log("Message ID:", info.messageId);
    }
  });
}

/**
 * 
 * to - Comma separated list or an array of recipients 
 *      e-mail addresses that will appear on the To: field
 * 
 * cc - Comma separated list or an array of recipients 
 *      email addresses that will appear on the Cc: field option 
 */

const sendErrorEmail = function (option, { html = " " } = {}) {
  let recipients = mailList.admins;
  let subject, text;
  let cc = mailList.manager;
  let from = mailList.from;

  switch (option) {
    case "proxyError":
      if (new Date() - lastProxyError > 1000 * 60 * 60) {
        console.log("Sending email for option:", option);
        subject = "Proxy Error";
        text = `Hey, There is an error in proxy. Please follow these steps to resolve the issue:
          1. Go to the proxy provider's website (https://dashboard.scraperapi.com/).
          2. Create a new account or log in to your existing account.
          3. Generate new proxy credentials and update them in the application.
          4. If the issue persists, please check the logs for more details.\n\nRegards,\nBrandbuddy`;
        // sendEmail({
        //   from,
        //   to: recipients,
        //   subject,
        //   text,
        //   cc,
        // });
        lastProxyError = new Date();
      }
      break;
    case "gptCreditError":
      if (new Date() - lastGPTCreditError > 1000 * 60 * 60) {
        console.log("Sending email for option:", option);
        subject = "GPT Credit Error";
        text = `Hey, There is an error in GPT credit. Please follow these steps to resolve the issue:
          1. Go to the GPT billing section (https://platform.openai.com/settings/organization/billing/overview).
          2. Add more credits to your account.
          3. If the issue persists, please check the logs for more details. \n\nRegards,\nBrandbuddy`;
        // sendEmail({
        //   from,
        //   to: recipients,
        //   subject,
        //   text,
        //   cc,
        // });
        lastGPTCreditError = new Date();
      }
      break;
    case "jungleScoutApiError":
      if (new Date() - lastJungleScoutCreditError > 1000 * 60 * 60) {
        console.log("Sending email for option:", option);
        subject = "Jungle Scout Credit Error";
        text = `Hey, There is an error in Jungle Scout credit. Please follow these steps to resolve the issue:
          1. Go to the JS billing section (https://www.junglescout.com/t/pricing/).
          2. Add more credits to your account.
          3. If the issue persists, please check the logs for more details.\n\nRegards,\nBrandbuddy`;
        // sendEmail({
        //   from,
        //   to: recipients,
        //   cc,
        //   subject,
        //   text,
        // });
        lastJungleScoutCreditError = new Date();
      }
      break;
    case "reportReadyAlert":
      console.log("Sending email for option:", option);
      recipients += ",<EMAIL>";
      subject = "Report Ready";
      text = `The job is complete. Please check the report on our website [https://www.equalcollective.com/].
              Let me know if you have any questions or feedback. \n\nRegards,\nBrandbuddy`;

      // sendEmail({
      //   from,
      //   to: recipients,
      //   subject,
      //   text,
      //   cc,
      // });
      break;
    case "jobTimeOut":
      console.log("Sending email for option:", option);
      // sendEmail({
      //   from,
      //   to: mailList.manager.concat(mailList.devs),
      //   subject: "Jobs Taking More Than 6 Hours Ago",
      //   html,
      // });
      break;
    default:
      console.log("Invalid option");
  }
};

module.exports = { sendErrorEmail };
