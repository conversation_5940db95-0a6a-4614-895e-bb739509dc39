const cheerio = require("cheerio");
const { selectors } = require("../../services/scrapeAmazon/selectors");

async function isSponsoredCheck({ htmlData, keyword }) {
  try {
    const $ = cheerio.load(htmlData);
    let isSponsored = 0;

    $(selectors.productLink).each((_, container) => {
      const title = $(container)
        .find(selectors.productTitle)
        .text()
        .toLowerCase();
      const titleCheck = title.includes(keyword);

      const header = $(container)
        .find(selectors.brandHeader)
        .text()
        .toLowerCase();
      const headerList = header ? header.split(" ") : false;
      const headerCheck = header
        ? headerList.some((keyword) => header.includes(keyword))
        : false;

      if (headerCheck || titleCheck) {
        const sponsorCheck = $(container).find(
          'span.a-color-secondary:contains("Sponsored")'
        );

        // console.log(sponsorCheck.length);

        // console.log({title,header})

        if (sponsorCheck.length) {
          isSponsored++;
        }
      }
    });
    return isSponsored ? true : false;
  } catch (e) {
    console.log("Error in isSponsoredCheck", e);
  }
}

module.exports = isSponsoredCheck;
