const prisma = require("../database/prisma/getPrismaClient");
const cron = require("node-cron");
const { closeBrowsers } = require("../utils/puppeteer/browserHelper");
const reloadServer = require("./reloadServer");
const { JobStatus } = require("@prisma/client");
const { generateLexImage } = require("../utils/lexUtils/generateLexImage");
require("dotenv").config();

// Add a global flag to track if a job is in progress
let isProcessing = false;

// Only start the worker if this is the Main server
// if (process.env.SERVER_ID === "Main") {
//   reviewUpdater();
//   cron.schedule("* * * * *", reviewUpdater);
// } else {
//   console.log("Not running Lex Image Generator Worker - Not Main server");
// }

async function reviewUpdater() {
  console.log("Processing Lex Review Image Generator Job");
  // If already processing a job, don't start another one
  if (isProcessing) {
    console.log("A job is already being processed, skipping this cycle");
    return;
  }

  try {
    isProcessing = true;

    // Find the oldest queued job
    const jobs = await prisma.lexImageGenJob.findMany({
      where: { status: JobStatus.PENDING },
      orderBy: { createdAt: "asc" },
    });

    if (jobs.length === 0) {
      isProcessing = false;
      return; // No jobs to process
    }

    for (const job of jobs) {
      console.log("Processing Job CSV data for job id:", job.id);
      // Update job status to 'processing'
      await prisma.lexImageGenJob.update({
        where: { id: job.id },
        data: {
          status: JobStatus.IN_PROGRESS,
        },
      });
      await processJob(job);
      await closeBrowsers();
      await reloadServer();
    }
  } catch (error) {
    console.error("Error in job processor:", error);
    prisma.lexImageGenJob
      .update({
        where: { id: job.id },
        data: {
          status: JobStatus.FAILED,
        },
      })
      .catch((err) => {
        console.error("Error updating job status:", err.message);
      });
  } finally {
    // Make sure to reset the flag when done, regardless of success or failure
    isProcessing = false;
  }
}

async function processLexImageGenJob(jobId) {
  try {
    await prisma.lexImageGenJob.update({
      where: {
        id: jobId,
      },
      data: {
        status: JobStatus.IN_PROGRESS,
      },
    });
    const pendingReviews = await prisma.lexImageGenOutputData.findMany({
      where: {
        jobId: jobId,
        status: {
          in: [JobStatus.PENDING, JobStatus.FAILED],
        },
      },
    });
    console.log("Pending reviews:", pendingReviews.length);
    for (const review of pendingReviews) {
      try {
        const imageUrl = await generateLexImage(review, 1);
        if (imageUrl) {
          await prisma.lexImageGenOutputData.update({
            where: { id: review.id },
            data: {
              status: "COMPLETED",
              imageUrl: imageUrl,
            },
          });
        } else {
          await prisma.lexImageGenOutputData.update({
            where: { id: review.id },
            data: {
              status: "FAILED",
            },
          });
        }
        console.log(`Processed review ${review.id}:`, imageUrl);
      } catch (error) {
        console.error(`Error processing review ${review.id}:`, error);
      }
    }
    await prisma.lexImageGenJob.update({
      where: {
        id: jobId,
      },
      data: {
        status: JobStatus.COMPLETED,
      },
    });
    console.log(`Job ${jobId} completed successfully`);
  } catch (error) {
    console.error(`Job ${jobId} failed:`,error);
    await prisma.lexImageGenJob.update({
      where: {
        id: jobId,
      },
      data: {
        status: JobStatus.FAILED,
      },
    });
  }
}

module.exports = { reviewUpdater, processLexImageGenJob };
