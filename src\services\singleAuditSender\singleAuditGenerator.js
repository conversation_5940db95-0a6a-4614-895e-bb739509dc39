const prisma = require("../../database/prisma/getPrismaClient");
const csvParser = require("../../utils/csvParser"); // Adjust the path as necessary
const processAmazonData = require("../../utils/workerUtils/processAmazonData");
const processAuditData = require("../../utils/workerUtils/processAuditData");
const scrapeAmazon = require("../scrapeAmazon");

async function auditGenerator(productUrl, userId) {
  try {
    const asin = extractAsinFromUrl(productUrl);
    const job = await prisma.job.create({
      data: {
        clientId: parseInt(userId),
        status: "pending",
        singleCompany: true,
        name:  asin+ "-singlejob",
        campaignId: 1,
      },
    });
    const row = {
      "Seller Name": asin || "",
      "Best Selling Product URL": productUrl || "",
      "ASIN":asin
    };
    console.log({row})

    await csvParser.processJsonData(row, job.clientId, job);
    const auditUrl = await processSingleJob(job);
    return auditUrl;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    // Sentry.captureException("Error in auditGenerator:", error);
    console.error("Error in auditGenerator:", error);
  }
}

async function processSingleJob(job) {
  try {
    console.log("Checking job status for jobId:", job.id);
    // Getting All the Company Data for the Job
    const outputData = await prisma.outputData.findFirst({
      where: {
        jobId: job.id,
      },
    });

    await scrapeAmazon({
      company_name: outputData.companyName,
      company_id: outputData.companyId,
      clientId: job.clientId,
    });
    let userPrompt = {
      "Company Name": outputData.companyName,
      "First Name": outputData.firstName,
      "Amazon Data": {},
      "Web Data": {},
    };

    const amazonData = await processAmazonData(userPrompt, outputData);
    await processAuditData(outputData, amazonData, job.clientId);

    const auditLink = await prisma.amazonAuditReport.findFirst({
      where: {
        companyId: outputData.companyId,
      },
      select: {
        pdfUrl: true,
      },
    });
    return auditLink.pdfUrl;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    // Sentry.captureException("Error in processing Job:", error);
    console.error("Error in processing Job:", error);
  }
}

function extractAsinFromUrl(url) {
  const asinPart = url.split("/dp/")[1]; // Get the part after "/dp/"
  const asin = asinPart.split(/[/?]/)[0]; // Split by "?" or "/" and take the first part
  return asin;
}

module.exports = auditGenerator;

// const url =
//   "https://www.amazon.com/INIU-High-Speed-Flashlight-Powerbank-Compatible/dp/B07CZDXDG8/?_encoding=UTF8&pd_rd_w=7sYtM&content-id=amzn1.sym.1e740ee2-ebac-498f-a702-cd53df994072&pf_rd_p=1e740ee2-ebac-498f-a702-cd53df994072&pf_rd_r=0EGZKNK5223K6FVA3BKZ&pd_rd_wg=FYzv4&pd_rd_r=3bc6e56f-7fbf-4116-881e-a7593818ac6b&ref_=pd_hp_d_atf_dealz_cs";
// auditGenerator(url, 2);
