/**
 * Example usage of the enhanced prompt chain system for lex analysis
 * This file demonstrates how to use the new prompt chain functionality
 */

const { runPrompt<PERSON>hain, runLexAnalysisChain } = require("../src/services/spike/runPromptChain");
const { analyzeAmazonReviewsWithCompliance } = require("../src/services/lex/lexPromptChainProcessor");

// Example review data
const sampleReview = {
  productTitle: "Amazon Basics 24-Pack AA Alkaline Batteries",
  "Review Title": "Terrible customer service experience",
  reviewContent: "The batteries are okay but the seller took forever to respond to my emails. Customer service was absolutely terrible and unhelpful. I had to wait 3 weeks for a response. Will never order from this seller again.",
  "Review ID": "R123456789"
};

const sampleReviews = [sampleReview];

async function demonstrateBasicPromptChain() {
  console.log("🔄 Demonstrating basic prompt chain with file loading...\n");
  
  try {
    // Example 1: Using file-based prompts
    const result = await runProm<PERSON><PERSON>hain(
      "example/brandAnalysis.yaml", // Load from file
      "nike", // Simple string input
      { debug: true }
    );
    
    console.log("📄 File-based prompt result:");
    console.log("Output:", result.output);
    console.log("Cost:", `$${result.cost.toFixed(4)}`);
    console.log("Tokens:", result.tokenUsage);
    console.log("\n" + "=".repeat(50) + "\n");
    
  } catch (error) {
    console.error("❌ Error in basic prompt chain:", error.message);
  }
}

async function demonstrateCustomVariables() {
  console.log("🔄 Demonstrating custom variable substitution...\n");
  
  try {
    // Example 2: Using custom variables
    const customPrompt = `
- role: system
  content: "You are a product analysis assistant."
  
- role: user
  content: |
    Analyze this product:
    Title: {{productTitle}}
    Price: {{price}}
    Category: {{category}}
    
    Provide a brief analysis of its market position.
`;

    const result = await runPromptChain(
      customPrompt, // YAML string
      {
        productTitle: "Wireless Bluetooth Headphones",
        price: "$79.99",
        category: "Electronics"
      },
      { debug: true }
    );
    
    console.log("🎯 Custom variables result:");
    console.log("Output:", result.output);
    console.log("Cost:", `$${result.cost.toFixed(4)}`);
    console.log("\n" + "=".repeat(50) + "\n");
    
  } catch (error) {
    console.error("❌ Error in custom variables demo:", error.message);
  }
}

async function demonstrateLexAnalysis() {
  console.log("🔄 Demonstrating lex analysis chain...\n");
  
  try {
    // Example 3: Lex analysis using the specialized function
    const result = await runLexAnalysisChain(sampleReview, { 
      debug: true,
      temperature: 0.3 
    });
    
    console.log("⚖️ Lex analysis result:");
    console.log("Analysis:", result.analysis.substring(0, 200) + "...");
    console.log("Top Violation:", result.topViolations.guidelineViolation1);
    console.log("Confidence Score:", result.topViolations.confidenceScore1);
    console.log("Violation Output:", result.violation ? result.violation.substring(0, 100) + "..." : "None");
    console.log("Total Cost:", `$${result.cost.toFixed(4)}`);
    console.log("Total Tokens:", result.tokenUsage.totalTokens);
    console.log("\n" + "=".repeat(50) + "\n");
    
  } catch (error) {
    console.error("❌ Error in lex analysis:", error.message);
  }
}

async function demonstrateBatchProcessing() {
  console.log("🔄 Demonstrating batch processing (original function replacement)...\n");
  
  try {
    // Example 4: Using the drop-in replacement function
    const results = await analyzeAmazonReviewsWithCompliance(sampleReviews, {
      debug: true,
      temperature: 0.3
    });
    
    console.log("📦 Batch processing results:");
    results.forEach((result, index) => {
      console.log(`Review ${index + 1}:`);
      console.log(`  - Top Violation: ${result.topViolations.GuidelineViolation1}`);
      console.log(`  - Confidence: ${result.topViolations.ConfidenceScore1}`);
      console.log(`  - Cost: $${result.cost.toFixed(4)}`);
      console.log(`  - Has Violation Output: ${result.violation ? 'Yes' : 'No'}`);
    });
    
    const totalCost = results.reduce((sum, r) => sum + r.cost, 0);
    console.log(`\n💰 Total batch cost: $${totalCost.toFixed(4)}`);
    console.log("\n" + "=".repeat(50) + "\n");
    
  } catch (error) {
    console.error("❌ Error in batch processing:", error.message);
  }
}

async function runAllExamples() {
  console.log("🚀 Starting Enhanced Prompt Chain Examples\n");
  console.log("=".repeat(60) + "\n");
  
  await demonstrateBasicPromptChain();
  await demonstrateCustomVariables();
  await demonstrateLexAnalysis();
  await demonstrateBatchProcessing();
  
  console.log("✅ All examples completed!");
}

// Uncomment to run examples
// runAllExamples().catch(console.error);

module.exports = {
  demonstrateBasicPromptChain,
  demonstrateCustomVariables,
  demonstrateLexAnalysis,
  demonstrateBatchProcessing,
  runAllExamples
};
