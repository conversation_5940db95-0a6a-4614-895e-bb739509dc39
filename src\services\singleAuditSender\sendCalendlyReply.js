const { SESClient, SendEmailCommand } = require("@aws-sdk/client-ses");
require("dotenv").config();

// Initialize SES client
const ses = new SESClient({ region: process.env.AWS_REGION }); // Make sure AWS_REGION is set

async function sendCalendlyReply({ to, subject, body, from }) {
  try {
    const params = {
      Destination: {
        ToAddresses: [to],
      },
      Message: {
        Body: {
          Html: {
            Charset: "UTF-8",
            Data: body,
          },
        },
        Subject: {
          Charset: "UTF-8",
          Data: subject,
        },
      },
      Source: from, // Must be a verified sender in SES
    };

    const command = new SendEmailCommand(params);
    const response = await ses.send(command);
    console.log("Email sent:", response.MessageId);
    return response;
  } catch (error) {
    console.error("Failed to send email:", error);
    throw error;
  }
}

function generateEmail(auditURL, inviteeName) {
  try {
    const subject = "Your Amazon Audit Report is Ready!";
    const body = `
        <p>Hi ${inviteeName},</p>
        <p>Thank you for scheduling a meeting with us. Your Amazon audit report is now ready.</p>
        <p>You can view it here: <a href="${auditURL}">${auditURL}</a></p>
        <p>If you have any questions, feel free to reply to this email.</p>
        <p>Best regards,<br/>The Audit Team</p>
      `;
    return { subject, body };
  } catch (error) {
    console.error("Failed to generate email:", error);
    throw error;
  }
}

// Call the function
// (async () => {
//   const email = generateEmail("test.com", "Prime");
//   await sendCalendlyReply({
//     to: "<EMAIL>",
//     subject: email.subject,
//     body: email.body,
//     from: "<EMAIL>",
//   });
// })();

module.exports = { sendCalendlyReply, generateEmail };
