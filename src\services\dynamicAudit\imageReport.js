const { parseYamlReport } = require("../../utils/yamlParser");
function getImageReport(data, report, clientId, ymlPath) {
  if (!data.productData?.[0]?.images) {
    return;
  }

  const reportData = {
    imagesCount: data.productData[0].images.noOfImages,
    videosCount: data.productData[0].images.noOfVideos,
  };

  const imageReports = parseYamlReport("imageReport", reportData, ymlPath);
  report.push(...imageReports);
}

module.exports = getImageReport;
