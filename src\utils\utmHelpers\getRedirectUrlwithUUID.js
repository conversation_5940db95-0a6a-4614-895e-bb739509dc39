const prisma = require("../../database/prisma/getPrismaClient");

const getRedirectUrlwithUUId = async (uuid) => {
  try {
    if (!uuid) {
      throw new Error("UUID is reuired");
    }

    const url = await prisma.uTMSyncNew.findUnique({
      where: {
        uuid,
      }
    });

    if (!url) {
      console.log("No URL found with this UUID:", {
        uuid,
      });
      return "";
    }
    return url;
  } catch (error) {
    console.log("Error while redirecting:", error);
    return ""; // Return an empty string if there's an error or no match found
  }
};

module.exports = getRedirectUrlwithUUId;
