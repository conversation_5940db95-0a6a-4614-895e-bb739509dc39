require("dotenv").config();
const { AzureOpenAI } = require("openai");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const fs = require('fs').promises;
const path = require('path');

/**
 * Initialize AzureOpenAI client
 */
const client = new AzureOpenAI({
  apiKey: process.env.AZURE_OPENAI_API_KEY,
  endpoint: process.env.AZURE_OPENAI_ENDPOINT,
  deployment: process.env.AZURE_OPENAI_DEPLOYMENT,
  apiVersion: process.env.AZURE_OPENAI_API_VERSION,
});

/**
 * Analyze Amazon review content using AzureOpenAI
 * @param {Object} review - The original review object
 * @returns {Promise<string>}
 */
async function analyzeReviewContent(review) {
  const contentPrompt = `
You are an Amazon review expert. You are given review title, review content and product title/name as on Amazon.

INPUTS:
- Product Title: ${review.productTitle}
- Review Title: ${review["Review Title"]}
- Review Content: ${review.reviewContent}

Based on this: 
1. Carefully analyze the product category and type based on the title
2. Determine what constitutes relevant and expected content for reviews of this product type
3. Identify the main claims, opinions, or experiences described in the review
4. Consider multiple possible interpretations of ambiguous statements
5. Evaluate whether the review primarily discusses the product itself or other factors
6. Assess the reviewer's apparent intent and tone.

Give an output with the above answers in short but without missing a detail for further processing.
`;

  const completion = await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "You are an Amazon review analysis assistant.",
      },
      {
        role: "user",
        content: contentPrompt,
      },
    ],
    model: process.env.AZURE_OPENAI_MODEL_ID,
    temperature: 0.7,
    max_tokens: 700,
  });

  return completion.choices?.[0]?.message?.content ?? "";
}

/**
 * Analyze review for compliance with community guidelines using AzureOpenAI
 * @param {Object} review - The original review object
 * @param {string} reviewAnalysis - Output from the first prompt
 * @returns {Promise<string>}
 */
async function analyzeReviewCompliance(review, reviewAnalysis) {
  const compliancePrompt = `
Analyze the following Amazon product review for compliance with community guidelines. For each guideline section, provide a confidence rating (0-3) indicating the likelihood of non-compliance:

0 = No violation
1 = Low confidence of violation (possibly non-compliant)
2 = Medium confidence of violation (likely non-compliant)
3 = High confidence of violation (clearly non-compliant)

INPUTS:
- Product Title: ${review.productTitle}
- Review Title: ${review["Review Title"]}
- Review Content: ${review.reviewContent}
- Review context: **${reviewAnalysis}**

DETAILED GUIDELINE SECTIONS:

G1 : Review focuses ONLY on Sellers and the Customer Service they provide
ALLOWED : "The product was good but customer service could be better" 
NOT ALLOWED : "Worst Customer service,nobody ever answers to mails" 
REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

G2: Review focuses ONLY on Ordering issues and returns
ALLOWED : "The product is great but one of the products were missing"
NOT ALLOWED : "I received the wrong order and now I can't even return" 
REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

G3: SHIPPING & PACKAGING
ALLOWED : "The product is okay, could have been packed better"
NOT ALLOWED: "Everything was spilling and damaged" "The product came broken, disappointed"
REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

G4: PRODUCT CONDITION ON ARRIVAL: Review focuses ONLY on Product condition on arrival and damaged product at time of arrival
ALLOWED: Comments about the product's performance, quality, strength and features, If the review talks about the product along with the seller/shipping feedback.
NOT ALLOWED: "The box arrived damaged,"
REASONING: Community content should help customers learn about the product itself, not individual ordering experience

G5 :SHIPPING COST : Review focuses ONLY on Shipping cost and speed
ALLOWED : Product is great, shipping could have been faster "
NOT ALLOWED : "Seller took too long to ship,"
REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

G6. PRICING/AVAILABILITY: Review contains individual pricing experiences or store-specific availability comments not relevant to all customers.
   ALLOWED: Value comments about the product (e.g., "For only $29, this blender is really great")
   ALLOWED: General availability wishes (e.g., "I wish this book was also available in paperback")
   NOT ALLOWED: Individual pricing experiences (e.g., "Found this item here for $5 less than at my local store")
   NOT ALLOWED: Store-specific availability (e.g., "My local Target doesn't carry this anymore")
   REASONING: These comments aren't relevant for all customers

G7. LANGUAGE VIOLATION: Content is written in languages other than English or Spanish or mixes languages inappropriately.
   ALLOWED: Content in the supported languages of the Amazon site
   NOT ALLOWED: Content in unsupported languages or mixed-language content
   REASONING: Content must be accessible to all users of the site

G8. SPAM/REPETITIVE CONTENT: Contains repetitive text, nonsense, gibberish, distracting punctuation/symbols, or ASCII art.
   ALLOWED: Coherent, non-repetitive text relevant to the product
   NOT ALLOWED: Repetitive text, nonsense/gibberish, content consisting mainly of punctuation/symbols, ASCII art
   REASONING: Such content is distracting and doesn't help customers make purchasing decisions

G9. PRIVATE INFORMATION: Shares personal information.
   NOT ALLOWED: Phone numbers, email addresses, mailing addresses, license plates, DSN, order numbers
   REASONING: Protects privacy and prevents identity theft

G10. PROFANITY/HARASSMENT: Contains profanity, obscenities, name-calling, harassment, threats, attacks, libel, defamation, or inflammatory content.
ALLOWED: Questioning beliefs or expertise respectfully, "I hate the product" "The product is crap"
NOT ALLOWED: Profanity/obscenities/name-calling, harassment/threats, attacks on people, libel/defamation/inflammatory content, coordinated posting from multiple accounts. Using Only abuse
REASONING: Maintains a respectful community environment

G11. HATE SPEECH: Expresses hatred based on protected characteristics or promotes organizations using hate speech.
   NOT ALLOWED: Hatred based on race, ethnicity, nationality, gender, gender identity, sexual orientation, religion, age, or disability
   NOT ALLOWED: Promoting organizations that use such hate speech
   REASONING: Ensures an inclusive and respectful environment

G12. SEXUAL CONTENT: Contains inappropriate sexual content.
   ALLOWED: Discussing sex and sensuality products sold on Amazon or products with sexual content
   NOT ALLOWED: Profanity/obscene language, content with nudity, sexually explicit images or descriptions
   REASONING: Maintains appropriate content standards

G13. EXTERNAL LINKS: Contains links to external sites, phishing, malware, or URLs with referrer/affiliate codes.
   ALLOWED: Links to other products on Amazon
   NOT ALLOWED: Links to external sites, phishing/malware sites, URLs with referrer tags or affiliate codes
   REASONING: Ensures user safety and prevents exploitation

G14. PROMOTIONAL CONTENT: Primary purpose is promoting a company, website, or special offer, or was created by someone with financial interest.
   NOT ALLOWED: Content whose MAIN purpose is promotion of another brand or product.
   REASONING: Prevents conflicts of interest and ensures authentic reviews

G15. ILLEGAL ACTIVITIES: Encourages illegal activities.
   NOT ALLOWED: Content encouraging violence, illegal drug use, underage drinking, child/animal abuse, fraud, terrorism
   NOT ALLOWED: Threats of physical/financial harm, fraudulent schemes, encouraging dangerous product misuse
   REASONING: Prevents promotion of harmful or illegal behavior

G16. MEDICAL CLAIMS: Makes statements about preventing or curing serious medical conditions.
   NOT ALLOWED: Claims related to preventing/curing serious medical conditions for any product type (including foods, beverages, supplements, cosmetics, personal care products)
   REASONING: Prevents potentially dangerous medical misinformation

Make sure not to hallucinate or generate content that is not there.

OUTPUT FORMAT:

REVIEW_INTERPRETATION: [Primary interpretation of the review's content and intent, noting any ambiguities or alternative readings]

RATINGS:
G1: [0-3]
G2: [0-3]
G3: [0-3]
G4: [0-3]
G5: [0-3]
G6: [0-3]
G7: [0-3]
G8: [0-3]
G9: [0-3]
G10: [0-3]
G11: [0-3]
G12: [0-3]
G13: [0-3]
G14: [0-3]
G15: [0-3]
G16: [0-3]

TOP_VIOLATIONS:
[ONLY if any violation rating above is more than or equal to 2, share the details below, else, List the three highest-rated violations in descending order by rating]

TOP1: G# - [Rating] - [Concise reasoning]
TOP2: G# - [Rating] - [Concise reasoning]
TOP3: G# - [Rating] - [Concise reasoning]

Make sure TOP_VIOLATIONS output format is clean and single quoted only, for example:

'TOP1: G10 - 2 - The review contains inflammatory content, including accus
ations of fraud ("absolute scam") and references to lawsuits, which could be con
sidered defamatory.  \n' +
'TOP2: G8 - 1 - The review includes some unclear or repetitive phrasing (e
.g., "Consciences of the tubes"), which may slightly detract from clarity but do
es not constitute outright gibberish.  \n' +
'TOP3: G6 - 1 - The review discusses pricing frustrations, but these are f
ramed as general concerns about value rather than individual pricing experiences
, making it a low-confidence violation.  ',
`;

  const completion = await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "You are an Amazon review compliance analysis assistant.",
      },
      {
        role: "user",
        content: compliancePrompt,
      },
    ],
    model: process.env.AZURE_OPENAI_MODEL_ID,
    temperature: 0.3,
    max_tokens: 900,
  });

  return completion.choices?.[0]?.message?.content ?? "";
}

/**
 * Extract top violations from compliance output
 * @param {string} complianceOutput - Output from the compliance prompt
 * @returns {Object} - Object containing the extracted top violations
 */
function extractTopViolations(complianceOutput) {
  const topViolations = {
    GuidelineViolation1: "",
    GuidelineViolation2: "",
    GuidelineViolation3: "",
    ConfidenceScore1: 0,
    ConfidenceScore2: 0,
    ConfidenceScore3: 0,
    Reason1: "",
    Reason2: "",
    Reason3: "",
  };

  // This regex supports:
  // - Variants like "**TOP1**", "TOP1:", "TOP1 -"
  // - Dashes: -, –, --
  // - Any spacing issues
  // - Captures guideline, score, and reason robustly
  // const topRegex =
  //   /(?:\*+)?TOP\s*([1-3])(?:\*+)?\s*[:\-–]*\s*G(\d+)\s*[-–—]{1,2}\s*(\d+)\s*[-–—]{1,2}\s*(.+?)(?=(?:\n\s*(?:\*+)?TOP\s*[1-3])|\n*$)/gis;

  // for (const match of matches) {
  //   const index = parseInt(match[1]); // 1, 2, or 3
  //   const guideline = `G${match[2]}`;
  //   const score = parseInt(match[3], 10);
  //   const reason = match[4].trim().replace(/\*+$/, ""); // Remove trailing asterisks if any

  //   topViolations[`GuidelineViolation${index}`] = guideline;
  //   topViolations[`ConfidenceScore${index}`] = score;
  //   topViolations[`Reason${index}`] = reason;
  // }

  // Enhanced regex to handle multiple formats including the new format with quotes and line breaks:
  // - Original: "TOP1: G# - # - reason"
  // - Markdown: "- **TOP1:** G# - # - reason"  
  // - New format: "'...TOP1: G# - # - reason...'"
  // - Variations with different spacing, punctuation, and line breaks
  const topRegex = /(?:[-*'"\s]*)?(?:\*+)?TOP\s*([1-3])(?:\*+)?(?::|-|\s)\s*G(\d+)\s*[-–—]{1,2}\s*(\d+)\s*[-–—]{1,2}\s*(.+?)(?=(?:\n.*?(?:[-*'"\s]*)?(?:\*+)?TOP\s*[1-3])|\n*$|'.*?\+|`.*?\+)/gis;

  const matches = [...complianceOutput.matchAll(topRegex)];

  for (const match of matches) {
    const index = parseInt(match[1]); // 1, 2, or 3
    const guideline = `G${match[2]}`;
    const score = parseInt(match[3], 10);
    
    // Clean up the reason by removing trailing/leading quotes, asterisks, markdown, and extra whitespace
    const reason = match[4].trim()
      .replace(/['"`]+/g, "") // Remove quotes
      .replace(/\*+$/g, "") // Remove trailing asterisks
      .replace(/^\*+/g, "") // Remove leading asterisks
      .replace(/\s*\+\s*$/g, "") // Remove trailing + symbols
      .replace(/\s+/g, " ") // Normalize whitespace
      .replace(/\n/g, " ") // Replace line breaks with spaces
      .trim();

    topViolations[`GuidelineViolation${index}`] = guideline;
    topViolations[`ConfidenceScore${index}`] = score;
    topViolations[`Reason${index}`] = reason;
  }

  return topViolations;
}

/**
 * Analyze review violation using AzureOpenAI
 * @param {Object} review - The original review object
 * @param {string} reviewAnalysis - Output from the first prompt
 * @param {string} guidelineViolation - The guideline violation
 * @param {string} reason - The reason given for the violation
 * @returns {Promise<string>}
 */
async function analyzeReviewViolation(
  review,
  reviewAnalysis,
  guidelineViolation,
  reason
) {
  const violationPrompt = `
You are an Amazon review expert. Check carefully if the review violates the given guideline. Think about what the review is really saying and whether it matches the spirit of the guideline — not just the words.

If the review is compliant:
Output only: False Positive

If the review clearly violates the guideline (with more than 85% confidence):
Write a short and clear explanation in simple English (5th grade level). Your output must follow this exact format, write it in a paragraph with not more than 40 words, make sure to adhere to the important rules:

Briefly explain what type of feedback it is (e.g., shipping, price, seller) include reasoning.

Important rules:
- Use actual quotes from part of the review to make your case.
- Do NOT start your output with a quote.
- Instead, start with something like: ["The review focuses on…" or "The reviewer talks about…" or something like that.

Do not guess or add any new info. Be strict and logical. Do not change the format.

End with this exact sentence (no edits, no rewording):
THIS IS A [type of feedback] FEEDBACK NOT PRODUCT REVIEW - WHICH IS NOT ALLOWED AS PER COMMUNITY GUIDELINE
(Replace [type of feedback] with words like "SHIPPING & PACKAGING", "SELLER COMPLAINT", "PRICING ISSUE", etc.)

INPUTS:
Product Title: ${review.productTitle}
Review Title: ${review["Review Title"]}
Review Content: ${review.reviewContent}
Review Context: ${reviewAnalysis}
Guideline violated: ${guidelineViolation}
Potential reason given for violation: ${reason}
`;

  const completion = await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "You are an Amazon review violation analysis assistant.",
      },
      {
        role: "user",
        content: violationPrompt,
      },
    ],
    model: process.env.AZURE_OPENAI_MODEL_ID,
    temperature: 0.3,
    max_tokens: 500,
  });

  return completion.choices?.[0]?.message?.content ?? "";
}

/**
 * Analyze Amazon reviews using AzureOpenAI and then check compliance
 * @param {Array} reviewsArray
 * @returns {Promise<Array>}
 */
async function analyzeAmazonReviewsWithCompliance(reviewsArray) {
  const analyses = [];

  for (const [index, review] of reviewsArray.entries()) {
    // Step 1: Run the first analysis
    let analysisResult = "";
    try {
      analysisResult = await analyzeReviewContent(review);
    } catch (err) {
      analysisResult = null;
    }

    // Step 2: Run the compliance check if analysis succeeded
    let complianceResult = "";
    if (analysisResult) {
      try {
        complianceResult = await analyzeReviewCompliance(
          review,
          analysisResult
        );
      } catch (err) {
        complianceResult = null;
      }
    }

    // Extract top violations from compliance output
    const topViolations = extractTopViolations(complianceResult);

    // Step 3: Run the violation analysis if confidence score is 2 or 3
    let violationResult = "";
    if (topViolations.ConfidenceScore1 >= 2) {
      try {
        violationResult = await analyzeReviewViolation(
          review,
          analysisResult,
          topViolations.GuidelineViolation1,
          topViolations.Reason1
        );
      } catch (err) {
        violationResult = null;
      }
    }

    analyses.push({
      reviewId: review["Review ID"],
      asin: review.ASIN,
      productTitle: review.productTitle,
      productLink: review.productLink,
      reviewTitle: review["Review Title"],
      reviewContent: review.reviewContent, // Added this line
      reviewLink: review["Review URL"],
      reviewer: review.reviewer,
      reviewerLink: review.reviewerLink,
      reviewDate: review.reviewDate,
      rating: review.reviewScore,
      analysis: analysisResult,
      compliance: complianceResult,
      topViolations: topViolations,
      violation: violationResult,
    });
  }

  return analyses;
}

/**
 * Write analysis results to a CSV file
 * @param {Array} results - The analysis results
 */
async function writeToCsv(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const csvWriter = createCsvWriter({
    path: `output_${timestamp}.csv`,
    header: [
      { id: "asin", title: "ASIN" },
      { id: "reviewId", title: "Review ID" },
      { id: "productLink", title: "Product Link" },
      { id: "reviewLink", title: "Review Link" },
      { id: "reviewContent", title: "Review Content" }, // Added this line
      { id: "prompt1Output", title: "Prompt 1 Output" },
      { id: "prompt2ViolationCode", title: "Prompt 2 Violation Code" },
      { id: "prompt2ConfidenceScore", title: "Prompt 2 Confidence Score" },
      { id: "prompt2Reason", title: "Prompt 2 Reason" },
      { id: "prompt3Output", title: "Prompt 3 Output" },
    ],
  });

  const records = results.map((result) => ({
    asin: result.asin,
    reviewId: result.reviewId,
    productLink: result.productLink,
    reviewLink: result.reviewLink,
    reviewContent: result.reviewContent || "", // Added this line
    prompt1Output: result.analysis || "",
    prompt2ViolationCode: result.topViolations?.GuidelineViolation1 || "",
    prompt2ConfidenceScore: result.topViolations?.ConfidenceScore1 || 0,
    prompt2Reason: result.topViolations?.Reason1 || "",
    prompt3Output: result.violation || "",
  }));

  await csvWriter.writeRecords(records);
  console.log(`✅ CSV results saved to: output.csv_${timestamp}`);
}

// Update the main runner to use the new function

const reviewsArray = [
  {
    ASIN: "B09B1BP1JH",
    "Seller Name": "Belize Belize",
    HelpfulCounts: 10,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B09B1BP1JH/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=1",
    "Review ID": "R1I2I0WG0EKGOW",
    productLink: "https://www.amazon.com/dp/B09B1BP1JH",
    productTitle:
      "Spartan Mosquito Pro Tech - 1 Acre Pack 4 Tubes (2 Boxes) 100% American Made",
    reviewContent:
      "EDIT: ABSOLUTE SCAM NO WONDER THEY WERE BEING SUED LEFT RIGHT UP AND DOWN BY THEIR INVESTORS! Yesterday I swatted one mosquito while I was setting these traps up. TODAY HOWEVER, I cannot even stand outside on my property. This drew THE ENTIRE NEIGHBORHOOD OF MOSQUITOS ALL INTO MY YARD. And I have a large yard for livening in town. About half an acre. I followed I structions. Says 80 feet from the house the closest one is 100 feet from my house and it looks like a swarm of flies going after some rotten food around my head. This isn’t right and Amazon needs to take this product off their market I will be talking to them for a refund because I’m not spending 50$ to bring the mosquitos INTO my yard. That’s bull.2 stars not because it doesn’t work I just put them up… but all advertising and videos out there, there are TONS of lawsuits out there had stated 90 days. THE FINE PRINT SAYS UP TO 30 DAYS. So no wonder all the 6-7 figure lawsuits. Can’t say in your advertisement 90 days then put in the fine print up to 30. 50$ every month?! That’s BULL. I will however be buying a big bag of mosquito killer and toss them in there and add water. Consciences of the tubes is the ONLY reason I gave 2 stars because the whole 5 gallon bucket and a 15$ BAG of add to water mosquito killer idea doesn’t work for me as I have a heeler and a border collie and I need to be able to hang these high in trees or they will get to it and although “safe for dogs” is stated on most mostiuto killers that you ad to water would you drink it? I think not. So the convienence of the hanging high up over 5 gallon buckets is the extra start. To the company… don’t say up to 3 months/90 days in all your advertisement videos on line and then put up to 30 on the fine print. You should be sued. A lot. Can’t make this stuf up smdh",
    reviewerCountry: "United States",
    reviewDate: "April 17, 2025",
    reviewScore: 1,
    reviewTitle: "False advertisement. The owner puts videos online. Lies.",
    reviewer: "Brian Mansinon",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R1I2I0WG0EKGOW",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AHW53W2YQHOGQZUXSYNBJSSU3R3Q/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B09B1BP1JH",
    "Seller Name": "Belize Belize",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B09B1BP1JH/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=4",
    "Review ID": "R2C2H8AB5JWA9O",
    productLink: "https://www.amazon.com/dp/B09B1BP1JH",
    productTitle:
      "Spartan Mosquito Pro Tech - 1 Acre Pack 4 Tubes (2 Boxes) 100% American Made",
    reviewContent: "Garbage don’t work. Stole my money.",
    reviewerCountry: "United States",
    reviewDate: "April 5, 2025",
    reviewScore: 1,
    reviewTitle: "Scam Product",
    reviewer: "Mike",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R2C2H8AB5JWA9O",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AEE63CUJ7KQMHJ6BOMFIMUQIMNGA/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B083G6WMGZ",
    "Seller Name": "Newlife Naturals",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B083G6WMGZ/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=1",
    "Review ID": "R273CTFBONW7EK",
    productLink: "https://www.amazon.com/dp/B083G6WMGZ",
    productTitle:
      "NewLife Naturals D-Mannose Capsules | w/Cranberry and Hibiscus Extracts | Natural Urinary Tract Health Supplement | 1400mg Pure Veggie Powder | 60 Veggie Capsules",
    reviewContent:
      "Be sure and research this product before you buy and use this product. It stimulates sugar production and it is NOT appropriate for diabetics, which I am.",
    reviewerCountry: "United States",
    reviewDate: "April 2, 2025",
    reviewScore: 1,
    reviewTitle: "Know what you are buying",
    reviewer: "FiveCats Designs",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R273CTFBONW7EK",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AHLSPYZYIO34ZW6CNQI6FVY5DSYA/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B071ZDD3HL",
    "Seller Name": "Naked",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B071ZDD3HL/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=2",
    "Review ID": "R28OZI29F5LL1H",
    productLink: "https://www.amazon.com/dp/B071ZDD3HL",
    productTitle:
      "NAKED Energy - Pure Pre Workout Powder for Men and Women, Vegan, Unflavored, Healthy Pre Workout - No Added Sweeteners, Colors Or Flavors - 50 Servings",
    reviewContent:
      "Unflavored makes everything you put it in tastes like piss. Can't return it because it's now been opened. Waste of ~$50.",
    reviewerCountry: "United States",
    reviewDate: "March 28, 2025",
    reviewScore: 1,
    reviewTitle: "Waste of money",
    reviewer: "Brett hough",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R28OZI29F5LL1H",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AFJEZ47SR3EV3Y6CZ7LCUTR5DSMA/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07ST3MCVF",
    "Seller Name": "Newlife Naturals",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07ST3MCVF/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=1",
    "Review ID": "R2JWUPSFE9CXJE",
    productLink: "https://www.amazon.com/dp/B07ST3MCVF",
    productTitle:
      "NewLife Naturals - Boric Acid Suppositories for Women pH Balance Pills - 600mg - Feminine Care - Vaginal Odor Itching Discharge BV | Made in USA | 30 Capsules",
    reviewContent:
      "This is the only thing I have found that works to keep me balanced but….This product has made me miss my (on time) period/menstruation on two occasions! I am concerned us women should not be putting boric acid inside of us. It maybe poisoning us.",
    reviewerCountry: "United States",
    reviewDate: "March 21, 2025",
    reviewScore: 1,
    reviewTitle: "Missing Period!",
    reviewer: "Kiki",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R2JWUPSFE9CXJE",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.****************************/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B0CRPPCD89",
    "Seller Name": "M Shop",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B0CRPPCD89/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=8",
    "Review ID": "R1ZR7CDU0ITG7B",
    productLink: "https://www.amazon.com/dp/B0CRPPCD89",
    productTitle:
      "M Mase- Professional Nail Drill Machine - Coreless 2-in-1 Nail File - Electric, Cordless, 35000 RPM, HD Display - Electric Nail Drill for Professional & Home Use (Rose Gold)",
    reviewContent:
      "The worst pile of junk I have ever bought. This is the 2nd one the company has sent out and it's stopped working shortly after.The build quality on it is subpar. I would strongly consider buying a different model and staying away from this shady company. They refuse to help resolve the issue I've had.Stay far away.",
    reviewerCountry: "United States",
    reviewDate: "March 19, 2025",
    reviewScore: 1,
    reviewTitle: "It's so bad",
    reviewer: "Chris/Steph",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R1ZR7CDU0ITG7B",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AEPEYBG4GB5RL6X6C6KTNGF6DYNA/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B09B1BP1JH",
    "Seller Name": "Belize Belize",
    HelpfulCounts: 2,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B09B1BP1JH/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=4",
    "Review ID": "R1O1FAXXZP4IP4",
    productLink: "https://www.amazon.com/dp/B09B1BP1JH",
    productTitle:
      "Spartan Mosquito Pro Tech - 1 Acre Pack 4 Tubes (2 Boxes) 100% American Made",
    reviewContent:
      "Doesn’t do crap, would recommend looking into active litigation against SPARTAN mosquito for their fraudulent claims",
    reviewerCountry: "United States",
    reviewDate: "March 17, 2025",
    reviewScore: 1,
    reviewTitle: "Waste of money",
    reviewer: "Sue Baer",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R1O1FAXXZP4IP4",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AEDO7U3ZRKRKQZIQCXC7FVD2EMWA/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B09B1BP1JH",
    "Seller Name": "Belize Belize",
    HelpfulCounts: 8,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B09B1BP1JH/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=2",
    "Review ID": "R254R3ET6XSJWP",
    productLink: "https://www.amazon.com/dp/B09B1BP1JH",
    productTitle:
      "Spartan Mosquito Pro Tech - 1 Acre Pack 4 Tubes (2 Boxes) 100% American Made",
    reviewContent:
      "Biggest scam you got, no wonder they had a legal problem with it before, mosquitoes still kept coming like nothing was around, and not a damn bug in it either,  either fix this or the people will take you down and karma will not be light.",
    reviewerCountry: "United States",
    reviewDate: "March 13, 2025",
    reviewScore: 1,
    reviewTitle: "Don't waste your money!",
    reviewer: "Jonathan Dixon",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R254R3ET6XSJWP",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AHY2VHMGIGHIFNXIMHUM576CJLHQ/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B018JDMD4K",
    "Seller Name": "Elizabeth Mott",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B018JDMD4K/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=7",
    "Review ID": "R18DPYTGVL5DPD",
    productLink: "https://www.amazon.com/dp/B018JDMD4K",
    productTitle:
      "Elizabeth Mott Thank Me Later Eye Primer – Long-Lasting, Matte Eyeshadow Base for Oily Lids & Crease-Free Wear – Pore Minimizer, Wrinkle-Filling, Shine Control Formula – Smudge-Proof, Lightweight, 10g",
    reviewContent: "Don't waste your money.",
    reviewerCountry: "United States",
    reviewDate: "March 4, 2025",
    reviewScore: 1,
    reviewTitle: "Don't waste your money.",
    reviewer: "Mermaid",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R18DPYTGVL5DPD",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AG5EYB4S6TJFN37NVWJOGX2N26HQ/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B071ZDD3HL",
    "Seller Name": "Naked",
    HelpfulCounts: 60,
    image1: "https://c.media-amazon.com/images/I/61vQ3WSsHtL._SY88.jpg",
    image2: "https://c.media-amazon.com/images/I/714Dv50k4WL._SY88.jpg",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B071ZDD3HL/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=1",
    "Review ID": "RNG3TQW40TSR9",
    productLink: "https://www.amazon.com/dp/B071ZDD3HL",
    productTitle:
      "NAKED Energy - Pure Pre Workout Powder for Men and Women, Vegan, Unflavored, Healthy Pre Workout - No Added Sweeteners, Colors Or Flavors - 50 Servings",
    reviewContent:
      "This product used to utilize natural, organic folate derived from Orgen-FA organic lemon peel. At some point midway through 2024 I noticed that they switched to synthetic folic acid. See attached photos. For those with MTHFR genetic mutations this is essentially poison. Synthetic folic acid exacerbates MTHFR complications resulting in a range of unpleasant symptoms including irritability, fatigue and brain fog. This product is far from naked since the formulation change. It is now wearing a toxic folic acid bikini. When I contacted the company the rep I talked to was deceptive and dismissive. All that being said, if you noticed a sudden, steep decline in performance over the past 6 months while using this formerly effective product, now you know why.",
    reviewerCountry: "United States",
    reviewDate: "January 14, 2025",
    reviewScore: 1,
    reviewTitle: "Formulation change from folate to synthetic folic acid",
    reviewer: "Timothy Durland",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/RNG3TQW40TSR9",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AGN5WZYYANIEFMPQ3OQLAXC3TXYQ/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B071ZDD3HL",
    "Seller Name": "Naked",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B071ZDD3HL/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=two_star&pageNumber=8",
    "Review ID": "R3RYJFKRWW09U1",
    productLink: "https://www.amazon.com/dp/B071ZDD3HL",
    productTitle:
      "NAKED Energy - Pure Pre Workout Powder for Men and Women, Vegan, Unflavored, Healthy Pre Workout - No Added Sweeteners, Colors Or Flavors - 50 Servings",
    reviewContent:
      "Pour le prix , il serais apprécié que le pot sois AU MOINS plein !!",
    reviewerCountry: "",
    reviewDate: "Reviewed in Canada on November 29, 2024",
    reviewScore: 0,
    reviewTitle: "",
    reviewer: "Jerry",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R3RYJFKRWW09U1",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B0CRM2T33J",
    "Seller Name": "TACH",
    HelpfulCounts: 32,
    image1: "https://m.media-amazon.com/images/I/71oHoLn+YOL._SY88.jpg",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B0CRM2T33J/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=two_star&pageNumber=1",
    "Review ID": "R161552QSTIEMW",
    productLink: "https://www.amazon.com/dp/B0CRM2T33J",
    productTitle:
      "Tach V3.1 Hard Shell 3 Piece Luggage Set - 20, 24 & 28 inch Luggage | Carry On, Medium & Large Checked Suitcases | Patented Built-In Connecting System | Rolling Suitcase Links 9 Bags (Green)",
    reviewContent:
      "I purchased a set of your best luggage from your store through Amazon 11 months ago.After a few uses, the place that holds the Velcro together on two of the three pieces of luggage has ripped and is not usable anymore to attach. I called the number on manufacturer website 833-TACH- LUG and no one answered and left two messages asking for a replacement and a return phone call. I also sent an email which was days later returned by someone at the company named John. I submitted the proof that he asked for via email then he stopped responding. 10 days later I called the company again to request the updated version V3.1 but again nothing but a generic computer voicemail.I am today formally filing a complaint regarding the failure in customer support and with the product with Amazon and filing an insurance claim to get a resolution.James W.",
    reviewerCountry: "United States",
    reviewDate: "January 22, 2024",
    reviewScore: 2,
    reviewTitle: "Not what they claim to be",
    reviewer: "James",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R161552QSTIEMW",
    reviewerLink: "Size: 3-Piece SetColor: Black",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07VVYN2PV",
    "Seller Name": "Empava",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07VVYN2PV/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=1",
    "Review ID": "RUVHCQEE3NS0W",
    productLink: "https://www.amazon.com/dp/B07VVYN2PV",
    productTitle:
      "Empava 36 Inch Electric Stove Induction Cooktop with 5 Power Boost Burners Smooth Surface Vitro Ceramic Glass in Black 240V",
    reviewContent:
      "The elements are only 3 inches so only the food in the middle of the pan will cook. If you turn up the temperature to try to cook the food on the edges then the middle of the pan will just end up with burn marks. Worst pitcher I've ever made.Good luck getting ahold of the manufacturer, and if you do they will just tell you to get f$&#%!.",
    reviewerCountry: "United States",
    reviewDate: "April 7, 2023",
    reviewScore: 1,
    reviewTitle: "Absolute trash would  rate negative stars if I could",
    reviewer: "muffstic",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/RUVHCQEE3NS0W",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AEEPVGF7IZI5AKGXYU5AXTINQQGA/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B01MF72PPZ",
    "Seller Name": "Elizabeth Mott",
    HelpfulCounts: 1,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B01MF72PPZ/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=3",
    "Review ID": "R3LTTZAH4KPJKU",
    productLink: "https://www.amazon.com/dp/B01MF72PPZ",
    productTitle:
      "Elizabeth Mott Whatup Beaches Matte Powder Bronzer & Contour for Face, Long-Lasting Natural Sun-Kissed Bronzing Finish for All Skin Tones, Buildable Pressed Compact Makeup, Vegan & Cruelty-Free (10g)",
    reviewContent:
      "This contains Talc it’s toxic to your health and can cause cancer. Talc has asbestos!!!",
    reviewerCountry: "United States",
    reviewDate: "June 22, 2022",
    reviewScore: 1,
    reviewTitle: "This is toxic!!",
    reviewer: "Ambrose Ayala",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R3LTTZAH4KPJKU",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AE4UPT3K4V3ROW2P4TVUGKIOUQ5Q/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07P3WBF5C",
    "Seller Name": "Empava",
    HelpfulCounts: 1,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07P3WBF5C/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=4",
    "Review ID": "R3KIKOV7USPLGQ",
    productLink: "https://www.amazon.com/dp/B07P3WBF5C",
    productTitle:
      "Empava 30 Inch Gas Cooktop with 5 World Class Made in Italy SABAF Burners, LPG/NG Convertible, Ideal RV Top Stoves for Kitchen, Stainless Steel",
    reviewContent:
      "I read other reviews that the cooktop glass shattered. I got lucky, as it came pre-shattered and not after unable to return. Very lucky! Anyways, if you end up wanting to get this cooktop after many bad reviews, be warned again and good luck!",
    reviewerCountry: "United States",
    reviewDate: "June 22, 2022",
    reviewScore: 1,
    reviewTitle: "Came Pre-Shattered",
    reviewer: "Joe K.",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R3KIKOV7USPLGQ",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.****************************/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07ST3MCVF",
    "Seller Name": "Newlife Naturals",
    HelpfulCounts: 2,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07ST3MCVF/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=three_star&pageNumber=5",
    "Review ID": "R1SUX539QJ9QN2",
    productLink: "https://www.amazon.com/dp/B07ST3MCVF",
    productTitle:
      "NewLife Naturals - Boric Acid Suppositories for Women pH Balance Pills - 600mg - Feminine Care - Vaginal Odor Itching Discharge BV | Made in USA | 30 Capsules",
    reviewContent:
      "Cured a yeast infection but gave me a herpes outbreak.. a very bad one that lasted weeks.. if you have hsv don't take this but otherwise it does work for curing the yeast infection",
    reviewerCountry: "United States",
    reviewDate: "August 15, 2021",
    reviewScore: 3,
    reviewTitle: "Works but..",
    reviewer: "elisha d.",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R1SUX539QJ9QN2",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AG2KWVVDIP3GFTNXO7HL3IO2B6PQ/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B01AWGY4TE",
    "Seller Name": "SureCall",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B01AWGY4TE/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=9",
    "Review ID": "R49943331MQS3",
    productLink: "https://www.amazon.com/dp/B01AWGY4TE",
    productTitle:
      "SureCall Fusion4Home Cell Phone Signal Booster up to 2000 sq ft, Boosts 5G/4G LTE, Omni Outdoor Antenna, Home & Office Multi-User All Carrier, Verizon AT&T Sprint T-Mobile, FCC Approved, USA Company",
    reviewContent:
      "When will we ever learn?  If it is a copycat product from China, it IS GOING TO BE JUNK!!Replaced it with a Wilson WeBoost that works.",
    reviewerCountry: "United States",
    reviewDate: "September 12, 2020",
    reviewScore: 1,
    reviewTitle: "More Chinese JUNK!",
    reviewer: "bobazon",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R49943331MQS3",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AFRTW2ZFO3V73HA36DRS7G2TI5RQ/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B088RM6936",
    "Seller Name": "Ficosta",
    HelpfulCounts: 12,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B088RM6936/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=2",
    "Review ID": "R32DQ4CQIJ4EGM",
    productLink: "https://www.amazon.com/dp/B088RM6936",
    productTitle:
      "Babyology All Natural Baby Wash and Shampoo - 100% Edible Ingredients - with Organic Lavender Essential Oil (Fragrance Free) - Good for Sensitive Skin - Non Toxic - Tear Free",
    reviewContent:
      "Cancer causing ingredients. It’s the combination of sodium benzoate and citric acid and/or ascorbic acid (vitamin C). When these ingredients get together, they form benzene, a cancer-causing chemical associated with leukemia and other blood cancers.",
    reviewerCountry: "United States",
    reviewDate: "December 1, 2019",
    reviewScore: 1,
    reviewTitle: "Cancer causing",
    reviewer: "Amazon Customer",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R32DQ4CQIJ4EGM",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AFIVZ6B6FJWN3LQ7U7C5FAJKUDCQ/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B00FAEOCP0",
    "Seller Name": "Elizabeth Mott",
    HelpfulCounts: 4,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B00FAEOCP0/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=three_star&pageNumber=1",
    "Review ID": "R1D0E76U50LN21",
    productLink: "https://www.amazon.com/dp/B00FAEOCP0",
    productTitle:
      "Elizabeth Mott Black Volumizing Mascara – Smudge-Proof, Lengthening Fiber Formula, Hourglass Wand – Clump-Free, Water-Resistant & Cruelty-Free – Safe for Lash Extensions, Long-Lasting, Full-Size 8ml",
    reviewContent:
      "This is one of those products that I got reeled in by because of the reviews. It happens to me a lot & usually with good reason. Sometimes I think there is a band-wagon mentality. In the case of this mascara I think that maybe you have a large pool of 5 star reviewers that are just hitting the market place & really haven't been test-driving mascara's for long enough to REALLY know when they've found one of the \"Greats\". But of course, I think that the iconic pink tube mascara with the green tube is overrated too. But I digress. I have VERY long eye-lashes. (I buy generic Latisse, an eye lash growing serum for alldaychemist.com, an Indian Pharmacy for CHEAP). My eyelashes are Blonde, as in nearly white so I can really tell on the coverage of a mascara. I will concede, this mascara is probably pretty good if you have short dark eyelashes as I have been using it on my lower eyelashes or as a base coat simply because I have to use it since I bought it. My FAVORITE ALL TIME mascara they no longer sell in stores & I have to buy on Amazon as its only sold in Europe now; Max Factor 2000 Calorie GREAT, Amazing, Truly volumizing, thick coverage, no caking, non-smearing.",
    reviewerCountry: "United States",
    reviewDate: "June 26, 2015",
    reviewScore: 3,
    reviewTitle: "Overrated.",
    reviewer: "AmazonAddict3",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R1D0E76U50LN21",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AFIGHNVVTOUZ4YTDTORVG6DQUDEQ/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B0CRPPCD89",
    "Seller Name": "M Shop",
    HelpfulCounts: 1,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B0CRPPCD89/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=two_star&pageNumber=2",
    "Review ID": "R3NQWR8FE11B7N",
    productLink: "https://www.amazon.com/dp/B0CRPPCD89",
    productTitle:
      "M Mase- Professional Nail Drill Machine - Coreless 2-in-1 Nail File - Electric, Cordless, 35000 RPM, HD Display - Electric Nail Drill for Professional & Home Use (Rose Gold)",
    reviewContent:
      "Tengo poco más de un mes con el taladro y simplemente dejo de funcionar nose que le pasa",
    reviewerCountry: "",
    reviewDate: "Reviewed in Mexico on September 24, 2024",
    reviewScore: 0,
    reviewTitle: "",
    reviewer: "Yessica flores",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R3NQWR8FE11B7N",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B0CRPPCD89",
    "Seller Name": "M Shop",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B0CRPPCD89/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=two_star&pageNumber=3",
    "Review ID": "R1SRZQ42TVN84D",
    productLink: "https://www.amazon.com/dp/B0CRPPCD89",
    productTitle:
      "M Mase- Professional Nail Drill Machine - Coreless 2-in-1 Nail File - Electric, Cordless, 35000 RPM, HD Display - Electric Nail Drill for Professional & Home Use (Rose Gold)",
    reviewContent: "Pues me gusto la estética, pero no funcionaba bien",
    reviewerCountry: "",
    reviewDate: "Reviewed in Mexico on September 22, 2022",
    reviewScore: 0,
    reviewTitle: "",
    reviewer: "Yadira Vázquez Chávez",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R1SRZQ42TVN84D",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07VVYN2PV",
    "Seller Name": "Empava",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07VVYN2PV/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=three_star&pageNumber=2",
    "Review ID": "R172QSDTKZRGMQ",
    productLink: "https://www.amazon.com/dp/B07VVYN2PV",
    productTitle:
      "Empava 36 Inch Electric Stove Induction Cooktop with 5 Power Boost Burners Smooth Surface Vitro Ceramic Glass in Black 240V",
    reviewContent: "Me parece que no funciona correctamente",
    reviewerCountry: "",
    reviewDate: "Reviewed in Mexico on October 20, 2022",
    reviewScore: 0,
    reviewTitle: "",
    reviewer: "Franc",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R172QSDTKZRGMQ",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07VVYN2PV",
    "Seller Name": "Empava",
    HelpfulCounts: 2,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07VVYN2PV/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=4",
    "Review ID": "R1Q679OLSU9F6W",
    productLink: "https://www.amazon.com/dp/B07VVYN2PV",
    productTitle:
      "Empava 36 Inch Electric Stove Induction Cooktop with 5 Power Boost Burners Smooth Surface Vitro Ceramic Glass in Black 240V",
    reviewContent: "Se quemó y no hay garantía, muy mal producto,",
    reviewerCountry: "",
    reviewDate: "Reviewed in Mexico on May 26, 2021",
    reviewScore: 0,
    reviewTitle: "",
    reviewer: "Vianneyt",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R1Q679OLSU9F6W",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07VVYN2PV",
    "Seller Name": "Empava",
    HelpfulCounts: 1,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07VVYN2PV/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=two_star&pageNumber=2",
    "Review ID": "R1FRYSC54SZMEW",
    productLink: "https://www.amazon.com/dp/B07VVYN2PV",
    productTitle:
      "Empava 36 Inch Electric Stove Induction Cooktop with 5 Power Boost Burners Smooth Surface Vitro Ceramic Glass in Black 240V",
    reviewContent: "Fácil de limpiar",
    reviewerCountry: "",
    reviewDate: "Reviewed in Mexico on January 17, 2022",
    reviewScore: 0,
    reviewTitle: "",
    reviewer: "Amy Chávez",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R1FRYSC54SZMEW",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07TSJQLW2",
    "Seller Name": "Empava",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07TSJQLW2/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=three_star&pageNumber=1",
    "Review ID": "RY5TGEE9RN9AZ",
    productLink: "https://www.amazon.com/dp/B07TSJQLW2",
    productTitle:
      'Empava 24" Gas Stove Cooktop 4 Italy Sabaf Sealed Burners NG/LPG Convertible in Stainless Steel, 24 Inch',
    reviewContent:
      "No me duro el año, el vidrio templado estallo mientras concinaba.La compre en mayo del 2020 y solo aguanto hasta abril del 2021.",
    reviewerCountry: "",
    reviewDate: "Reviewed in Mexico on April 15, 2021",
    reviewScore: 0,
    reviewTitle: "",
    reviewer: "Omar Cerón Rodríguez",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/RY5TGEE9RN9AZ",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07VVYN2PV",
    "Seller Name": "Empava",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07VVYN2PV/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=three_star&pageNumber=2",
    "Review ID": "R29YRWW8ZJZ8IR",
    productLink: "https://www.amazon.com/dp/B07VVYN2PV",
    productTitle:
      "Empava 36 Inch Electric Stove Induction Cooktop with 5 Power Boost Burners Smooth Surface Vitro Ceramic Glass in Black 240V",
    reviewContent:
      "Calienta muy bien, muy rápido, el único detalle es que consume mucha energía cuando no hay ninguna parrilla encendida, en modo espera. Yo puse un interruptor general para cuando no esta en uso.",
    reviewerCountry: "",
    reviewDate: "Reviewed in Mexico on April 10, 2023",
    reviewScore: 0,
    reviewTitle: "",
    reviewer: "DAVID M.",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R29YRWW8ZJZ8IR",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B07ST3MCVF",
    "Seller Name": "Newlife Naturals",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B07ST3MCVF/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=two_star&pageNumber=8",
    "Review ID": "R11MMR9V5QNH7E",
    productLink: "https://www.amazon.com/dp/B07ST3MCVF",
    productTitle:
      "NewLife Naturals - Boric Acid Suppositories for Women pH Balance Pills - 600mg - Feminine Care - Vaginal Odor Itching Discharge BV | Made in USA | 30 Capsules",
    reviewContent:
      "I have given these capsules multiple chances on multiple occasions. It seems no matter how far I insert these capsules they somehow end up being wiped away & barely dissolved. I would not buy this brand again",
    reviewerCountry: "",
    reviewDate: "Reviewed in Canada on October 8, 2024",
    reviewScore: 0,
    reviewTitle: "These do not dissolve well",
    reviewer: "Sheridan",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R11MMR9V5QNH7E",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B001976YQM",
    "Seller Name": "Shenzhen Knives",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B001976YQM/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=5",
    "Review ID": "R10WPSGXIUK4MZ",
    productLink: "https://www.amazon.com/dp/B001976YQM",
    productTitle:
      'Shenzhen Knives White Ceramic Knife Set - 3-Piece Kitchen Knives Bundle: 6" Chef\'s, 5" Slicing, and 4" Paring Knife. Lightweight Kitchen Cutlery for Precision Slicing and Cooking',
    reviewContent:
      "Very poor quality , handles peeled in few months , blade started moving right away",
    reviewerCountry: "",
    reviewDate: "Reviewed in Canada on October 7, 2024",
    reviewScore: 0,
    reviewTitle: "Terrible quality",
    reviewer: "Lola Paez",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R10WPSGXIUK4MZ",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
  {
    ASIN: "B071ZDD3HL",
    "Seller Name": "Naked",
    HelpfulCounts: 0,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B071ZDD3HL/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=three_star&pageNumber=10",
    "Review ID": "R3IZT8N8WYLL8J",
    productLink: "https://www.amazon.com/dp/B071ZDD3HL",
    productTitle:
      "NAKED Energy - Pure Pre Workout Powder for Men and Women, Vegan, Unflavored, Healthy Pre Workout - No Added Sweeteners, Colors Or Flavors - 50 Servings",
    reviewContent:
      "Not a really good pre workout for energy but it has no sugar,so it doesn’t break my fasting.",
    reviewerCountry: "",
    reviewDate: "Reviewed in Canada on October 31, 2024",
    reviewScore: 0,
    reviewTitle: "Pre workout",
    reviewer: "Ninjaking3d",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R3IZT8N8WYLL8J",
    reviewerLink: "",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
];

async function writeToJson(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `analysis-results-${timestamp}.json`;
  const filepath = path.join(__dirname, filename);
  
  const jsonData = {
    timestamp: new Date().toISOString(),
    totalReviews: results.length,
    results: results
  };
  
  try {
    await fs.writeFile(filepath, JSON.stringify(jsonData, null, 2));
    console.log(`✅ Results saved to: ${filename}`);
  } catch (error) {
    console.error('❌ Error saving JSON file:', error);
  }
}

async function hey() {
  const results = await analyzeAmazonReviewsWithCompliance(reviewsArray);
  console.log("\n📊 Final Results:");
  console.dir(results, { depth: null, colors: true });
  
  // Write results to JSON with timestamp
  await writeToJson(results);
  // Write results to CSV
  await writeToCsv(results);

}

// hey();
module.exports = { analyzeAmazonReviewsWithCompliance };
