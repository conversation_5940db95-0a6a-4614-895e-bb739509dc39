const { Queue } = require("bullmq");
const prisma = require("../../database/prisma/getPrismaClient");
require("dotenv").config();

const queueName = "mainQueue";

const connection = { url: process.env.UPSTASH_REDIS_URL };

const jobQueue = new Queue(queueName, { connection });

/**
 * Returns the priority level for a given job script type.
 *
 * Job priority determines the execution order in the queue — lower numbers are higher priority.
 *
 * @param {string} scriptType - The type of job script (e.g., "singleJeff", "bulkJeff", "lexAsin").
 * @returns {number} A numeric priority (1 = highest priority, larger = lower priority).
 *
 * Example usage:
 *   const priority = getJobPriority("singleJeff"); // returns 1
 */
function getJobPriority(scriptType) {
  switch (scriptType) {
    case "singleJeff":
      return 1;
    case "singleLexAsin":
      return 2;
    case "singleLexSeller":
      return 3;
    case "bulkJeff":
      return 4;
    case "bulkLexAsin":
      return 5;
    case "singleLexReview":
      return 6;
    case "bulkLexReview":
      return 7;
    default:
      return 8; // fallback priority for unknown types
  }
}

/**
 * Add a job to the main queue with optional repeat interval.
 *
 * @param {string} scriptType - Name/type of the job.
 * @param {Object} data - Payload for the job.
 * @param {Object} options - BullMQ options + custom 'every' (in ms).
 * Example for every 1 minute: { priority: 1, every: 60000 }
 */
async function addToQueue(
  scriptType,
  data = {},
  options = { priority: getJobPriority(scriptType) }
) {
  try {

    const newJobCentralJob = await prisma.jobCentral.create({
      data: {
        scriptType,
        priority: options.priority,
        params: data,
        queueName,
        clientId: data.clientId || 1,
      },
    });

    data.id = newJobCentralJob.id;

    const jobOptions = { ...options };

    // If 'every' (ms) is provided, set BullMQ repeat config
    if (options.every) {
      jobOptions.repeat = { every: options.every };
      delete jobOptions.every; // remove to prevent BullMQ warnings
    }

    await jobQueue.add(scriptType, data, jobOptions);
    console.log(`✅ Job '${scriptType}' added with options:`, jobOptions);
    return newJobCentralJob;
  } catch (error) {
    console.error(`❌ Failed to add job '${scriptType}':`, error);
  }
}

module.exports = { addToQueue, jobQueue, connection };
