const axios = require("axios");
const cheerio = require("cheerio");
const { selectors } = require("../scrapeAmazon/selectors");
const { getHtmlByProxy, getHtmlFromS3 } = require("../../utils/getHtmlByProxy");
const { getAmazonTLD } = require("../../utils/lexUtils/countryCodeMapping");

// Helper function to get base URL by country code
function getBaseUrlByCountry(countryCode = 'US') {
  const tld = getAmazonTLD(countryCode);
  const baseUrl = `https://www.amazon.${tld}`;
  console.log(`Using Amazon ${countryCode} domain: ${baseUrl}`);
  return baseUrl;
}

async function scrapeAsinsFromSellerId(
  sellerId,
  countryCode = "US",
  maxPages = 20,
  clientId = 1
) {
  const baseUrl = getBaseUrlByCountry(countryCode);
  const searchUrl = `${baseUrl}/s?i=merchant-items&me=${sellerId}&page=`;

  const uniqueAsins = new Set();
  const products = [];

  for (let page = 1; page <= maxPages; page++) {
    const url = `${searchUrl}${page}`;
    console.log(`Fetching page ${page} - ${url}`);

    let htmlData;
    try {
      htmlData = await getHtmlByProxy(url, clientId);
    } catch (err) {
      console.warn(`Skipping page ${page} due to fetch error:`, err.message);
      break; // Stop pagination on failure
    }
    const $ = cheerio.load(htmlData);
    let newAsinsFound = 0;

    const sellerName = $("#nav-search-dropdown-card .searchSelect option:first-child").text().trim() || "";

    $(selectors.productContainer).each((_, container) => {
      const asin = $(container).attr("data-asin");
      if (!asin || uniqueAsins.has(asin)) return;

      const title = $(container).find("h2 span").text().trim();
      const productLink = `${baseUrl}/dp/${asin}`;
      const image = $(container).find("img.s-image").attr("src") || "";
      const ratingText = $(container).find(".a-icon-alt").text().trim();
      const avgRating = parseFloat(ratingText.split(" ")[0]) || 0;

      const totalReviews =
        parseInt(
          $(container)
            .find(selectors.numOfRatings)
            .text()
            .replace(/,/g, "")
            .trim()
        ) || 0;

      products.push({
        asin,
        title,
        image,
        productLink,
        avgRating,
        totalReviews,
        sellerName,
      });

      uniqueAsins.add(asin);
      newAsinsFound++;
    });

    if (newAsinsFound === 0) {
      console.log("No new ASINs found, ending pagination early.");
      break;
    }
  }

  return products;
}

// export default scrapeAsinsFromSellerId;

async function Example() {
  try {
    const sellerId = "A3R77EJUKEY6MC"; // example seller ID
    const countryCode = "UK";
    const maxPages = 10;

    const products = await scrapeAsinsFromSellerId(
      sellerId,
      countryCode,
      maxPages,
      1
    );
    console.log({ products });
  } catch (error) {
    console.error("Error during scraping:", error.message);
  }
}

// Example();

module.exports = {scrapeAsinsFromSellerId, getBaseUrlByCountry};