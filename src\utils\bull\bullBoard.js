const express = require("express");
const { ExpressAdapter } = require("@bull-board/express");
const { createBullBoard } = require("@bull-board/api");
const { Queue } = require("bullmq");
const { BullMQAdapter } = require("@bull-board/api/bullMQAdapter");
require("dotenv").config();

const { connection } = require("./connection");

// Create both queues
const mainQueue = new Queue("mainQueue", { connection });
const lexQueue = new Queue("lexQueue", { connection });

// Set up Bull Board
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath("/admin/queues");

createBullBoard({
  queues: [new BullMQAdapter(mainQueue), new BullMQAdapter(lexQueue)],
  serverAdapter,
});

const app = express();
app.use("/admin/queues", serverAdapter.getRouter());

const PORT = 3001;
app.listen(PORT, () => {
  console.log(
    `📊 BullMQ Dashboard running on http://localhost:${PORT}/admin/queues`
  );
});
