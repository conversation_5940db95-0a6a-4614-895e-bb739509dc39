const express = require("express");
const { ExpressAdapter } = require("@bull-board/express");
const { createBullBoard } = require("@bull-board/api");
const { Queue } = require("bullmq");
const { BullMQAdapter } = require("@bull-board/api/bullMQAdapter");
const IORedis = require("ioredis");
const { connection } = require("./bull");
require("dotenv").config();

const queueName = "mainQueue";
const jobQueue = new Queue(queueName, { connection });

const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath("/admin/queues");

const { addQueue, removeQueue, setQueues } = createBullBoard({
  queues: [new BullMQAdapter(jobQueue)],
  serverAdapter,
});

const app = express();
app.use("/admin/queues", serverAdapter.getRouter());

const PORT = 3001;
app.listen(PORT, () => {
  console.log(
    `📊 BullMQ Dashboard running on http://localhost:${PORT}/admin/queues`
  );
});
