const { DefaultAzureCredential } = require("@azure/identity");
const { ComputeManagementClient } = require("@azure/arm-compute");
const { NetworkManagementClient } = require("@azure/arm-network");
const { DnsManagementClient } = require("@azure/arm-dns"); // Added for DNS management
require("dotenv").config();

// Make sure to add these to your .env file

// --- Service Principal Credentials (for local development) ---
// To run this script without `az login`, create a Service Principal and add these variables to your .env file
// process.env.AZURE_TENANT_ID
// process.env.AZURE_CLIENT_ID
// process.env.AZURE_CLIENT_SECRET

const subscriptionId = process.env.AZURE_SUBSCRIPTION_ID;
const resourceGroupName = process.env.RESOURCE_GROUP_NAME;
const vmName = process.env.VM_NAME;
const publicIpName = process.env.PUBLIC_IP_NAME; // Name of the CURRENT Public IP resource
const location = process.env.AZURE_LOCATION; // e.g., "Central India"
const ipConfigName = "ipconfig1"; // Default is usually 'ipconfig1'. Verify in Azure Portal.

// --- DNS Configuration ---
// const dnsZoneName = process.env.DNS_ZONE_NAME; // e.g., "yourdomain.com" - Not used if keeping default Azure DNS

// --- Azure Clients Initialization ---
const credential = new DefaultAzureCredential();
const computeClient = new ComputeManagementClient(credential, subscriptionId);
const networkClient = new NetworkManagementClient(credential, subscriptionId);
const dnsClient = new DnsManagementClient(credential, subscriptionId); // DNS client

// --- VM Actions ---

/**
 * Deallocates the virtual machine. This is required to change network interface settings.
 */
const stopVM = async () => {
  console.log(`Deallocating VM: ${vmName}...`);
  const poller = await computeClient.virtualMachines.beginDeallocate(
    resourceGroupName,
    vmName
  );
  await poller.pollUntilDone();
  console.log("✅ VM deallocated.");
};

/**
 * Starts the virtual machine.
 */
const startVM = async () => {
  console.log(`Starting VM: ${vmName}...`);
  const poller = await computeClient.virtualMachines.beginStart(
    resourceGroupName,
    vmName
  );
  await poller.pollUntilDone();
  console.log("✅ VM started.");
};

// --- Network Actions ---

/**
 * Retrieves the primary Network Interface Card (NIC) name from the VM.
 * @returns {Promise<string>} The name of the NIC.
 */
const getNICName = async () => {
  const vm = await computeClient.virtualMachines.get(resourceGroupName, vmName);
  const nicId = vm.networkProfile.networkInterfaces[0].id;
  const nicName = nicId.split("/").pop();
  return nicName;
};

/**
 * Disassociates the public IP address from the specified NIC.
 * @param {string} nicName - The name of the network interface.
 */
const disassociatePublicIP = async (nicName) => {
  console.log(`Disassociating Public IP from NIC: ${nicName}...`);
  const nic = await networkClient.networkInterfaces.get(
    resourceGroupName,
    nicName
  );

  // Find the IP configuration and set its public IP to null
  const ipConfig = nic.ipConfigurations.find((c) => c.name === ipConfigName);
  if (ipConfig) {
    ipConfig.publicIPAddress = null;
  } else {
    throw new Error(
      `IP configuration '${ipConfigName}' not found on NIC '${nicName}'.`
    );
  }

  const poller = await networkClient.networkInterfaces.beginCreateOrUpdate(
    resourceGroupName,
    nicName,
    nic
  );
  await poller.pollUntilDone();
  console.log("✅ Public IP disassociated.");
};

/**
 * Deletes the specified public IP address resource.
 * @param {string} ipToDelete - The name of the public IP to delete.
 */
const deletePublicIP = async (ipToDelete) => {
  console.log(`Deleting old Public IP: ${ipToDelete}...`);
  const poller = await networkClient.publicIPAddresses.beginDelete(
    resourceGroupName,
    ipToDelete
  );
  await poller.pollUntilDone();
  console.log("✅ Old Public IP deleted.");
};

/**
 * Lists all public IP addresses in the resource group and finds ones matching the specified name.
 * @param {string} namePattern - The name pattern to match (e.g., "lex-review-scraper-ip").
 * @returns {Promise<Array>} Array of matching public IP resources.
 */
const findPublicIPsByName = async (namePattern) => {
  console.log(`Searching for Public IPs matching pattern: ${namePattern}...`);
  const publicIPs = await networkClient.publicIPAddresses.list(
    resourceGroupName
  );
  const matchingIPs = [];

  for await (const ip of publicIPs) {
    if (ip.name.includes(namePattern)) {
      matchingIPs.push(ip);
      console.log(`Found matching IP: ${ip.name} (${ip.ipAddress})`);
    }
  }

  console.log(
    `Found ${matchingIPs.length} Public IPs matching pattern "${namePattern}"`
  );
  return matchingIPs;
};

/**
 * Deletes all public IP addresses that match the specified name pattern.
 * @param {string} namePattern - The name pattern to match and delete.
 */
const deleteAllMatchingPublicIPs = async (namePattern) => {
  console.log(
    `🗑️ Starting deletion of all Public IPs matching: ${namePattern}`
  );

  const matchingIPs = await findPublicIPsByName(namePattern);

  if (matchingIPs.length === 0) {
    console.log(`No Public IPs found matching pattern: ${namePattern}`);
    return;
  }

  // First, check if any of these IPs are currently associated with NICs
  for (const ip of matchingIPs) {
    if (ip.ipConfiguration) {
      console.log(
        `⚠️ Public IP ${ip.name} is currently associated with a NIC. Will disassociate first.`
      );

      // Extract NIC name from the IP configuration ID
      const nicId = ip.ipConfiguration.id;
      const nicName = nicId.split("/").slice(-3, -2)[0]; // Extract NIC name from resource ID

      try {
        await disassociatePublicIP(nicName);
        console.log(`✅ Disassociated ${ip.name} from NIC ${nicName}`);
      } catch (error) {
        console.error(
          `❌ Failed to disassociate ${ip.name} from NIC: ${error.message}`
        );
      }
    }
  }

  // Now delete all matching IPs
  for (const ip of matchingIPs) {
    try {
      await deletePublicIP(ip.name);
      console.log(`✅ Deleted Public IP: ${ip.name}`);
    } catch (error) {
      console.error(
        `❌ Failed to delete Public IP ${ip.name}: ${error.message}`
      );
    }
  }

  console.log(
    `🎯 Completed deletion process for Public IPs matching: ${namePattern}`
  );
};

/**
 * Creates a new Standard SKU, Zonal, Static public IP address.
 * @param {string} newPublicIpName - The name for the new public IP resource.
 * @returns {Promise<object>} The created public IP address object.
 * @param {string|null} domainNameLabel - The DNS domain name label to assign to the public IP.
 */
const createPublicIP = async (newPublicIpName, domainNameLabel = null) => {
  console.log(
    `Creating new Public IP: ${newPublicIpName} in location: ${location}...`
  );
  const vmDetails = await computeClient.virtualMachines.get(
    resourceGroupName,
    vmName
  );
  const zones = vmDetails.zones;

  const params = {
    location: location,
    publicIPAllocationMethod: "Static",
    sku: { name: "Standard" },
  };

  if (domainNameLabel) {
    params.dnsSettings = { domainNameLabel: domainNameLabel };
    console.log(
      `Attempting to use DNS Label: ${domainNameLabel} for new IP ${newPublicIpName}`
    );
  } else {
    console.log(
      `No DNS Label provided for new IP ${newPublicIpName}. Azure default or no FQDN will be set.`
    );
  }

  if (zones && zones.length > 0) {
    params.zones = zones;
    console.log(`Matching VM zones: ${zones.join(", ")}`);
  }

  const poller = await networkClient.publicIPAddresses.beginCreateOrUpdate(
    resourceGroupName,
    newPublicIpName,
    params
  );
  const result = await poller.pollUntilDone();
  console.log("✅ New Public IP created.");
  return result;
};

/**
 * Associates a public IP with a NIC by updating the NIC's IP configuration.
 * @param {string} nicName - The name of the network interface.
 * @param {string} newPublicIpId - The full resource ID of the new public IP.
 */
const associatePublicIPWithNIC = async (nicName, newPublicIpId) => {
  console.log(`Associating new Public IP with NIC: ${nicName}...`);
  const nic = await networkClient.networkInterfaces.get(
    resourceGroupName,
    nicName
  );

  // Find the IP configuration and update its public IP
  const ipConfig = nic.ipConfigurations.find((c) => c.name === ipConfigName);
  if (ipConfig) {
    ipConfig.publicIPAddress = { id: newPublicIpId };
  } else {
    throw new Error(
      `IP configuration '${ipConfigName}' not found on NIC '${nicName}'.`
    );
  }

  const poller = await networkClient.networkInterfaces.beginCreateOrUpdate(
    resourceGroupName,
    nicName,
    nic
  );
  await poller.pollUntilDone();
  console.log("✅ NIC updated with new Public IP.");
};

/**
 * Gets the IP address string from a public IP resource.
 * @param {string} ipName - The name of the public IP resource.
 * @returns {Promise<string>} The public IP address string.
 */
const getPublicIPAddress = async (ipName) => {
  const publicIP = await networkClient.publicIPAddresses.get(
    resourceGroupName,
    ipName
  );
  console.log(`Retrieved IP Address: ${publicIP.ipAddress}`);
  return publicIP.ipAddress;
};

// --- DNS Function ---

/**
 * Creates or updates an 'A' record in a specified DNS Zone to point to the new IP.
 * @param {string} ipAddress - The new IPv4 address for the 'A' record.
 * @param {string} aRecordName - The name of the 'A' record to create (e.g., 'vm-123').
 */
const updateDnsARecord = async (ipAddress, aRecordName) => {
  console.log(
    `Updating DNS 'A' record '${aRecordName}.${dnsZoneName}' to point to ${ipAddress}...`
  );
  await dnsClient.recordSets.createOrUpdate(
    resourceGroupName,
    dnsZoneName,
    aRecordName,
    "A", // Record type
    {
      ttl: 300, // Time-to-live in seconds
      aRecords: [{ ipv4Address: ipAddress }],
    }
  );
  console.log("✅ DNS 'A' record updated successfully.");
};

// --- Main Execution Flow ---
const main = async () => {
  const startTime = Date.now();
  console.log("--- Starting VM IP and DNS Update Process ---");

  // Fixed name for the new IP
  let newIpAddressString; // To store the actual new IP address
  let fqdn; // To store the FQDN of the new IP

  try {
    // Stop the VM
    await stopVM();

    // --- Network Operations ---
    const nicName = await getNICName();
    console.log(`Retrieved NIC Name: ${nicName}`);

    // Delete ALL existing public IPs with the name "lex-review-scraper-ip"
    await deleteAllMatchingPublicIPs(publicIpName);

    // Use the fixed name for the new public IP resource
    const hardcodedDomainNameLabel = "lex-reviewer-server";
    console.log(`Using hardcoded DNS Label: ${hardcodedDomainNameLabel}`);
    console.log(`Creating new Public IP with fixed name: ${publicIpName}`);

    // Create the new public IP with the exact name "lex-review-scraper-ip"
    const newPublicIpObject = await createPublicIP(
      publicIpName,
      hardcodedDomainNameLabel
    );
    await associatePublicIPWithNIC(nicName, newPublicIpObject.id);

    // Start the VM
    await startVM();

    console.log(
      "Waiting 30 seconds for Azure to fully assign the new IP and DNS to propagate..."
    );
    await new Promise((resolve) => setTimeout(resolve, 30000));

    // Get the new IP address string and FQDN for verification
    const finalPublicIp = await networkClient.publicIPAddresses.get(
      resourceGroupName,
      publicIpName
    );
    newIpAddressString = finalPublicIp.ipAddress;
    fqdn = finalPublicIp.dnsSettings?.fqdn;

    console.log(`New IP Address: ${newIpAddressString}`);
    if (fqdn) {
      console.log(
        `FQDN (this should use the old DNS label if it was successfully reused): ${fqdn}`
      );
    } else {
      console.log(
        "No FQDN configured for the new IP. This might happen if the DNS label was not set or could not be reused."
      );
    }
    // Custom DNS A record update is removed as per request to use default Azure DNS.
  } catch (error) {
    console.error("❌ An error occurred during the process:", error.message);
    if (error.stack) {
      console.error(error.stack);
    }
  } finally {
    console.log("\n--- Process Summary ---");
    console.log(`New Azure Public IP Resource Name: ${publicIpName}`);
    console.log(`✅ The Public IP name is now fixed as: ${publicIpName}`);
    console.log(
      `📝 Update PUBLIC_IP_NAME in your .env file to: ${publicIpName}`
    );
    if (newIpAddressString) {
      console.log(`New IP Address: ${newIpAddressString}`);
    }
    if (fqdn) {
      console.log(`Associated FQDN: ${fqdn}`);
    }
    const endTime = Date.now();
    const durationInSeconds = ((endTime - startTime) / 1000).toFixed(2);
    console.log(`Process finished in ${durationInSeconds} seconds.`);
  }
};
module.exports = main;
if (require.main === module) {
  main();
}
