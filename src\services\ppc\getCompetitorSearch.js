const getTargetURL = require("../../utils/multicountry/getTargetURL");
const { closePages } = require("../../utils/puppeteer/browserHelper");
const connectPuppeteer = require("../../utils/puppeteer/index");
const { uploadImage, getS3Url } = require("../aws/s3");
const { selectors } = require("../scrapeAmazon/selectors");
const fuzzysort = require("fuzzysort");
const fs = require('fs');
const path = require('path');
const IMAGE_FOLDER = "pdptest/";
async function getCompetitorSearch(
  asin,
  brand,
  company_id,
  TARGET_URL,
  searchTerm,
  file_name = "competitor",
  prospectBrand,
  sellerName
) {
  let browser;
  let page;
  try {
    if (!TARGET_URL) {
      TARGET_URL = await getTargetURL(company_id);
    }
    ({ browser, page } = await connectPuppeteer(
      `${TARGET_URL}/s?k=${searchTerm}`,
      undefined,
      company_id
    ));
    // await page.waitForSelector(selectors.productTitleProductPage, { timeout: 600000 });

    // Save the HTML content of the page
    // const htmlContent = await page.content();
    // const htmlFilePath = path.join(
    //   __dirname,
    //   `${file_name}_${brand}_01_search.html`
    // );
    // fs.writeFileSync(htmlFilePath, htmlContent, "utf-8");
    // console.log(`HTML saved to ${htmlFilePath}`);
    await page.evaluate(() => {
      const input = document.querySelector("#twotabsearchtextbox");
      const div = document.createElement("div");
      div.style.cssText = `
                    position: absolute;
                    visibility: hidden;
                    height: 0;
                    width: 0;
                    overflow: scroll;
                    white-space: pre;
                    height:${window.getComputedStyle(input).height};    
                    font-size: ${window.getComputedStyle(input).fontSize};
                `;
      document.body.appendChild(div);
      window.invisibleDiv = div;
      // Create red border element
    });
    // await page.click("#twotabsearchtextbox");
    // await page.type("#twotabsearchtextbox", searchTerm);
    const divWidth = await page.evaluate((searchTerm) => {
      window.invisibleDiv.textContent += searchTerm;
      const input = document.querySelector("#twotabsearchtextbox");
      const inputWidth = input.offsetWidth;
      const divWidth = Math.min(window.invisibleDiv.scrollWidth, inputWidth);
      return divWidth;
    }, searchTerm);
    // try {
    //   await page.waitForNavigation({
    //     waitUntil: "networkidle0",
    //     timeout: 30000,
    //   });
    // } catch (e) {
    //   console.log("error in navigation");
    // }
    await page.evaluate((divWidth) => {
      const input = document.querySelector("#twotabsearchtextbox");
      const redBorder = document.createElement("div");
      const inputRect = input.getBoundingClientRect();
      redBorder.style.cssText = `
                position: absolute;
                top: ${inputRect.top + 3}px;
                left: ${inputRect.left + 3}px;
                height: 32px;
                width: ${divWidth}px;
                border:2px solid red;
                z-index: 9999999999;
            `;
      document.body.appendChild(redBorder);
    }, divWidth);
    const sponsoredElements = await page.evaluate((brand) => {
      const topSponsoredBanners = Array.from(
        document.querySelectorAll(".AdHolder.s-flex-full-width")
      );
      const sponsoredBanners = Array.from(
        document.querySelectorAll('[data-component-type="s-sponsored-banner"]')
      );
      const sponsoredSections = Array.from(
        document.querySelectorAll('[data-component-type="s-search-result"]')
      ).filter((el) =>
        el.querySelector('[data-component-type="s-sponsored-label-info"]')
      );
      const sponsoredProducts = Array.from(
        document.querySelectorAll(
          '[data-component-type="s-impression-counter"]'
        )
      ).filter((el) => el.querySelector(".puis-sponsored-label-text"));
      const sponsoredCarousels = Array.from(
        document.querySelectorAll(".s-widget-container")
      )
        .filter((el) => el.querySelector(".s-widget-sponsored-label-text"))
        .filter((el) =>
          el.querySelector('[data-component-type="s-impression-counter"]')
        );
      const sponsoredBanners3 = Array.from(
        document.querySelectorAll(".sb-video-creative")
      ).filter((el) => {
        return !el
          .querySelector('[data-type="productTitle"] .a-link-normal')
          ?.textContent?.toLowerCase()
          ?.includes(brand?.toLowerCase());
      });
      const sponsoredBanners2 = Array.from(
        document.querySelectorAll(
          '[data-component-type="sbv-video-single-product"]'
        )
      ).filter((el) => {
        const componentProps = JSON.parse(el?.dataset?.componentProps);
        if (componentProps?.videoType == "sponsored") {
          if (
            Array.from(
              el?.querySelectorAll(
                "span.a-size-medium.a-color-base.a-text-normal,span.a-size-base-plus.a-color-base"
              )
            ).some((span) =>
              span.textContent.toLowerCase().includes(brand.toLowerCase())
            )
          ) {
            return false;
          }
          return true;
        }
      });
      return [
        ...topSponsoredBanners,
        ...sponsoredBanners,
        ...sponsoredSections,
        ...sponsoredProducts,
        ...sponsoredCarousels,
        ...sponsoredBanners2,
        ...sponsoredBanners3,
      ].map((el) => {
        const rect = el.getBoundingClientRect();
        // const randomExpansion = Math.random() * 5 + 1; // Random value between 1 and 6
        const differentSelectors = `.a-size-base-plus.a-color-base.a-text-normal,span.a-size-medium.a-color-base.a-text-normal,span.a-size-base-plus.a-color-base,span.a-size-medium.a-color-base,.a-size-medium.a-spacing-none.a-color-base.a-text-normal,.AdHolder.s-flex-full-width`;
        let productTitle = [];

        if (
          el.classList.contains("AdHolder") &&
          el.classList.contains("s-flex-full-width")
        ) {
          // Target span elements with class "a-truncate" and get their text
          const truncateSpans = el.querySelectorAll("span.a-truncate");
          productTitle = Array.from(truncateSpans)
            .map((span) => span.innerText?.trim()?.toLowerCase())
            .filter((text) => text);
        } else {
          // Original logic for other elements
          productTitle = Array.from(
            el.querySelectorAll(differentSelectors)
          )?.map((el) => el.textContent?.toLowerCase() || "");
        }
        // const productTitle = Array.from(
        //   el.querySelectorAll(
        //     `[data-component-type="s-impression-counter"] ${differentSelectors},${differentSelectors} `
        //   ) || []
        // )?.map((el) => el.textContent?.toLowerCase() || "");
        return {
          x: rect.x,
          y: rect.y,
          productTitle,
          width: rect.width,
          height: rect.height,
          containsBrand: false,
          element: el,
        };
      });
    }, brand);

    //Check to match compBrand(brand) name
    sponsoredElements.forEach((el) => {
      // console.log(`in sponsoredElement title is: ${el.productTitle}`);
      const containsBrand = el.productTitle.some((title) => {
        if (!title || title.length < 3) return false;
        const cleanTitle = title.toLowerCase().trim();
        const cleanBrand = brand.toLowerCase().trim();

        // Direct match
        if (cleanTitle === cleanBrand) return true;
        if (cleanTitle.includes(cleanBrand)) return true;


        const brandWords = cleanBrand
          .split(/[\s-]+/)
          .filter((word) => word.length > 3);
        const titleWords = cleanTitle.split(/[\s-]+/);

        const significantMatch = brandWords.every((brandWord) =>
          titleWords.some(
            (titleWord) =>
              titleWord === brandWord ||
              (titleWord.length > 4 && titleWord.includes(brandWord))
          )
        );

        if (significantMatch) {
          const fuzzyScore = fuzzysort.single(cleanBrand, cleanTitle)?.score;
          return fuzzyScore && fuzzyScore > 0.8;
        }

        return false;
      });
      // console.log({
      //   titles: el.productTitle,
      //   brand: brand,
      //   matches: containsBrand,
      // });

      el.containsBrand = containsBrand;
    });

    //Check for prospect brand and seller name together
    sponsoredElements.forEach((el) => {
      let containsProspectBrandOrSeller = false;

      // Check for prospect brand if it's not empty
      if (prospectBrand && prospectBrand.trim()) {
        const brandCheck = el.productTitle.some((text) => {
          if (!text || text.length < 3) return false;
          const cleanText = text.toLowerCase().trim();
          const cleanBrand = prospectBrand.toLowerCase().trim();

          if (cleanText === cleanBrand) return true;

          const brandWords = cleanBrand
            .split(/[\s-]+/)
            .filter((word) => word.length > 3);
          const textWords = cleanText.split(/[\s-]+/);

          const significantMatch = brandWords.every((brandWord) =>
            textWords.some(
              (textWord) =>
                textWord === brandWord ||
                (textWord.length > 4 && textWord.includes(brandWord))
            )
          );

          if (significantMatch) {
            const fuzzyScore = fuzzysort.single(cleanBrand, cleanText)?.score;
            return fuzzyScore !== undefined && fuzzyScore > -50;
          }

          return false;
        });

        if (brandCheck) {
          containsProspectBrandOrSeller = true;
        }
      }

      // Check for seller if it's not empty and no brand match found
      if (!containsProspectBrandOrSeller && sellerName && sellerName.trim()) {
        const sellerCheck = el.productTitle.some((text) => {
          if (!text || text.length < 3) return false;
          const cleanText = text.toLowerCase().trim();
          const cleanSeller = sellerName.toLowerCase().trim();

          if (cleanText === cleanSeller) return true;

          const sellerWords = cleanSeller
            .split(/[\s-]+/)
            .filter((word) => word.length > 3);
          const textWords = cleanText.split(/[\s-]+/);

          const significantMatch = sellerWords.every((sellerWord) =>
            textWords.some(
              (textWord) =>
                textWord === sellerWord ||
                (textWord.length > 4 && textWord.includes(sellerWord))
            )
          );

          if (significantMatch) {
            const fuzzyScore = fuzzysort.single(cleanSeller, cleanText)?.score;
            return fuzzyScore !== undefined && fuzzyScore > -50;
          }

          return false;
        });

        if (sellerCheck) {
          containsProspectBrandOrSeller = true;
        }
      }
      // console.log({
      //   titles: el.productTitle,
      //   brand: prospectBrand,
      //   seller: sellerName,
      //   matches: containsProspectBrandOrSeller,
      // });

      el.containsProspectBrand = containsProspectBrandOrSeller; // Using the same property name for compatibility
    });

    // Draw individual boxes for all sponsored elements
    for (const element of sponsoredElements) {
      if (element.containsProspectBrand) {
        // console.log("element have containsProspectBrand: ", element);
        continue;
      }
      await page.evaluate((el) => {
        const div = document.createElement("div");
        div.style.position = "absolute";
        div.style.left = `${el.x}px`;
        div.style.top = `${el.y}px`;
        div.style.width = `${el.width}px`;
        div.style.height = `${el.height}px`;
        div.style.border = "2px solid red";
        div.style.boxSizing = "border-box";
        div.style.pointerEvents = "none";
        div.style.zIndex = "9999";
        document.body.appendChild(div);
      }, element);
    }
    const screenshotWithHeader = await page.screenshot({
      clip: {
        x: 0,
        y: 0,
        width: await page.evaluate(() => document.documentElement.clientWidth),
        height: 1500,
      },
      encoding: "binary",
    });
    const image_path = `${file_name}_search_${asin}_.png`;
    await uploadImage(screenshotWithHeader, image_path, IMAGE_FOLDER);
    // groupScreenshotsWithHeader.push(getS3Url(image_path, IMAGE_FOLDER));
    const fullPageScreenshot = await page.screenshot({
      fullPage: true,
      encoding: "binary",
    });
    const full_image_path = `${file_name}_${asin}_search_full_page_result.png`;
    const fullImageLink = getS3Url(full_image_path, IMAGE_FOLDER);
    await uploadImage(fullPageScreenshot, full_image_path, IMAGE_FOLDER);
    await page.evaluate(() => {
      const div = document.body.lastElementChild;
      if (div && div.style.border === "2px solid red") {
        div.remove();
      }
    });
    const sponsoredCount = sponsoredElements.length;
    const hasSponsored = sponsoredCount > 0;
    const prospectBrandItems =
      sponsoredElements?.filter(
        (el) => el.y <= 1500 && el.containsProspectBrand
      ) || [];
    const containsProspectBrand = prospectBrandItems.length > 0;
    const competitorBrandItems =
      sponsoredElements?.filter((el) => el.y <= 1500 && el.containsBrand) || [];
    const allCompetitorsListings = competitorBrandItems?.length;
    const allOtherBrandExceptCompetitor =
      sponsoredElements?.filter((el) => el.y <= 1500 && !el.containsBrand) ||
      [];
    const containsOnlyCompetitor =
      !containsProspectBrand && allCompetitorsListings > 0;

    const text = containsOnlyCompetitor
      ? `${brand} as very strong sponsored defense campaigns and no competitors are able to target their branded keywords. You should also do that!`
      : !containsProspectBrand
      ? `Youre not targeting the ${brand} while other competitors are at the top and stealing their traffic.`
      : "You have some products in the competitor listing";
    // console.log({
    //   count: sponsoredCount,
    //   hasSponsored,
    //   isSponsoredInFirstRow: allOtherBrandExceptCompetitor.length > 0,
    //   sponsoredElements: allOtherBrandExceptCompetitor?.length,
    //   totalSponsoredElements: sponsoredElements,
    //   containsProspectBrand: containsProspectBrand,
    //   containsOnlyCompetitor: containsOnlyCompetitor,
    //   images: {
    //     groupScreenshotsWithHeader: getS3Url(image_path, IMAGE_FOLDER),
    //     fullPage: fullImageLink,
    //   },
    //   text: text,
    // })
    return {
      count: sponsoredCount,
      hasSponsored,
      isSponsoredInFirstRow: allOtherBrandExceptCompetitor.length > 0,
      sponsoredElements: allOtherBrandExceptCompetitor?.length,
      totalSponsoredElements: sponsoredElements,
      containsProspectBrand: containsProspectBrand, // true if prospect sponsored comes in comp search
      containsOnlyCompetitor: containsOnlyCompetitor, // true only in case all are sponsored by comp
      images: {
        groupScreenshotsWithHeader: getS3Url(image_path, IMAGE_FOLDER),
        fullPage: fullImageLink,
      },
      text: text,
    };
  } catch (error) {
    console.error("An error occurred:", error);
    throw error;
  } finally {
    await closePages(browser);
  }
}
module.exports = getCompetitorSearch;

async function Example() {
  try {
    const asin = "B0859MV1V5";
    const brand = "roborock"; //CompetitorBrand
    const company_id = 20;
    const TARGET_URL = "https://www.amazon.com";
    const searchTerm = "roborock";
    const file_name = "CompSearch";
    const prospectBrand = "dreame";
    const prospectSeller = "dreame";
    const response = await getCompetitorSearch(
      asin,
      brand,
      company_id,
      TARGET_URL,
      searchTerm,
      file_name,
      prospectBrand,
      prospectSeller
    );
    console.log({ response });
  } catch (error) {
    console.error("An error occurred:", error);
    throw error;
  }
}

// Example();
