function getImageReport(data, report) {
  if (!data.productData || !data.productData[0].images) {
    return;
  }
  // console.log("Images: ", data.productData[0].images);
  const imagesCount = data.productData[0].images.noOfImages;
  const videosCount = data.productData[0].images.noOfVideos;

  // Check if images are less than 7 or 6 with a video
  if (
    imagesCount < 6 ||
    (imagesCount === 5 && (videosCount || videosCount > 0))
  ) {
    report.push({
      DATA_POINT: "Images",
      PRIORITY: "Urgent",
      Logic: imagesCount < 6 ? "< 6 images" : "5 images without video",
      PAIN_POINT: `There are only ${imagesCount} images on your listing right now.`,
      Improvements: [
        "You should have at least 6 images including different angles, lifestyle images & infographics.",
        "This helps the consumers understand your product better and leads to higher conversions.",
      ],
      Benefits: ["CVR ↑"],
    });
  }
}

module.exports = getImageReport;
