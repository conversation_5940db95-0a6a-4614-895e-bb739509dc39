const prisma = require("../database/prisma/getPrismaClient");
const cron = require("node-cron");
const { JobStatus, ScrapingStatus, ReviewStatus } = require("@prisma/client");
const {
  scrapeAmazonReviews,
} = require("../services/lex/scrapeReviewsFromAsins");
const {
  analyzeAmazonReviewsWithCompliance,
} = require("../services/lex/lexReviewPromptProcessor");
const { sendMessageToSlack } = require("../utils/slack");
require("dotenv").config();

// Global flags to track processing
let isProcessingReviewJobs = false;

// Only start the worker if this is the Main server
if (process.env.SERVER_ID === "Main") {
  console.log("Starting LEX Reviews Worker on Main server");

  // Process review jobs every 3 minutes
  cron.schedule("*/3 * * * *", () => {
    processLexReviewJobs();
  });

  // Initial run
  setTimeout(() => {
    processLexReviewJobs();
  }, 10000); // Start after 10 seconds to avoid conflicts
} else {
  console.log("Not running LEX Reviews Worker - Not Main server");
}

/**
 * Process pending LEX review jobs
 */
async function processLexReviewJobs() {
  if (isProcessingReviewJobs) {
    console.log("LEX review jobs already being processed, skipping...");
    return;
  }

  try {
    isProcessingReviewJobs = true;
    console.log("🔍 Processing LEX Review Jobs...");

    // Get all pending review jobs
    const pendingJobs = await prisma.lexJob.findMany({
      where: {
        status: JobStatus.PENDING,
        type: { in: ["SINGLE_REVIEW", "BULK_REVIEW"] },
      },
      orderBy: { createdAt: "asc" },
      include: {
        asins: {
          select: {
            id: true,
            asin: true,
            title: true,
          },
        },
      },
    });

    if (!pendingJobs || pendingJobs.length === 0) {
      console.log("📭 No pending LEX review jobs found");
      return;
    }

    console.log(
      `🚀 Found ${pendingJobs.length} pending review jobs to process`
    );

    // Process each job one by one
    for (let i = 0; i < pendingJobs.length; i++) {
      const job = pendingJobs[i];
      console.log(
        `📦 Processing review job ${i + 1}/${pendingJobs.length}: ${job.name}`
      );

      await processReviewJobSafely(job);

      // Wait between jobs to avoid rate limiting
      if (i < pendingJobs.length - 1) {
        console.log("⏳ Waiting 30 seconds before next job...");
        await new Promise((resolve) => setTimeout(resolve, 30000));
      }
    }

    console.log(
      `✅ Completed processing all ${pendingJobs.length} review jobs`
    );
  } catch (error) {
    console.error("❌ Error in processLexReviewJobs:", error);
  } finally {
    isProcessingReviewJobs = false;
  }
}

/**
 * Safely process a single review job with error handling
 */
async function processReviewJobSafely(job) {
  try {
    console.log(`🚀 Starting ${job.type} Job: ${job.name} (ID: ${job.id})`);

    // Update job status to IN_PROGRESS
    await prisma.lexJob.update({
      where: { id: job.id },
      data: {
        status: JobStatus.IN_PROGRESS,
        updatedAt: new Date(),
      },
    });

    // Route to appropriate processor based on job type
    switch (job.type) {
      case "SINGLE_REVIEW":
        await processSingleReviewJob(job);
        break;
      case "BULK_REVIEW":
        await processBulkReviewJob(job);
        break;
      default:
        throw new Error(`Unknown review job type: ${job.type}`);
    }

    console.log(`✅ Successfully completed ${job.type} Job: ${job.name}`);
  } catch (error) {
    console.error(`❌ Error processing ${job.type} Job ${job.name}:`, error);

    try {
      // Mark job as failed
      await prisma.lexJob.update({
        where: { id: job.id },
        data: {
          status: JobStatus.FAILED,
          errorMessage: error.message,
          updatedAt: new Date(),
        },
      });
    } catch (updateError) {
      console.error(`❌ Error updating failed job ${job.id}:`, updateError);
    }
  }
}

/**
 * Get active cookies from database and parse them
 */
async function getActiveCookies() {
  try {
    const activeCookies = await prisma.lexReviewScraperCookies.findMany({
      where: {
        cookieStatus: "ACTIVE",
        active: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    if (!activeCookies || activeCookies.length === 0) {
      console.log("No active cookies found in database");
      return [];
    }

    // Return the first available cookies parsed as array
    const cookieRecord = activeCookies[0];
    console.log(`Using cookies from email: ${cookieRecord.emailId}`);

    try {
      const cookieArray = JSON.parse(cookieRecord.cookieKey);

      if (Array.isArray(cookieArray) && cookieArray.length > 0) {
        console.log(`Loaded ${cookieArray.length} cookies from database`);
        return {
          cookies: cookieArray,
          emailId: cookieRecord.emailId,
          cookieRecord: cookieRecord,
        };
      } else {
        console.log("Cookie array is empty or invalid");
        return [];
      }
    } catch (parseError) {
      console.error("Error parsing cookie JSON from database:", parseError);
      return [];
    }
  } catch (error) {
    console.error("Error fetching cookies from database:", error);
    return [];
  }
}

/**
 * Process a single review job with two-phase approach
 */
async function processSingleReviewJob(job) {
  console.log(`🕷️ Scraping reviews for single ASIN: ${job.asin}`);

  // Get ASIN record for brand info and review count check
  const asinRecord = await prisma.lexASIN.findUnique({
    where: { asin: job.asin },
    include: {
      seller: {
        select: {
          name: true,
          sellerId: true,
        },
      },
    },
  });

  if (!asinRecord) {
    throw new Error(`ASIN record not found: ${job.asin}`);
  }

  // Check if ASIN has reviews before proceeding with scraping
  if (asinRecord.totalReviews === 0 || asinRecord.totalReviews === null) {
    console.log(`⚠️ ASIN ${job.asin} has 0 reviews, skipping scraping`);

    // Mark job as completed with 0 reviews
    await prisma.lexJob.update({
      where: { id: job.id },
      data: {
        status: JobStatus.COMPLETED,
        totalReviews: 0,
        updatedAt: new Date(),
      },
    });

    // Update ASIN status to SCRAPED since we confirmed it has no reviews
    await prisma.lexASIN.update({
      where: { asin: job.asin },
      data: {
        status: ScrapingStatus.SCRAPED,
        updatedAt: new Date(),
      },
    });

    console.log(
      `✅ Single Review Job ${job.name} completed - ASIN has no reviews to scrape`
    );
    return;
  }

  // Get active cookies
  const activeCookieData = await getActiveCookies();
  if (
    !activeCookieData ||
    !activeCookieData.cookies ||
    activeCookieData.cookies.length === 0
  ) {
    throw new Error("No active cookies available for scraping");
  }

  const brand = asinRecord.seller?.name || asinRecord.seller?.sellerId || "";
  const cookies = activeCookieData.cookies;
  const sortByMostRecent =
    job.sortByMostRecent !== undefined ? job.sortByMostRecent : true;

  try {
    // ONLY PHASE 1: Scrape reviews and save to database
    const reviewResults = await scrapeAmazonReviews(
      job.asin,
      cookies,
      brand,
      sortByMostRecent
    );

    if (!reviewResults.success) {
      // Check if it's a login form or cookie expiration error
      if (
        reviewResults.error &&
        (reviewResults.error.includes("LOGIN_FORM_DETECTED") ||
          reviewResults.error.includes("cookies expired"))
      ) {
        console.log(
          `🔐 Authentication failure detected - marking cookies as expired for email: ${activeCookieData.emailId}`
        );

        await prisma.lexReviewScraperCookies.updateMany({
          where: {
            emailId: activeCookieData.emailId,
          },
          data: {
            cookieStatus: "EXPIRE",
            active: false,
            updatedAt: new Date(),
          },
        });

        throw new Error(
          `Chrome reload page error - cookies expired: ${reviewResults.error}`
        );
      }

      throw new Error(`Review scraping failed: ${reviewResults.error}`);
    }

    console.log(
      `📥 Saving ${reviewResults.reviews.length} scraped reviews to database...`
    );

    let savedCount = 0;
    let failedCount = 0;

    // Parse review date to DateTime
    const parseReviewDate = (dateString) => {
      try {
        if (!dateString) return null;

        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          const parsedDate = Date.parse(dateString);
          return isNaN(parsedDate) ? null : new Date(parsedDate);
        }
        return date;
      } catch (error) {
        console.error(`Error parsing date "${dateString}":`, error);
        return null;
      }
    };

    // Save reviews to database with DATA_SCRAPED status
    for (const reviewData of reviewResults.reviews) {
      try {
        await prisma.lexReview.upsert({
          where: { reviewID: reviewData["Review ID"] },
          update: {
            reviewContent: reviewData.reviewContent,
            reviewTitle: reviewData["Review Title"],
            reviewScore: reviewData.reviewScore,
            reviewDate: parseReviewDate(reviewData.reviewDate),
            reviewer: reviewData.reviewer,
            reviewerCountry: reviewData.reviewerCountry,
            reviewerID: reviewData.reviewerID,
            isVerified: reviewData.isVerified === "True" ? true : false,
            reviewLink: reviewData["Review URL"],
            reviewerLink: reviewData.reviewerLink,
            HelpfulCounts: reviewData.HelpfulCounts,
            reviewImage: null,
            image1: reviewData.image1,
            image2: reviewData.image2,
            image3: reviewData.image3,
            image4: reviewData.image4,
            pageUrl: reviewData.pageUrl,
            sellerId: asinRecord.seller?.sellerId || null,
            productTitle: reviewData.productTitle,
            productLink: reviewData.productLink,
            variant_0: reviewData.variant_0,
            variant_1: reviewData.variant_1,
            status: ReviewStatus.DATA_SCRAPED,
            updatedAt: new Date(),
          },
          create: {
            reviewID: reviewData["Review ID"],
            reviewContent: reviewData.reviewContent,
            reviewTitle: reviewData["Review Title"],
            reviewScore: reviewData.reviewScore,
            reviewDate: parseReviewDate(reviewData.reviewDate),
            reviewer: reviewData.reviewer,
            reviewerCountry: reviewData.reviewerCountry,
            reviewerID: reviewData.reviewerID,
            isVerified: reviewData.isVerified === "True" ? true : false,
            reviewLink: reviewData["Review URL"],
            reviewerLink: reviewData.reviewerLink,
            HelpfulCounts: reviewData.HelpfulCounts,
            reviewImage: null,
            image1: reviewData.image1,
            image2: reviewData.image2,
            image3: reviewData.image3,
            image4: reviewData.image4,
            pageUrl: reviewData.pageUrl,
            sellerId: asinRecord.seller?.sellerId || null,
            productTitle: reviewData.productTitle,
            productLink: reviewData.productLink,
            variant_0: reviewData.variant_0,
            variant_1: reviewData.variant_1,
            status: ReviewStatus.DATA_SCRAPED,

            asinRef: {
              connect: { asin: reviewData.ASIN },
            },

            // job: {
            //   connect: { id: job.id }
            // }
          },
        });
        savedCount++;
      } catch (reviewError) {
        console.error(
          `❌ Failed to save review ${reviewData["Review ID"]}:`,
          reviewError
        );
        failedCount++;
      }
    }

    console.log(
      `✅ Scraping Complete: Saved ${savedCount} reviews with DATA_SCRAPED status`
    );

    // Mark job as completed (scraping phase done)
    await prisma.lexJob.update({
      where: { id: job.id },
      data: {
        status: JobStatus.COMPLETED,
        totalReviews: savedCount,
        errorMessage:
          failedCount > 0 ? `Failed to save ${failedCount} reviews` : null,
        updatedAt: new Date(),
      },
    });

    // Update ASIN status to SCRAPED
    await prisma.lexASIN.update({
      where: { asin: job.asin },
      data: {
        status: ScrapingStatus.SCRAPED,
        updatedAt: new Date(),
      },
    });

    console.log(`✅ Single Review Job ${job.name} completed successfully!`);
    console.log(`   📊 Total reviews scraped: ${reviewResults.totalReviews}`);
    console.log(`   💾 Reviews saved: ${savedCount}`);
    console.log(`   ❌ Total failures: ${failedCount}`);
    console.log(`   📝 ASIN ${job.asin} status updated to SCRAPED`);
  } catch (error) {
    // Check if it's a cookie expiration error
    if (
      error.message.includes("cookies expired") ||
      error.message.includes("Chrome reload page error")
    ) {
      console.error(`🔐 Cookie Expiration Error: ${error.message}`);

      // Mark ASIN as failed due to cookie expiration
      try {
        await prisma.lexASIN.update({
          where: { asin: job.asin },
          data: {
            status: ScrapingStatus.FAILED,
            updatedAt: new Date(),
          },
        });
      } catch (asinUpdateError) {
        console.error(
          `❌ Error updating ASIN status to FAILED:`,
          asinUpdateError
        );
      }

      throw error;
    }

    // Mark ASIN as failed for other errors
    try {
      await prisma.lexASIN.update({
        where: { asin: job.asin },
        data: {
          status: ScrapingStatus.FAILED,
          updatedAt: new Date(),
        },
      });
    } catch (asinUpdateError) {
      console.error(
        `❌ Error updating ASIN status to FAILED:`,
        asinUpdateError
      );
    }

    throw error;
  }
}

/**
 * Process a bulk review job
 */
async function processBulkReviewJob(job) {
  console.log(`🕷️ Processing bulk review job: ${job.name}`);

  if (!job.asins || job.asins.length === 0) {
    console.log(`ℹ️ No ASINs found for bulk review job ${job.name}`);

    await prisma.lexJob.update({
      where: { id: job.id },
      data: {
        status: JobStatus.COMPLETED,
        totalReviews: 0,
        updatedAt: new Date(),
      },
    });

    console.log(
      `✅ Bulk Review Job ${job.name} completed - no ASINs to process`
    );
    return;
  }

  // Get active cookies
  const activeCookieData = await getActiveCookies();
  if (
    !activeCookieData ||
    !activeCookieData.cookies ||
    activeCookieData.cookies.length === 0
  ) {
    throw new Error("No active cookies available for scraping");
  }

  let totalReviewsSaved = 0;
  let successfulAsins = 0;
  let failedAsins = 0;
  let cookiesExpired = false;

  const sortByMostRecent =
    job.sortByMostRecent !== undefined ? job.sortByMostRecent : true;

  // Parse review date helper
  const parseReviewDate = (dateString) => {
    try {
      if (!dateString) return null;

      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        const parsedDate = Date.parse(dateString);
        return isNaN(parsedDate) ? null : new Date(parsedDate);
      }
      return date;
    } catch (error) {
      console.error(`Error parsing date "${dateString}":`, error);
      return null;
    }
  };

  // PHASE 1: Process each ASIN for scraping and saving
  console.log(
    `📥 Phase 1: Scraping and saving reviews for ${job.asins.length} ASINs...`
  );

  for (let i = 0; i < job.asins.length; i++) {
    const asinData = job.asins[i];

    try {
      console.log(
        `📦 Processing ASIN ${i + 1}/${job.asins.length}: ${asinData.asin}`
      );

      // Get ASIN record with seller info
      const asinRecord = await prisma.lexASIN.findUnique({
        where: { asin: asinData.asin },
        include: {
          seller: {
            select: {
              name: true,
              sellerId: true,
            },
          },
        },
      });

      // Check if ASIN has reviews before scraping
      if (asinRecord.totalReviews === 0 || asinRecord.totalReviews === null) {
        console.log(
          `⚠️ ASIN ${asinData.asin} has 0 reviews, skipping scraping`
        );

        // Update ASIN status to SCRAPED since we confirmed no reviews
        await prisma.lexASIN.update({
          where: { asin: asinData.asin },
          data: {
            status: ScrapingStatus.SCRAPED,
            updatedAt: new Date(),
          },
        });

        successfulAsins++;
        continue;
      }

      const brand =
        asinRecord?.seller?.name || asinRecord?.seller?.sellerId || "";
      const cookies = activeCookieData.cookies;

      const reviewResults = await scrapeAmazonReviews(
        asinData.asin,
        cookies,
        brand,
        sortByMostRecent
      );

      if (!reviewResults.success) {
        // Check for login form error
        if (
          reviewResults.error &&
          (reviewResults.error.includes("LOGIN_FORM_DETECTED") ||
            reviewResults.error.includes("cookies expired"))
        ) {
          console.log(
            `🔐 Login form detected during bulk processing - marking cookies as expired`
          );

          await prisma.lexReviewScraperCookies.updateMany({
            where: {
              emailId: activeCookieData.emailId,
            },
            data: {
              cookieStatus: "EXPIRE",
              active: false,
              updatedAt: new Date(),
            },
          });

          cookiesExpired = true;
          throw new Error(
            `Authentication required: ${reviewResults.error}. Stopping bulk job as cookies have expired.`
          );
        }

        console.error(
          `❌ Failed to scrape reviews for ASIN ${asinData.asin}: ${reviewResults.error}`
        );
        failedAsins++;
        continue;
      }

      let asinReviewsSaved = 0;

      // Save reviews for this ASIN with DATA_SCRAPED status
      for (const reviewData of reviewResults.reviews) {
        try {
          await prisma.lexReview.upsert({
            where: { reviewID: reviewData["Review ID"] },
            update: {
              reviewContent: reviewData.reviewContent,
              reviewTitle: reviewData["Review Title"],
              reviewScore: reviewData.reviewScore,
              reviewDate: parseReviewDate(reviewData.reviewDate),
              reviewer: reviewData.reviewer,
              reviewerCountry: reviewData.reviewerCountry,
              reviewerID: reviewData.reviewerID,
              isVerified: reviewData.isVerified === "True" ? true : false,
              reviewLink: reviewData["Review URL"],
              reviewerLink: reviewData.reviewerLink,
              HelpfulCounts: reviewData.HelpfulCounts,
              reviewImage: null,
              image1: reviewData.image1,
              image2: reviewData.image2,
              image3: reviewData.image3,
              image4: reviewData.image4,
              pageUrl: reviewData.pageUrl,
              sellerId: asinRecord?.seller?.sellerId || null,
              productTitle: reviewData.productTitle,
              productLink: reviewData.productLink,
              variant_0: reviewData.variant_0,
              variant_1: reviewData.variant_1,
              status: ReviewStatus.DATA_SCRAPED,
              updatedAt: new Date(),
            },
            create: {
              reviewID: reviewData["Review ID"],
              reviewContent: reviewData.reviewContent,
              reviewTitle: reviewData["Review Title"],
              reviewScore: reviewData.reviewScore,
              reviewDate: parseReviewDate(reviewData.reviewDate),
              reviewer: reviewData.reviewer,
              reviewerCountry: reviewData.reviewerCountry,
              reviewerID: reviewData.reviewerID,
              isVerified: reviewData.isVerified === "True" ? true : false,
              reviewLink: reviewData["Review URL"],
              reviewerLink: reviewData.reviewerLink,
              HelpfulCounts: reviewData.HelpfulCounts,
              reviewImage: null,
              image1: reviewData.image1,
              image2: reviewData.image2,
              image3: reviewData.image3,
              image4: reviewData.image4,
              pageUrl: reviewData.pageUrl,
              sellerId: asinRecord?.seller?.sellerId || null,
              productTitle: reviewData.productTitle,
              productLink: reviewData.productLink,
              variant_0: reviewData.variant_0,
              variant_1: reviewData.variant_1,
              status: ReviewStatus.DATA_SCRAPED,

              // Connect to ASIN relation
              asinRef: asinRecord
                ? {
                    connect: { asin: asinData.asin },
                  }
                : undefined,
              // Connect to job relation
              // job: {
              //   connect: { id: job.id }
              // }
            },
          });
          asinReviewsSaved++;
        } catch (reviewError) {
          console.error(
            `❌ Failed to save review ${reviewData["Review ID"]}:`,
            reviewError
          );
        }
      }

      // Update ASIN status to SCRAPED
      if (asinRecord) {
        await prisma.lexASIN.update({
          where: { asin: asinData.asin },
          data: {
            status: ScrapingStatus.SCRAPED,
            updatedAt: new Date(),
          },
        });
      }

      totalReviewsSaved += asinReviewsSaved;
      successfulAsins++;

      console.log(
        `✅ Saved ${asinReviewsSaved} reviews for ASIN: ${asinData.asin}`
      );

      // Wait between ASINs to avoid rate limiting
      if (i < job.asins.length - 1) {
        console.log("⏳ Waiting 15 seconds before next ASIN...");
        await new Promise((resolve) => setTimeout(resolve, 15000));
      }
    } catch (error) {
      console.error(`❌ Error processing ASIN ${asinData.asin}:`, error);
      // Mark failed ASIN status
      try {
        await prisma.lexASIN.update({
          where: { asin: asinData.asin },
          data: {
            status: ScrapingStatus.FAILED,
            updatedAt: new Date(),
          },
        });
      } catch (asinUpdateError) {
        console.error(
          `❌ Error updating ASIN status to FAILED:`,
          asinUpdateError
        );
      }

      failedAsins++;

      // If cookies expired, stop processing remaining ASINs
      if (cookiesExpired) {
        console.log("🛑 Stopping bulk job due to expired cookies");
        break;
      }
    }
  }

  console.log(
    `✅ Scraping Complete: Saved ${totalReviewsSaved} reviews from ${successfulAsins} ASINs`
  );

  // Mark bulk job as completed
  const errorMessage = cookiesExpired
    ? `Cookies expired during processing. Successfully processed ${successfulAsins} ASINs before failure.`
    : failedAsins > 0
    ? `Failed to process ${failedAsins} ASINs`
    : null;

  await prisma.lexJob.update({
    where: { id: job.id },
    data: {
      status: cookiesExpired ? JobStatus.FAILED : JobStatus.COMPLETED,
      totalReviews: totalReviewsSaved,
      errorMessage: errorMessage,
      updatedAt: new Date(),
    },
  });

  console.log(
    `✅ Bulk Review Job ${job.name} ${
      cookiesExpired ? "failed due to expired cookies" : "completed"
    }!`
  );
  console.log(`   📊 Total ASINs: ${job.asins.length}`);
  console.log(`   ✅ Successfully processed: ${successfulAsins}`);
  console.log(`   ❌ Failed: ${failedAsins}`);
  console.log(`   💾 Total reviews saved: ${totalReviewsSaved}`);

  if (cookiesExpired) {
    throw new Error(errorMessage);
  }
}

// Graceful shutdown
process.on("SIGINT", () => {
  console.log("🛑 LEX Reviews Worker shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("🛑 LEX Reviews Worker shutting down gracefully...");
  process.exit(0);
});

module.exports = {
  processLexReviewJobs,
  processSingleReviewJob,
  processBulkReviewJob,
};
