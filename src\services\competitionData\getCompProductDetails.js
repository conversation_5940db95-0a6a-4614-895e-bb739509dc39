const cheerio = require("cheerio");

const Sentry = require("@sentry/node");
const { selectors } = require("../../services/scrapeAmazon/selectors");
const extractRating = require("../../services/amazonAudit/ratingsReport");
const getReviews = require("../../utils/scrapeAmazonUtils/getReviews");
const getCategoryAndRank = require("../../utils/scrapeAmazonUtils/getCategoryAndRank");
const getStoreFront = require("../../services/amazonAudit/storeFrontReport");
const { MAX_NO_OF_REVIEWS } = require("../../services/scrapeAmazon/constant");
const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");

async function getCompProductDetails(htmlData, url) {
  try {
    console.log("Getting Product Details");
    const $ = cheerio.load(htmlData);
    const titleElement = $(selectors.nameElement);
    if (titleElement.length === 0) {
      return null;
    }
    let brand = "";
    brand = $(selectors.brandName)
      .text()
      .replace("Brand", "")
      .trim()
      .toLowerCase();

    if (brand == "") {
      const storeName = $("#bylineInfo")
        .text()
        .replace("Visit the", "")
        .replace("Store", "")
        .trim();
      brand = storeName;
    }
    if (brand == "") {
      const manufacturer = $('span:contains("Manufacturer")')
        .next("span")
        .text()
        .trim();
      brand = manufacturer.toLowerCase();
    }
    const title = $(selectors.nameElement).first().text().trim();
    const priceString = $(selectors.priceElement).first().text().trim();
    const price = parseFloat(priceString.replace(/[^\d.-]/g, "")) || 0;
    const salesString = $(selectors.lastMonthSale).text().trim() || "N/A";
    let sales = 0;
    if (salesString !== "N/A") {
      const numberMatch = salesString.match(/(\d+(\.\d+)?)([K]?)\+/);
      if (numberMatch) {
        let number = parseFloat(numberMatch[1]);
        const unit = numberMatch[3];
        if (unit === "K") {
          number *= 1000;
        }
        // Add a random number between 1 and 9
        sales = Math.floor(number) + Math.floor(Math.random() * 9) + 1;
      }
    }
    // console.log({ priceString, price, salesString, sales });
    const revenue = price * sales;
    // const aplusContent = $(selectors.aPlusContent1).length ? true : false;
    // const productVideo = $(selectors.productVideo).length ? true : false;
    // const outOfStock = $(selectors.outOfStock).length ? true : false;
    // const categoryAndRank = getCategoryAndRank($);
    // const review = getReviews($, MAX_NO_OF_REVIEWS);
    // const store = getStoreFront($);
    const asin = url.match(/\/dp\/([A-Z0-9]{10})\//)?.[1];
    return {
      asin,
      brand: brand,
      title: title,
      revenue:revenue,
      // description: description,
      // price: price,
      // rating: rating,
      // numOfRating: numOfRating,
      // sales: sales,
      // aplusContent: aplusContent,
      // productVideo: productVideo,
      // outOfStock,
      // categoryRank: categoryAndRank,
      // store: store,
      // review,
    };
  } catch (error) {
    Sentry.captureException("Error in Getting Product Details:", error);
    console.error("Error in Getting Product Details:", error);
  }
}

module.exports = getCompProductDetails;

async function getProductData() {
  const clientId = 1;
  const url =
    "https://www.amazon.com/Timex-T40051-Expedition-Metal-Leather/dp/B0000TIISW/ref=pd_rhf_dp_s_ci_mcx_mr_hp_d_d_sccl_1_3/140-2727520-1519259?pd_rd_w=Eqvp2&content-id=amzn1.sym.0a853d15-c5a9-4695-90cd-fdc0b630b803:amzn1.symc.4d67cb82-b560-48ed-9497-a0a2a821f019&pf_rd_p=0a853d15-c5a9-4695-90cd-fdc0b630b803&pf_rd_r=E588VDGATD3HVHK24XYB&pd_rd_wg=5I0gV&pd_rd_r=14b6772d-ccbd-43e7-b8d7-0306a22068ae&pd_rd_i=B0000TIISW&psc=1";
  const htmlData = await getHtmlByProxy(url, clientId);
  const company_name = "MTN OPS";
  const data = getCompProductDetails(htmlData, url);
  // fs.writeFileSync("output.json", JSON.stringify(data));
  console.log("------done-----");
  console.log({ data });
}
// getProductData(); 
