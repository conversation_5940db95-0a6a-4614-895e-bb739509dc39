/**
 * Standalone Azure OpenAI Connection Test
 * Run this file directly to test your Azure OpenAI setup
 * 
 * Usage: node testConnection.js
 */

require("dotenv").config();
const { testAzureOpenAI } = require("./src/services/spike/runPromptChain");

async function main() {
  console.log("🔍 Azure OpenAI Connection Test");
  console.log("================================\n");
  
  // Display current environment variables (without showing sensitive data)
  console.log("📋 Current Configuration:");
  console.log(`- Endpoint: ${process.env.AZURE_OPENAI_ENDPOINT || "❌ NOT SET"}`);
  console.log(`- API Version: ${process.env.AZURE_OPENAI_API_VERSION || "❌ NOT SET"}`);
  console.log(`- API Key: ${process.env.AZURE_OPENAI_API_KEY ? "✅ SET" : "❌ NOT SET"}`);
  console.log("");
  
  // Check if required environment variables are set
  const requiredVars = [
    'AZURE_OPENAI_API_KEY',
    'AZURE_OPENAI_ENDPOINT', 
    'AZURE_OPENAI_API_VERSION'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log("❌ Missing required environment variables:");
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log("\n💡 Please check your .env file and ensure all variables are set.");
    process.exit(1);
  }
  
  try {
    // Run the test
    console.log("🧪 Testing connection...\n");
    const result = await testAzureOpenAI("Hello! Please confirm the connection is working.");
    
    if (result.success) {
      console.log("\n🎉 SUCCESS! Azure OpenAI is working correctly.");
      console.log("\n📊 Test Results:");
      console.log(`   Response: "${result.response}"`);
      console.log(`   Model: ${result.model}`);
      console.log(`   Duration: ${result.duration}`);
      console.log(`   Tokens Used: ${result.tokenUsage.total_tokens || 'Unknown'}`);
      console.log(`   Prompt Tokens: ${result.tokenUsage.prompt_tokens || 'Unknown'}`);
      console.log(`   Completion Tokens: ${result.tokenUsage.completion_tokens || 'Unknown'}`);
      
    } else {
      console.log("\n❌ FAILED! Azure OpenAI connection is not working.");
      console.log("\n🔍 Error Details:");
      console.log(`   Error: ${result.error}`);
      console.log(`   Error Type: ${result.errorType}`);
      if (result.statusCode) {
        console.log(`   Status Code: ${result.statusCode}`);
        console.log(`   Status Text: ${result.statusText}`);
      }
      
      console.log("\n🔧 Troubleshooting Steps:");
      console.log("1. Verify your Azure OpenAI resource is deployed and active");
      console.log("2. Check that your API key is correct and not expired");
      console.log("3. Ensure your endpoint URL is correct");
      console.log("4. Verify the API version is supported");
      console.log("5. Check if your Azure subscription has sufficient quota");
      
      process.exit(1);
    }
    
  } catch (error) {
    console.log("\n💥 Unexpected error occurred:");
    console.log(`   ${error.message}`);
    console.log("\n📋 Full error details:");
    console.log(error);
    process.exit(1);
  }
}

// Run the test
main().catch((error) => {
  console.error("💥 Fatal error:", error);
  process.exit(1);
});
