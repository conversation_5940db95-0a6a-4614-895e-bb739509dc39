const { selectors } = require("../../services/scrapeAmazon/selectors");

const reviewCategoryMap = [
  {
    numOfReviews: [0, 5],
    category: "Bad/Low",
  },
  {
    numOfReviews: [6, 30],
    category: "Okay to Start",
  },
  {
    numOfReviews: [31, 50],
    category: "Optimal",
  },
  {
    numOfReviews: [51, Infinity],
    category: "Helpful",
  },
];
function getCategory(numOfReviews) {
  for (const entry of reviewCategoryMap) {
    if (
      numOfReviews >= entry.numOfReviews[0] &&
      numOfReviews <= entry.numOfReviews[1]
    ) {
      return entry.category;
    }
  }
  return "Unknown"; // Default case if no category matches
}

function extractReviews($, allReviews) {
  const reviewElements = $(allReviews);
  let reviewData = [];
  let fiveStarReviewFound = false;

  reviewElements.each((index, element) => {
    const reviewAuthor = $(element).find(selectors.authorName).text().trim();
    const dateAndLocation = $(element).find(selectors.reviewDate).text().trim();
    const reviewTitle = $(element).find(selectors.reviewTitle).text().trim();
    const reviewBody = $(element).find(selectors.reviewBody).text().trim();
    const reviewRatingText = $(element)
      .find(selectors.reviewRating)
      .text()
      .trim();
    const reviewRating = parseFloat(reviewRatingText);

    if (reviewRating === 5.0) {
      fiveStarReviewFound = true;
    }

    const reviewNumber = `Review${index + 1}`;

    reviewData.push({
      reviewNumber,
      reviewAuthor,
      reviewRatingText,
      dateAndLocation,
      reviewTitle,
      reviewBody,
    });
  });
  if (fiveStarReviewFound) {
    reviewData.sort((a, b) => b.reviewRating - a.reviewRating);
  }
  // else {
  //   reviewData.push({ "5star": "No five star Review" });
  // }
  return reviewData;
}

function getReviews($, numOfReviews) {
  const totalReviewCountElement = $(selectors.numOfRating);
  const totalReviewCount = totalReviewCountElement.length
    ? totalReviewCountElement.text().split(" ")[0]
    : 0;
  let totalReviewCountInt = 0;
  if (totalReviewCount) {
    totalReviewCountInt = parseInt(totalReviewCount.replace(",", ""));
  }
  const reviewMissing = totalReviewCount ? false : true;
  // console.log({ totalReviewCount });
  const reviewsCategory = getCategory(totalReviewCountInt);

  // const ratingPerctElement = $(".a-histogram-row.a-align-center");
  const percentages = [];
  const reviewPerStar = [];
  const starLabels = [
    "5starReview",
    "4starReview",
    "3starReview",
    "2starReview",
    "1starReview",
  ];

  $(
    "._cr-ratings-histogram_style_histogram-row-container__Vh7Di"
  ).each((index, element) => {
    const percentage = $(element)
      .attr("aria-label")
      .trim()
      .split(" ")[0]
      .trim();
    percentages.push(parseInt(percentage));
    const numberOfReviews = Math.round(
      totalReviewCountInt * (percentage / 100)
    );
    const reviewObject = {};
    reviewObject[starLabels[index]] = numberOfReviews;
    reviewPerStar.push(reviewObject);
  });
  // console.log({ percentages, reviewPerStar });
  let reviewData = [];

  const mainReviewsElement = $(selectors.allReviewsMain);
  if (mainReviewsElement.length) {
    reviewData = extractReviews($, selectors.allReviewsMain, numOfReviews);
  } else {
    const altReviewsElement = $(selectors.allReviewsAlt);
    if (altReviewsElement.length) {
      reviewData = extractReviews($, selectors.allReviewsAlt);
    } else {
      reviewData = "N/A";
    }
  }
  reviewData = reviewData.slice(0, numOfReviews);
  return {
    totalReviewCountInt,
    reviewsCategory: reviewsCategory,
    reviewMissing,
    reviewPerStar,
    reviews: reviewData,
  };
}

module.exports = getReviews;
