const prisma = require("../database/prisma/getPrismaClient");

// Function to format just the date and append " UTC"
const formatDateOnly = (dateString) => {
  if (!dateString) return "Undefined";

  const date = new Date(dateString);
  return (
    date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }) + " UTC"
  );
};

// Function to format just the time and append " UTC"
const formatTimeOnly = (dateString) => {
  if (!dateString) return "Undefined";

  const date = new Date(dateString);
  return (
    date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: true, // for 12-hour format, remove this for 24-hour format
    }) + " UTC"
  );
};
async function highlightElement(element, highlight, page) {
  return await page.evaluate((element, highlight) => {

    const rect = element.getBoundingClientRect();
    const randomExpansion = Math.random() * 5 + 1; // Random value between 1 and 6
    const result = {
      x: rect.x - randomExpansion,
      y: rect.y - randomExpansion,
      width: rect.width + randomExpansion * 2,
      height: rect.height + randomExpansion * 2
    };
    if (highlight) {
      const div = document.createElement('div');
      div.style.position = 'absolute';
      div.style.left = `${result.x}px`;
      div.style.top = `${result.y}px`;
      div.style.width = `${result.width}px`;
      div.style.height = `${result.height}px`;
      div.style.border = '2px solid red';
      div.style.boxSizing = 'border-box';
      div.style.pointerEvents = 'none';
      document.body.appendChild(div);
    }
    return result;
  }, element, highlight);

}

async function removeHighlight(page) {
  await page.evaluate(() => {
    const highlightedElements = document.querySelectorAll('div[style*="border: 2px solid red"]');
    highlightedElements.forEach(element => element.remove());
  });
}

async function getClientName(id) {
  try {
    const clientName = await prisma.user.findUnique({
      where: {
        id,
      },
      select: {
        name: true,
      },
    });
    if (!clientName) {
      console.log("No client data for id:", id);
      return null;
    }
    return clientName.name;
  } catch (error) {
    console.log("Error fetching client name:", error);
    return null;
  }
}
module.exports = {
  formatTimeOnly,
  formatDateOnly,
  highlightElement,
  removeHighlight,
  getClientName,
};
