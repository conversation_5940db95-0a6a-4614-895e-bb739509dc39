const ratingCategoryMap = [
  {
    minRating: 4.8,
    maxRating: 5.0,
    category: 5,
  },
  {
    minRating: 4.6,
    maxRating: 4.7,
    category: 4.5,
  },
  {
    minRating: 4.3,
    maxRating: 4.59,
    category: 4.5,
  },
  {
    minRating: 3.8,
    maxRating: 4.29,
    category: 4.0,
  },
  {
    minRating: 3.6,
    maxRating: 3.79,
    category: 3.5,
  },
  {
    minRating: 3.3,
    maxRating: 3.59,
    category: 3.5,
  },
  {
    minRating: 2.8,
    maxRating: 3.29,
    category: 3,
  },
  {
    minRating: 2.6,
    maxRating: 2.79,
    category: 2.5,
  },
  {
    minRating: 2.3,
    maxRating: 2.59,
    category: 2.5,
  },
  {
    minRating: 2.0,
    maxRating: 2.29,
    category: 2,
  },
  {
    minRating: 1.5,
    maxRating: 1.99,
    category: 1.5,
  },
  {
    minRating: 1.0,
    maxRating: 1.49,
    category: 1,
  },
];

// Function to get category based on rating
function getRatingCategory(rating) {
  for (const entry of ratingCategoryMap) {
    if (rating >= entry.minRating && rating <= entry.maxRating) {
      return entry.category;
    }
  }
  return rating; // Default case if no category matches
}

function getRating($, selector) {
  const ratingElement = $(selector);
  const ratingRaw = ratingElement.length
    ? ratingElement.first().text().trim()
    : "0";
  let rating = 0;
  if (ratingRaw) {
    rating = parseFloat(ratingRaw.split(" ")[0]);
  }
  const ratingComesAs = getRatingCategory(rating);

  return {
    rating,
    ratingComesAs,
  };
}

module.exports = { getRating,getRatingCategory };

