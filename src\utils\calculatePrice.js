//input - model, inputRate and outputRate, promptTokens
const gpt4TurboPricing = {
  inputPer1000: 0.01,
  outputPer1000: 0.03,
};

//All price models per 1k tokens
//have to check if naming is proper or not
const pricingModels = {
  "gpt-4-turbo-preview": gpt4TurboPricing,
  "gpt-4-0125-preview": gpt4TurboPricing,
  "gpt-4-1106-preview": gpt4TurboPricing,
  "gpt-4-1106-vision-preview": gpt4TurboPricing,
  "gpt-4": {
    inputPer1000: 0.03,
    outputPer1000: 0.06,
  },
  "gpt-4-32k": {
    inputPer1000: 0.06,
    outputPer1000: 0.12,
  },
  "gpt-3.5-turbo": {
    inputPer1000: 0.0015,
    outputPer1000: 0.0015,
  },
  "gpt-3.5-turbo-0125": {
    inputPer1000: 0.0005,
    outputPer1000: 0.0015,
  },
  "gpt-3.5-turbo-instruct": {
    inputPer1000: 0.0015,
    outputPer1000: 0.002,
  },
  "gpt-4o": {
    inputPer1000: 0.005,
    outputPer1000: 0.015,
  },
  "gpt-4o-2024-05-13": {
    inputPer1000: 0.005,
    outputPer1000: 0.015,
  },
};

//Calculate pricing
function calculatePricing(inputTokens, outputTokens, modelName) {
  if (!(modelName in pricingModels)) {
    return { inputPricing: 0, outputPricing: 0 };
  }

  const model = pricingModels[modelName];

  const inputPricing = (inputTokens / 1000) * model.inputPer1000;
  const outputPricing = (outputTokens / 1000) * model.outputPer1000;

  return {
    inputPricing,
    outputPricing,
  };
}

module.exports = calculatePricing;
