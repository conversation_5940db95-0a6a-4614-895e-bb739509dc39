# APIs

## User
1. **Sign Up**
   - **Endpoint:** `/api/user/signup`
   - **Method:** `POST`
   - **Description:** Register a new user.
   - **Request Body:**
     ```json
     {
       "email": "string",
       "password": "string",
       "name": "string"
     }
     ```
   - **Response:**
     ```json
     {
       "id": "integer",
       "email": "string",
       "name": "string",
       "createdAt": "DateTime",
       "updatedAt": "DateTime"
     }
     ```

2. **Log In**
   - **Endpoint:** `/api/user/login`
   - **Method:** `POST`
   - **Description:** Authenticate an existing user.
   - **Request Body:**
     ```json
     {
       "email": "string",
       "password": "string"
     }
     ```
   - **Response:**
     ```json
     {
       "token": "string"
     }
     ```

3. **ME**
   - **Endpoint:** `/api/user/me`
   - **Method:** `GET`
   - **Description:** Get the authenticated user's details.
   - **Headers:**
     ```json
     {
       "Authorization": "Bearer <token>"
     }
     ```
   - **Response:**
     ```json
     {
       "id": "integer",
       "email": "string",
       "name": "string",
       "createdAt": "DateTime",
       "updatedAt": "DateTime"
     }
     ```

## Jobs
1. **Get All Jobs for the User**
   - **Endpoint:** `/api/jobs/`
   - **Method:** `GET`
   - **Description:** Retrieve all jobs associated with the authenticated user.
   - **Headers:**
     ```json
     {
       "Authorization": "Bearer <token>"
     }
     ```
   - **Response:**
     ```json
     [
       {
         "id": "integer",
         "title": "string",
         "csv_link": "string", // If csv uploaded the  send csv link
         "pdf_link": "string", // If one product link uploaded then
         "createdAt": "DateTime",
         "updatedAt": "DateTime"
       }
     ]
     ```

2. **Post a Job**
   - **Endpoint:** `/api/jobs`
   - **Method:** `POST`
   - **Description:** Create a new job.
   - **Headers:**
     ```json
     {
       "Authorization": "Bearer <token>"
     }
     ```
   - **Request Body:**
     ```json
     {
       "file": "File"
     }
     ```
     Or
       ```json
     {
       "product Url": "string"
     }
     ```
   - **Response:**
     ```json
       {
         "id": "integer",
         "title": "string",
         "csv_link": "string", // If csv uploaded the  send csv link
         "pdf_link": "string", // If one product link uploaded then
         "createdAt": "DateTime",
         "updatedAt": "DateTime"
       }
     ```

## Admins
1. **Get All Client Details (Impersonate as the Client)**
   - **Endpoint:** `/api/admin/clients`
   - **Method:** `GET`
   - **Description:** Retrieve all client details. Admins can impersonate clients to view their details.
   - **Headers:**
     ```json
     {
       "Authorization": "Bearer <admin-token>"
     }
     ```
   - **Response:**
     ```json
     [
       {
         "id": "integer",
         "email": "string",
         "name": "string",
         "createdAt": "DateTime",
         "updatedAt": "DateTime"
       }
     ]
     ```

## Audit

1. **Get Amazon Audit Report**
   - **Endpoint:** `/api/get_amazon_audit_report/:slug`
   - **Method:** `GET`
   - **Description:** Retrieve the Amazon audit report for a specific company.
   - **Parameters:**
     - `slug` (string): The slug of the company.
   - **Response:**
     ```json
     {
       "id": "integer",
       "slug": "string",
       "companyName": "string",
       "finalUrl": "string",
       "pageImage": "string",
       "productImage": "string",
       "pdfUrl": "string",
       "category": "string",
       "createdAt": "DateTime",
       "updatedAt": "DateTime"
     }
     ```

2. **Create Amazon Audit Report**
   - **Endpoint:** `/api/amazonAuditReport`
   - **Method:** `POST`
   - **Description:** Create a new Amazon audit report.
   - **Request Body:**
     ```json
     {
       "companyName": "string",
       "productUrl": "string",
       "client": "string",
       "emailId": "string",
       "to_email": "string",
       "regenerate": "boolean",
       "is_test": "boolean"
     }
     ```
   - **Response:**
     ```json
     {
       "id": "integer",
       "slug": "string",
       "companyName": "string",
       "finalUrl": "string",
       "pageImage": "string",
       "productImage": "string",
       "pdfUrl": "string",
       "category": "string",
       "createdAt": "DateTime",
       "updatedAt": "DateTime"
     }
     ```

3. **Get Request Logs**
   - **Endpoint:** `/api/request_logs/:limit`
   - **Method:** [`GET`]
   - **Description:** Retrieve a limited number of request logs.
   - **Parameters:**
     - `limit` (integer): The number of request logs to retrieve.
   - **Response:**
     ```json
     [
       {
         "id": "integer",
         "url": "string",
         "method": "string",
         "headers": "string",
         "body": "string",
         "response": "string",
         "statusCode": "integer",
         "createdAt": "DateTime",
         "updatedAt": "DateTime"
       }
     ]
     ```