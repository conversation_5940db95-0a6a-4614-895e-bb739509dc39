const express = require("express");
const router = express.Router();
const { userAuth } = require("../middlewares/jwt");
const {
  createAndUploadToSheet,
  makeSheetPublic,
  createGoogleSheet,
} = require("../utils/googleSheetUtils");
const { processCsvFiles, fetchCSVFileData } = require("../utils/googleSheetUtils/processCsv");
router.post("/api/create-sheet", userAuth, async (req, res) => {
  try {
    const { jobIdArray } = await req.body;

    const {
      successData,
      unsuccessfulData,
      successHeaders,
      unsuccessfulHeaders,
    } = await processCsvFiles(jobIdArray);
    if (!successData.length && !unsuccessfulData.length) {
      return res.json(
        { error: "No valid CSV data found" },
        { status: 500 }
      );
    }

    console.log(
      "Successfully processed CSV data. Uploading to Google Sheets..."
    );
    const sheetId = await createAndUploadToSheet(
      successData,
      unsuccessfulData,
      successHeaders,
      unsuccessfulHeaders
    );
    if (!sheetId) {
      return res.json(
        { error: "Failed to create Google Sheet" },
        { status: 500 }
      );
    }

    console.log("Google Sheet created with ID:", sheetId);
    const sheetUrl = await makeSheetPublic(sheetId);

    console.log("Google Sheet is now editable by anyone. URL:", sheetUrl);
    return res.status(200).json({ sheetUrl });
  } catch (error) {
    console.error("Server error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/create-google-sheet:
 *   post:
 *     summary: Create a Google Sheet from database data
 *     description: Creates a Google Sheet containing data from the specified table and IDs, and returns the public URL
 *     security:
 *       - bearerAuth: []
 *     tags:
 *       - Google Sheets
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - idArray
 *               - tableName
 *               - indentifierKey
 *             properties:
 *               idArray:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of IDs to fetch data for
 *                 example: ["52", "42"]
 *               tableName:
 *                 type: string
 *                 description: Name of the database table to fetch data from
 *                 example: "lexImageGenReview"
 *               indentifierKey:
 *                 type: string
 *                 description: The key to use for identifying records in the table
 *                 example: "id"
 *     responses:
 *       200:
 *         description: Google Sheet created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 sheetUrl:
 *                   type: string
 *                   description: Public URL of the created Google Sheet
 *                   example: "https://docs.google.com/spreadsheets/d/1Nmc_EJdG469qZTcGBmV6299vVCbAn73DZ2kLgdR3yZE"
 *       400:
 *         description: Missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Missing idArray or tableName or indentifierKey"
 *       404:
 *         description: No data found for the given IDs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "No data found"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post("/api/create-google-sheet", userAuth, async (req, res) => {
  try {
    const { idArray, tableName, indentifierKey } = req.body;

    if (!idArray || !tableName || !indentifierKey) {
      return res.status(400).json({ error: "Missing idArray or tableName or indentifierKey" });
    }

    const csvData = await fetchCSVFileData(idArray, tableName, indentifierKey);

    if (csvData instanceof Error) {
      return res.status(500).json({ error: csvData.message });
    }

    if (!csvData || csvData.length === 0) {
      return res.status(404).json({ error: "No data found" });
    }

    const sheetId = await createGoogleSheet(csvData);
    if (!sheetId) {
      return res.status(500).json({ error: "Failed to create Google Sheet" });
    }

    const sheetUrl = await makeSheetPublic(sheetId);
    return res.status(200).json({ sheetUrl });
  } catch (error) {
    console.error("Server error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;
