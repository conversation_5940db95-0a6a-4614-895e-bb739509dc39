const express = require("express");
const router = express.Router();
const prisma = require("../database/prisma/getPrismaClient"); // Adjust the path as necessary
const { userAuth } = require("../middlewares/jwt");
const yaml = require("js-yaml");
const converter = require("json-2-csv");

// GET: Fetch all features
router.get("/api/spike/get-all-features", userAuth, async (req, res) => {
  try {
    const features = await prisma.feature.findMany();
    res.status(200).json(features);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching features:", error.message);
    res.status(500).json({ error: "Internal server error" });
  }
});

// POST: Save or update feature and prompts from YAML
router.post("/api/spike/save-feature-prompts", userAuth, async (req, res) => {
  try {
    const { yamlConfig } = req.body;
    // console.log({ yamlConfig });
    const parsed = yaml.load(yamlConfig);

    const featureName = parsed.feature;
    const prompts = parsed.prompts || [];

    console.log({ featureName, prompts });

    if (!featureName || !Array.isArray(prompts)) {
      return res.status(400).json({ error: "Invalid YAML format." });
    }

    const feature = await prisma.feature.upsert({
      where: { name: featureName },
      update: { numOfVersions: prompts.length },
      create: { name: featureName, numOfVersions: prompts.length },
    });

    for (const prompt of prompts) {
      const content =
        typeof prompt.content === "string"
          ? prompt.content
          : JSON.stringify(prompt.content);

      await prisma.prompt.upsert({
        where: {
          featureId_name: {
            featureId: feature.id,
            name: prompt.name,
          },
        },
        update: {
          content,
          type: prompt.type,
        },
        create: {
          name: prompt.name,
          featureId: feature.id,
          content,
          type: prompt.type,
        },
      });
    }

    res
      .status(200)
      .json({ message: "Feature and prompts saved successfully." });
  } catch (error) {
    console.error("Error saving feature:", error.stack);
    res.status(500).json({ error: "Internal server error" });
  }
});

router.get(
  "/api/spike/get-feature-report/:featureName",
  userAuth,
  async (req, res) => {
    try {
      const featureName = req.params.featureName;
      const feature = await prisma.feature.findUnique({
        where: {
          name: featureName,
        },
        select: {
          id: true,
        },
      });
        if (!feature) {
             return res.status(404).json({ error: "Feature not found, Check name again" });
        }
      const logs = await prisma.promptLogs.findMany({
        where: {
          ftID: feature.id,
        },
      });
      const csvContent = converter.json2csv(logs);
      const inputCsvFileName = `feature_logs_${featureName}.csv`;
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${inputCsvFileName}`
      );
      res.setHeader("Content-Type", "text/csv");
      res.status(200).send(csvContent);
    } catch (error) {
      console.error("Error fetching feature report data:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

module.exports = router;
