/**
 * Simple test to check if Azure OpenAI is working
 * Run: node testAzure.js
 */

const { testAzureOpenAI } = require("./src/services/spike/runPromptChain");

async function runTest() {
  console.log("🚀 Testing Azure OpenAI...\n");
  
  const result = await testAzureOpenAI("Say hello and confirm you're working");
  
  if (result.success) {
    console.log("\n🎉 SUCCESS! Azure OpenAI is working");
  } else {
    console.log("\n❌ FAILED! Check your Azure OpenAI setup");
    console.log("Error:", result.error);
  }
  
  return result;
}

runTest().catch(console.error);
