const scrapeAboutData = require("./process");
const outputFileName = `scrapeAboutData/about_data_${new Date()
    .toISOString()
    .slice(0, 15)
    .replace(/[-T:]/g, "")
    .replace(/:/g, "")}.csv`;

const fs = require("fs");
const csv = require("csv-parser");

function processFile(file) {
  const outputStream = fs.createWriteStream(outputFileName, { flags: "a" }); // Append mode
  outputStream.write("Website,Home Page Text,About URLs,About Texts,Status\n");
  const domains = new Set();

  fs.createReadStream(file)
    .pipe(csv())
    .on("data", (row) => {
      const keyword = row["website"];
      domains.add(keyword);
    })
    .on("end", () => {
      console.log(`Unique Domains: ${domains.size}`);
      let processedCount = 0;

      for (const base_url of domains) {
        processedCount++;
        console.log(
          `Processing: ${processedCount}/${domains.size} -> ${base_url}`
        );

        scrapeAboutData({
          company_url: base_url,
          company_id: processedCount,
        })
          .then((data) => {
            // Escape commas in text fields by wrapping them in double quotes
            const csvRow = Object.values(data)
              .slice(1)
              .map((value) =>
                typeof value === "string" && value.includes(",")
                  ? `"${value.replace(/,/g, '|')}"`
                  : value
              )
              .join(",");
            outputStream.write(csvRow + "\n");
          })
          .catch((error) => {
            console.error(`Error processing ${base_url}: ${error}`);
          });
      }
    });
}

processFile("scrapeAboutData/data1.csv");

// scrapeAboutData({
//   company_url: "laidbacksnacks.com",
//   company_id: 1,
// });
