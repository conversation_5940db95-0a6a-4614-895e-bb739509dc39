/*
  Warnings:

  - The `status` column on the `LexASIN` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `LexReview` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "ScrapingStatus" AS ENUM ('PENDING', 'SCRAPED', 'FAILED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ReviewStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'DATA_SCRAPED', 'AI_ANALYSIS_PENDING', 'COMPLETED', 'FAILED');

-- DropForeignKey
ALTER TABLE "LexReview" DROP CONSTRAINT "LexReview_jobId_fkey";

-- DropIndex
DROP INDEX "LexASIN_LexSellerId_idx";

-- AlterTable
ALTER TABLE "LexASIN" ADD COLUMN     "countryCode" TEXT,
ADD COLUMN     "sellerId" TEXT,
DROP COLUMN "status",
ADD COLUMN     "status" "ScrapingStatus" NOT NULL DEFAULT 'PENDING';

-- AlterTable
ALTER TABLE "LexReview" DROP COLUMN "status",
ADD COLUMN     "status" "ReviewStatus" NOT NULL DEFAULT 'PENDING';

-- AlterTable
ALTER TABLE "LexReviewScraperCookies" ADD COLUMN     "countryCode" TEXT;

-- AlterTable
ALTER TABLE "LexSeller" ADD COLUMN     "status" "ScrapingStatus" NOT NULL DEFAULT 'PENDING';

-- DropEnum
DROP TYPE "AsinStatus";

-- CreateIndex
CREATE INDEX "LexASIN_sellerId_idx" ON "LexASIN"("sellerId");

-- CreateIndex
CREATE INDEX "LexASIN_countryCode_idx" ON "LexASIN"("countryCode");

-- CreateIndex
CREATE INDEX "LexASIN_status_idx" ON "LexASIN"("status");

-- CreateIndex
CREATE INDEX "LexReview_status_idx" ON "LexReview"("status");

-- CreateIndex
CREATE INDEX "LexSeller_status_idx" ON "LexSeller"("status");
