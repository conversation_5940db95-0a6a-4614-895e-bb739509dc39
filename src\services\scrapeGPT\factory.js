const Sentry = require("@sentry/node");
const { getChatGPTResponse } = require("./request");
const { getPromptTemplates } = require("../../models/configuration");
const {
  chatGPTPricingCalculator,
} = require("../../utils/pricing/pricingCalculator");
async function getPromptTemplate(type, clientId) {
  const promptTemplates = await getPromptTemplates(clientId);
  if (!promptTemplates[type]) {
    Sentry.captureException(new Error(`Unknown type: ${type}`));
    throw new Error(`Unknown type: ${type}`);
  }
  return promptTemplates[type];
}

async function completionFactory(
  type,
  data,
  clientId,
  retries = 3,
  delay = 1000
) {
  try {
    const system_prompt = await getPromptTemplate(type, clientId);
    const user_prompt = JSON.stringify(data);

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const result = await getChatGPTResponse(
          system_prompt,
          user_prompt,
          clientId
        );

        chatGPTPricingCalculator(result, type);
        return result;
      } catch (error) {
        if (attempt < retries) {
          console.warn(
            `Retrying ${type} (${attempt}/${retries}) due to error: ${error.message}`
          );
          await new Promise((resolve) => setTimeout(resolve, delay * attempt)); // Exponential backoff
        } else {
          console.error(`Error in ${type}:`, error.stack);
          Sentry.captureException(error);
          throw new Error(
            `Error fetching text from ChatGPT after ${retries} retries: ${error}`
          );
        }
      }
    }
  } catch (error) {
    console.error(`Unexpected error in ${type}:`, error.stack);
    Sentry.captureException(error);
    return {
      message: "INVALID KEYWORD",
    };
  }
}

// Example usage
async function exampleUsage() {
  try {
    // const data = "Grocery & Gourmet Food";
    const data = {
      "Brand Name": "IBENZER",
      "Product Title":
        'IBENZER Compatible with New 2024 2023 MacBook Air 15 Inch Case M3 A3114 M2 A2941, HardShell Case & KeyboardCover & Type-C Adapter for Mac Air 15.3" Retina Display&Touch ID, CrystalClear, AT15-CYCL+1TC',
    };
    const imageOptimisationGPTInput = {
      // Image: 'https://eq--assets.s3.ap-south-1.amazonaws.com/images/more-labs-b0859mv1v5-1_page_image.png',
      Title:
        "More Labs Dream Well, Holistic Sleep Drink Solution, Non-Habit Forming with Lemon Balm, Jujube Seed, Melatonin, Glycine (Pack of 12)",
      "About this item":
        "Dream Well: A holistic sleep solution designed to help ease your mind and your body, so you can doze off fast and wake up restored*     Relax into Sleep: Lemon balm extract helps ease the mind and relax the body into a state of rest*     Wake Up Restored: Supports cognitive processing so you can wake up clear-headed*     Directions: Drink one bottle of Dream Well approximately 30 minutes before you’re ready to sleep; if you normally have a hard time waking up, start with half a bottle     Trusted Ingredients: Manufactured in an NSF-certified, FDA-registered facility; caffeine free, soy free, gluten free, sugar free, nut free, non-GMO, vegan, no artificial colors; TSA friendly",
    };
    const clientId = 1;
    const searchKeywordInput = {
      title:
        "AirCraft Home, 4 x Scrubbing Pads for The Aircraft PowerGlide | Machine-Washable Microfibre Pads",
      description:
        "Pack of 4 microfibre scrubbing pads - these microfibre pads are designed for use with the AirCraft PowerGlide cordless floor cleaner and make your cleaning experience even more effective on any type of hard floor. The pads are also machine-washable, meaning you can use them again and again.Scrubbing Pads - this set comes with four AirCraft PowerGlide scrubbing pads. These 20cm pads are made from a soft and highly absorbent microfibre material. They can be used on all floor types and across all floor cleaning tasks.Suitable throughout the home and for all hard floor types - our fast and efficient AirCraft PowerGlide can be used on floors throughout the home, including wood, tiles, vinyl, laminate, LVT and natural stone.Superior cleaning power - The microfibre pads rotate 200 times a minute, achieving a powerful clean and a dry, polished floor.Suitable for both PowerGlide and PowerGlide City.",
    };
    // console.log({imageOptimisationGPTInput})
    const result = await completionFactory(
      "compSearchKeyword",
      searchKeywordInput,
      clientId
    );
    console.log(result);
  } catch (error) {
    console.error("Error:", error);
  }
}

// exampleUsage();
module.exports = { completionFactory };
