const connectPuppeteer = require("../../utils/puppeteer/index");
const { selectors } = require("../scrapeAmazon/selectors");
const getTargetURL = require("../../utils/multicountry/getTargetURL");
const { uploadImage, getS3Url } = require("../aws/s3");
const fuzzysort = require("fuzzysort");
const { closePages } = require("../../utils/puppeteer/browserHelper");
const IMAGE_FOLDER = "pdptest/";

async function getPDPAdSearch(asin, sellerName, brand, company_id, TARGET_URL) {
//   console.log(brand, "brand");
  let browser = null;
  try {
    if (!TARGET_URL) {
      TARGET_URL = await getTargetURL(company_id);
    }

    const { browser: _browser, page } = await connectPuppeteer(
      `${TARGET_URL}/dp/${asin}`,
      undefined,
      company_id
    );
    browser = _browser;

    const sponsoredElements = await page.evaluate((brand) => {
      const products = [];
      const carousels = Array.from(
        document.querySelectorAll("[data-a-carousel-options]")
      ).filter((carousel) =>
        carousel.querySelector(".sp_desktop_sponsored_label")
      );

      // const iframes = Array.from(document.querySelectorAll('iframe[name]')).filter(iframe => {
      //     const containsBrandOrSeller = iframe.name.toLowerCase().includes(brand.toLowerCase())
      //     const rect = iframe.getBoundingClientRect();
      //     products.push({
      //         title,
      //         asin,
      //         isSponsored: true,
      //         containsBrandOrSeller,
      //         x: rect.x,
      //         y: rect.y,
      //         width: rect.width,
      //         height: rect.height
      //     });
      // })

      carousels.forEach((carousel) => {
        const rect = carousel.getBoundingClientRect();
        products.push({
          isCarousel: true,
          x: rect.x,
          y: rect.y,
          width: rect.width,
          height: rect.height,
        });

        const cards = carousel.querySelectorAll("li.a-carousel-card");
        cards.forEach((card) => {
          const cardRect = card.getBoundingClientRect();

          const titleElement = card.querySelector("div.sponsored-products-truncator-truncated");
          const title = titleElement?.textContent?.trim();
          if (title) {
            products.push({
              title,
              isSponsored: true,
              containsBrandOrSeller: false,
              x: cardRect.x,
              y: cardRect.y,
              carouselEndX: rect.x + rect.width,
              width: cardRect.width,
              height: cardRect.height,
            });
          }
        });
      });

      return products;
    }, brand);

    // Add brand/seller name checking logic
    sponsoredElements.forEach((el) => {
      let containsBrandOrSeller = false;
      const title = el.title?.toLowerCase() || "";

      if (brand && brand.trim()) {
        const cleanBrand = brand.toLowerCase().trim();
        if (title === cleanBrand) {
          containsBrandOrSeller = true;
        } else {
          const brandWords = cleanBrand
            .split(/[\s-]+/)
            .filter((word) => word.length > 3);
          const titleWords = title.split(/[\s-]+/);

          const significantMatch = brandWords.every((brandWord) =>
            titleWords.some(
              (titleWord) =>
                titleWord === brandWord ||
                (titleWord.length > 4 && titleWord.includes(brandWord))
            )
          );

          if (significantMatch) {
            const fuzzyScore = fuzzysort.single(cleanBrand, title)?.score;
            containsBrandOrSeller =
              fuzzyScore !== undefined && fuzzyScore > -50;
          }
        }
      }

      if (!containsBrandOrSeller && sellerName && sellerName.trim()) {
        const cleanSeller = sellerName.toLowerCase().trim();
        if (title === cleanSeller) {
          containsBrandOrSeller = true;
        } else {
          const sellerWords = cleanSeller
            .split(/[\s-]+/)
            .filter((word) => word.length > 3);
          const titleWords = title.split(/[\s-]+/);

          const significantMatch = sellerWords.every((sellerWord) =>
            titleWords.some(
              (titleWord) =>
                titleWord === sellerWord ||
                (titleWord.length > 4 && titleWord.includes(sellerWord))
            )
          );

          if (significantMatch) {
            const fuzzyScore = fuzzysort.single(cleanSeller, title)?.score;
            containsBrandOrSeller =
              fuzzyScore !== undefined && fuzzyScore > -50;
          }
        }
      }

      el.containsBrandOrSeller = containsBrandOrSeller;
    });

    // Filter elements that don't contain brand or seller name
    const nonMatchingElements = sponsoredElements.filter(
      (el) => !el.containsBrandOrSeller && !el.isCarousel
    );

    // Draw boxes only for non-matching elements
    for (const element of nonMatchingElements) {
      await page.evaluate((el) => {
        const div = document.createElement("div");
        div.style.position = "absolute";
        div.style.left = `${el.x}px`;
        div.style.top = `${el.y}px`;
        div.style.width = `${el.width}px`;
        div.style.height = `${el.height}px`;
        div.style.border = "2px solid red";
        div.style.boxSizing = "border-box";
        div.style.pointerEvents = "none";
        div.style.zIndex = "999999";
        document.body.appendChild(div);
      }, element);
    }

    // Capture carousel screenshots
    const carousels = sponsoredElements.filter((el) => el.isCarousel);
    for (let i = 0; i < carousels.length; i++) {
      const carousel = carousels[i];
      const screenshot = await page.screenshot({
        encoding: "binary",
        clip: {
          x: carousel.x - 60,
          y: carousel.y - 120,
          width: carousel.width + 120,
          height: carousel.height + 160,
        },
        // path: `pdp_ss_${asin}_image_${i + 1}.png`
      });

      await uploadImage(
        screenshot,
        `pdp_ss_${asin}_image_${i + 1}.png`,
        IMAGE_FOLDER
      );
    }
    // try {
    //     await page.waitForNavigation({ timeout: 10000 });
    // } catch (e) {

    // }
    const fullPageScreenshot = await page.screenshot({
      encoding: "binary",
      fullPage: true,
    });
    await uploadImage(
      fullPageScreenshot,
      `pdp_${asin}_ad_full_page_result.png`,
      IMAGE_FOLDER
    );

    await page.evaluate(() => {
      const divsToUpdate = document.querySelectorAll(
        'div[style*="border: 2px solid red"]'
      );
      divsToUpdate.forEach((div) => div.removeAttribute("style"));
    });

    const groupImages = [];
    const carouselImages = [];
    let fullPageImage = "";

    // Add carousel images
    for (let i = 0; i < carousels.length; i++) {
      carouselImages.push(
        getS3Url(`pdp_ss_${asin}_image_${i + 1}.png`, IMAGE_FOLDER)
      );
    }

    // Add full page image
    fullPageImage = getS3Url(
      `pdp_${asin}_ad_full_page_result.png`,
      IMAGE_FOLDER
    );

    const sponsoredCount = sponsoredElements.length;
    const hasSponsored = sponsoredCount > 0;
    return {
      count: sponsoredCount,
      hasSponsored,
      sponsoredElements,
      images: {
        carousels: carouselImages,
        fullPage: fullPageImage,
      },
    };
  } catch (error) {
    console.error("An error occurred:", error);
    throw error;
  } finally {
    await closePages(browser);
  }
}
module.exports = getPDPAdSearch;

async function Example() {
  try {
    const asin = "B0CH9VFR8H"; // Example ASIN
    const sellerName = "freego official store";
    const brand = "freego";
    const company_id = 20;
    const TARGET_URL = "https://www.amazon.com";
    const file_name = "PDP_Ads_Search";

    const response = await getPDPAdSearch(
      asin,
      sellerName,
      brand,
      company_id,
      TARGET_URL
    );

    console.log({ response });
    // console.log("Raw Response:", JSON.stringify(response, null, 2));

    // await saveToCSV(response, file_name);
  } catch (error) {
    console.error("An error occurred:", error);
    throw error;
  }
}

// Example();
