function getAdsReport(data, report) {
  // Deprecated running_ads data
  const adsStatus = data.running_ads;
  const prospectRevenue = Math.round(
    data.productData[0].price * data.productData[0].sales
  );

  // console.log("Ads: ", adsStatus);
  if (!adsStatus) {
    const improvementMessage = prospectRevenue >= 20 && prospectRevenue <= 80
      ? "Amazon ads perform best for products that are $20 - $80 (fitting your range)."
      : "Our expertise is in running ads for expensive products which are $100+.";

    report.push({
      DATA_POINT: "Ads",
      PRIORITY: "Urgent",
      Logic: "Not Present",
      PAIN_POINT:
        "Was searching your company name on Amazon from multiple US pincodes but didn't see any ads from you guys.",
      Improvements: [
        `You should instantly start running ads, they are the best way you can scale on Amazon.`,
        improvementMessage,
        `At your stage of the business, I think you should start with Sponsored product ads.`,
        `Make sure campaigns are structured properly.`,
      ],
      Benefits: ["Profit ↑", "Visibility & SEO ↑"],
    });
  }
}

module.exports = getAdsReport;