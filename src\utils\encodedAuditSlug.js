/**
 * Clean URL encoding utilities for audit page parameters
 * Uses base64 encoding with optional secret key for compact, URL-safe representation
 * URL format: /audit/product-slug/encoded-email/encoded-seller-id/encoded-client-id/
 */

// Default secret key (single digit 0-9)
const DEFAULT_SECRET = 5;

/**
 * Encode any string to URL-safe base64 format with optional secret key
 * @param {string} input - String to encode
 * @param {number} secretKey - Single digit secret key (0-9) for XOR obfuscation
 * @returns {string} - Encoded string
 */
function encodeString(input, secretKey = DEFAULT_SECRET) {
  if (!input || typeof input !== 'string') return '';
  
  try {
    // Validate secret key
    if (typeof secretKey !== 'number' || secretKey < 0 || secretKey > 9) {
      secretKey = DEFAULT_SECRET;
    }
    
    // Convert string to bytes
    const bytes = new TextEncoder().encode(input);
    
    // Apply XOR with secret key to each byte for simple obfuscation
    const obfuscatedBytes = bytes.map(byte => byte ^ secretKey);
    
    // Convert to base64 and make URL-safe
    const base64 = btoa(String.fromCharCode(...obfuscatedBytes));
    
    // Make URL-safe by replacing characters
    return base64
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, ''); // Remove padding
  } catch (error) {
    console.error('Error encoding string:', error);
    return '';
  }
}

/**
 * Decode base64 format back to original string
 * @param {string} encoded - Encoded string
 * @param {number} secretKey - Single digit secret key (0-9) used for encoding
 * @returns {string} - Decoded string
 */
function decodeString(encoded, secretKey = DEFAULT_SECRET) {
  if (!encoded || typeof encoded !== 'string') return '';
  
  try {
    // Validate secret key
    if (typeof secretKey !== 'number' || secretKey < 0 || secretKey > 9) {
      secretKey = DEFAULT_SECRET;
    }
    
    // Restore base64 format
    let base64 = encoded
      .replace(/-/g, '+')
      .replace(/_/g, '/');
    
    // Add padding if needed
    const padding = '='.repeat((4 - base64.length % 4) % 4);
    base64 += padding;
    
    // Decode from base64
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    // Reverse XOR with secret key
    const originalBytes = bytes.map(byte => byte ^ secretKey);
    
    // Convert back to string
    return new TextDecoder().decode(new Uint8Array(originalBytes));
  } catch (error) {
    console.error('Error decoding string:', error);
    return '';
  }
}

/**
 * Create URL path with encoded segments
 * @param {object} data - Object containing email, sellerId, productSlug and optional secretKey
 * @returns {string} - URL path
 * The Audit URL is of the format: /audit/product-slug/encoded-email/encoded-seller-id/
 * The encoded-email and encoded-seller-id are encoded using the encodeString function with the secret key.
 * The campaign-id is not encoded.
 */
function createEncodedURL(data) {
  const { email, sellerId, productSlug, clientId, secretKey = DEFAULT_SECRET } = data;
  
  if (!productSlug) {
    throw new Error('Product slug is required');
  }
  
  let url = `/audit/${productSlug}`;
  
  // Add encoded segments if provided with a default value of ###
  if (email) {
    url += `/${encodeString(email, secretKey)}`;
  }else{
    url += `/${encodeString('###', secretKey)}`;
  }
  
  if (sellerId) {
    url += `/${encodeString(sellerId, secretKey)}`;
  }else{
    url += `/${encodeString('###', secretKey)}`;
  }

  if (clientId) {
    url += `/${encodeString(clientId, secretKey)}`;
  }else{
    url += `/${encodeString('###', secretKey)}`;
  }

  return url;
}

/**
 * Decode the URL to get the product slug, email, seller ID and client ID
 * @param {string} url - The URL to decode
 * @returns {object} - The decoded URL
 */
function decodedURL(url){
  try {
    // Remove leading slash and split
    const cleanUrl = url.startsWith('/') ? url.substring(1) : url;
    const segments = cleanUrl.split('/');
    
    // Expected format: audit/product-slug/encoded-email/encoded-seller-id/encoded-client-id
    if (segments.length < 2 || segments[0] !== 'audit') {
      throw new Error('Invalid URL format. Expected /audit/product-slug/...');
    }
    
    const productSlug = segments[1];
    const email = segments[2] ? decodeString(segments[2], DEFAULT_SECRET) : '';
    const sellerId = segments[3] ? decodeString(segments[3], DEFAULT_SECRET) : '';
    const clientId = segments[4] ? decodeString(segments[4], DEFAULT_SECRET) : '';

    // Filter out ### default values
    return {
      productSlug,
      email: email,
      sellerId: sellerId,
      clientId: clientId
    };
  } catch (error) {
    console.error('Error decoding URL:', error);
    return {
      productSlug: '',
      email: '',
      sellerId: '',
      clientId: '',
      error: error.message
    };
  }
}

if(require.main === module){
  // We will pass the objects in createEncodedURL and decodedURL and check if the decoded URL is the same as the original URL
  const productSlug = 'g-o-a-t-fuel-b0cg9zb3qf-4';
  const email = '<EMAIL>';
  const sellerId = '1234567890';
  const clientId = '1234567890';
  // Test With all the parameters
  const url = createEncodedURL({
    productSlug: productSlug,
    email: email,
    sellerId: sellerId,
    clientId: clientId
  });
  console.log(url);
  const decoded = decodedURL(url);
  console.log(decoded);
  // Test With no sellerId
  const url2 = createEncodedURL({
    productSlug: productSlug,
    email: email,
    clientId: clientId
  });
  console.log(url2);
  const decoded2 = decodedURL(url2);
  console.log(decoded2);
  // Test With no clientId
  const url3 = createEncodedURL({
    productSlug: productSlug,
    email: email,
    sellerId: sellerId
  });
  console.log(url3);
  const decoded3 = decodedURL(url3);
  console.log(decoded3);
  // Test With no email, sellerId and clientId
  const url4 = createEncodedURL({
    productSlug: productSlug,
  });
  console.log(url4);
  const decoded4 = decodedURL(url4);
  console.log(decoded4);
}



module.exports = {
  createEncodedURL,
  decodedURL
};