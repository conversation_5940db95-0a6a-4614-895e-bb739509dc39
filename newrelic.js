module.exports = {
  app_name: ["jeff-relic"],
  license_key: "1887721f25cb973c270cb5fd402745c4FFFFNRAL",
  logging: {
    level: "debug",
  },
  distributed_tracing: {
    enabled: true,
  },
  transaction_tracer: {
    record_sql: "raw", 
  },
  allow_all_headers: true,
  attributes: {
    exclude: [
      "request.headers.cookie",
      "request.headers.authorization",
      "request.headers.proxyAuthorization",
      "request.headers.setCookie*",
      "request.headers.x*",
      "response.headers.cookie",
      "response.headers.authorization",
      "response.headers.proxyAuthorization",
      "response.headers.setCookie*",
      "response.headers.x*",
    ],
  },
};
