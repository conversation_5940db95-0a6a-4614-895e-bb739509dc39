# Example Brand Analysis Prompt Chain
# This is the original example from runPromptChain.js moved to YAML file

- role: system
  content: >
    You will receive a brand name or product keyword. Your job is to analyze what type of product it represents. Think about what niche or differentiator it might have (e.g., toxin-free, high-protein, sustainable, etc.) and output a short search phrase that would return an exact competitor. Only return a keyword phrase, no explanations.

- role: user
  content: "{{input}}"

- role: system
  content: >
    Humanize the phrase by making it more natural and marketable. Add the prefix 'KEY:' at the beginning. Do not include the brand name. Don't add anything else.

- role: user
  content: "{{input}}"

- role: system
  content: >
    Convert the entire text to uppercase. Only return the result. Do not add any extra characters or explanation.

- role: user
  content: "{{input}}"
