{"name": "equal-collective", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon index.js && pnpm sentry:sourcemaps", "start": "node index.js && pnpm sentry:sourcemaps ", "debug:scrape": "DEBUG=\"worker-pool:*\" node ./scrapeAmazon/test.js", "debug": "DEBUG=\"worker-pool:*\" nodemon index.js", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org brand-buddy --project node node_modules && sentry-cli sourcemaps upload --org brand-buddy --project node node_modules"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-ec2": "^3.623.0", "@aws-sdk/client-lambda": "^3.622.0", "@aws-sdk/client-s3": "^3.621.0", "@aws-sdk/client-ses": "^3.812.0", "@azure/arm-compute": "^22.4.0", "@azure/arm-dns": "^5.1.0", "@azure/arm-network": "^34.0.0", "@azure/identity": "^4.10.0", "@bull-board/express": "^6.10.1", "@aws-sdk/client-ssm": "^3.623.0", "@aws-sdk/credential-provider-env": "^3.620.1", "@mozilla/readability": "^0.5.0", "@prisma/client": "^6.5.0", "@sentry/cli": "^2.31.0", "@sentry/node": "^7.110.1", "@sentry/profiling-node": "^7.110.1", "@sparticuz/chromium": "^126.0.0", "axios": "^1.6.7", "bcrypt": "^5.1.1", "bullmq": "^5.54.0", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "csv-parse": "^5.6.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "csvtojson": "^2.0.10", "debug": "^4.3.4", "dotenv": "^16.4.5", "eslint": "^9.29.0", "express": "^4.18.3", "express-session": "^1.18.0", "fuzzysort": "^3.1.0", "googleapis": "^144.0.0", "joi": "^17.13.3", "js-yaml": "^4.1.0", "jsdom": "^24.0.0", "json-2-csv": "^5.5.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.24", "multer": "^1.4.5-lts.1", "newrelic": "^12.13.0", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "nodemailer": "^6.9.13", "nodemon": "^3.1.0", "openai": "^4.50.0", "puppeteer": "22.8.2", "puppeteer-core": "^22.15.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "scrapingbee": "^1.7.5", "sqlite3": "^5.1.7", "string-similarity": "^4.0.4", "uuid": "^11.0.5", "workerpool": "^9.1.0", "xml-js": "^1.6.11"}, "devDependencies": {"prisma": "^6.5.0"}, "prisma": {"schema": "./src/database/prisma/schema.prisma"}}