const { google }  = require("googleapis");

const serviceAccountBase64 = process.env.GOOGLE_SERVICE_ACCOUNT_KEY;
// console.log("serviceAccountBase64", serviceAccountBase64);

if (!serviceAccountBase64) {
  throw new Error(
    "Missing GOOGLE_SERVICE_ACCOUNT_BASE64 environment variable."
  );
}

const serviceAccountJSON = JSON.parse(
  Buffer.from(serviceAccountBase64, "base64").toString("utf-8")
);
// console.log("serviceAccountJSON", serviceAccountJSON);

const auth = new google.auth.GoogleAuth({
  credentials: serviceAccountJSON,
  scopes: [
    "https://www.googleapis.com/auth/spreadsheets",
    "https://www.googleapis.com/auth/drive",
  ],
});

const sheets = google.sheets({ version: "v4", auth });
const drive = google.drive({ version: "v3", auth });

const createAndUploadToSheet = async (
  successData,
  unsuccessfulData,
  successHeaders,
  unsuccessfulHeaders
) => {
  try {
    console.log("Creating new Google Sheet...");
    const sheetResponse = await sheets.spreadsheets.create({
      requestBody: {
        properties: { title: `Generated Sheet - ${new Date().toISOString()}` },
      },
    });

    const sheetId = sheetResponse.data.spreadsheetId;
    console.log("Google Sheet Created:", sheetId);

    const formatCellValue = (value) => {
      if (value === undefined || value === null || Number.isNaN(value))
        return "";
      if (typeof value === "object") return JSON.stringify(value); // Handle JSON
      if (typeof value === "number") return value; // Keep numbers as numbers
      return value; // Default case (string remains string)
    };

    const batchUpdateRequests = [];

    if (successData.length) {
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId: sheetId,
        requestBody: {
          requests: [
            {
              updateSheetProperties: {
                properties: { title: "Success + Non-Revenue" },
                fields: "title",
              },
            },
          ],
        },
      });

      batchUpdateRequests.push({
        range: "Success + Non-Revenue!A1",
        valueInputOption: "RAW",
        requestBody: {
          values: [
            successHeaders,
            ...successData.map((row) =>
              successHeaders.map((h) => formatCellValue(row[h]))
            ),
          ],
        },
      });
    }

    if (unsuccessfulData.length) {
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId: sheetId,
        requestBody: {
          requests: [{ addSheet: { properties: { title: "Unsuccessful" } } }],
        },
      });

      batchUpdateRequests.push({
        range: "Unsuccessful!A1",
        valueInputOption: "RAW",
        requestBody: {
          values: [
            unsuccessfulHeaders,
            ...unsuccessfulData.map((row) =>
              unsuccessfulHeaders.map((h) => formatCellValue(row[h]))
            ),
          ],
        },
      });
    }

    await Promise.all(
      batchUpdateRequests.map((req) =>
        sheets.spreadsheets.values.update({ spreadsheetId: sheetId, ...req })
      )
    );

    console.log("Google Sheet Updated Successfully");
    return sheetId;
  } catch (error) {
    console.error("Error creating or updating Google Sheet:", error);
    return null;
  }
};

const makeSheetPublic = async (sheetId) => {
  try {
    await drive.permissions.create({
      fileId: sheetId,
      requestBody: {
        role: "writer", // Set as 'writer' so anyone can edit
        type: "anyone", // Available to anyone with the link
      },
      auth: drive.auth, // Ensure authentication is passed
    });

    console.log(
      `✅ Google Sheet is now public: https://docs.google.com/spreadsheets/d/${sheetId}`
    );
    return `https://docs.google.com/spreadsheets/d/${sheetId}`;
  } catch (error) {
    console.error("❌ Error making sheet public:", error);
    return null;
  }
};

const createGoogleSheet = async (data) => {
  try {
    let retries = 3;
    let lastError = null;

    while (retries > 0) {
      try {
        const sheetResponse = await sheets.spreadsheets.create({
          requestBody: {
            properties: { title: `Generated Sheet - ${new Date().toISOString()}` },
          },
        });
        const sheetId = sheetResponse.data.spreadsheetId;
        console.log("Google Sheet Created:", sheetId);

        // upload data to sheet
        await sheets.spreadsheets.values.update({
          spreadsheetId: sheetId,
          range: "A1",
          valueInputOption: "RAW",
          requestBody: { values: data },
        });

        return sheetId;
      } catch (error) {
        lastError = error;
        retries--;
        if (retries > 0) {
          console.log(`Retrying Google Sheets API call. ${retries} attempts remaining...`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
        }
      }
    }
    throw lastError;

  } catch (error) {
    console.error("Error creating Google Sheet:", error);
    return null;
  }
}

module.exports = { createAndUploadToSheet, makeSheetPublic, createGoogleSheet };