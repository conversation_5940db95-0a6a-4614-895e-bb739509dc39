const express = require("express");
const router = express.Router();
const prisma = require("../database/prisma/getPrismaClient"); // Adjust the path as necessary
const { userAuth } = require("../middlewares/jwt");
const multer = require("multer");
const upload = multer({ dest: "uploads/" });
const csvParser = require("../utils/csvParser"); // Adjust the path as necessary
const converter = require("json-2-csv"); // Ensure you have this package installed
const generatePDF = require("../utils/scrapeAmazonUtils/generatePDF");
const { isValidS3Link, uploadCsv} = require("../services/aws/s3");
const { addToQueue } = require("../utils/bull/bull");

// Get All Jobs for the User
router.get("/api/jobs/:clientId", userAuth, async (req, res) => {
  try {
    // Extract pagination parameters with validation
    const { 
      page = 1, 
      pageSize = 20, 
      sortBy = 'updatedAt', 
      sortOrder = 'desc' 
    } = req.query;
    
    const clientId = parseInt(req.params.clientId);
    const pageNum = Math.max(1, parseInt(page));
    const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize))); // Limit to max 100 items per page
    const skip = (pageNum - 1) * pageSizeNum;
    
    // Validate sort parameters
    const validSortFields = ['id', 'name', 'status', 'createdAt', 'updatedAt', 'qualifiedLeads'];
    const validSortOrders = ['asc', 'desc'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'updatedAt';
    const sortDirection = validSortOrders.includes(sortOrder) ? sortOrder : 'desc';

          // Common query options
    const queryOptions = {
      skip,
      take: pageSizeNum,
      orderBy: { [sortField]: sortDirection },
      include: {
        OutputData: {
          select: { id: true, campaignName: true, mailData: true, productSlug: true },
        },
      },
    };

    // Execute queries in parallel for better performance
    const [
      jobs,
      jobsCount,
      singleJobs,
      singleJobsCount
    ] = await Promise.all([
      // Fetch regular jobs with pagination
      prisma.job.findMany({
        where: { clientId, singleCompany: false },
        ...queryOptions,
      }),
      // Get total count of regular jobs
      prisma.job.count({
        where: { clientId, singleCompany: false },
      }),
      // Fetch single company jobs with pagination
      prisma.job.findMany({
        where: { clientId, singleCompany: true },
        ...queryOptions,
      }),
      // Get total count of single company jobs
      prisma.job.count({
        where: { clientId, singleCompany: true },
      }),
    ]);

    // Transform regular jobs response
    const jobsResponse = jobs.map((job) => ({
      id: job.id,
      fileName: job.name,
      csvStatus: job.status,
      totalLeads: job.OutputData.length,
      qualifiedLeads: job.qualifiedLeads,
      campaignName: job.OutputData[0]?.campaignName,
      statusMap: job.statusMap,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
    }));

    // Transform single company jobs response
    const singleJobsResponse = singleJobs.map((job) => ({
      id: job.id,
      fileName: job.name,
      csvStatus: job.status,
      totalLeads: job.OutputData.length,
      campaignName: job.OutputData[0]?.campaignName,
      statusMap: job.statusMap,
      data: {
        mailData: job.OutputData[0]?.mailData,
        webpageLink: job.OutputData[0]?.productSlug
          ? `https://www.equalcollective.com/jeff/audit/${job.OutputData[0].productSlug}`
          : "",
        pdfLink: job.OutputData[0]?.productSlug
          ? `https://eq--assets.s3.ap-south-1.amazonaws.com/pdfs/${job.OutputData[0].productSlug}.pdf`
          : "",
      },
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
    }));

    // Calculate pagination metadata
    const jobsPagination = {
      currentPage: pageNum,
      pageSize: pageSizeNum,
      totalItems: jobsCount,
      totalPages: Math.ceil(jobsCount / pageSizeNum),
      hasNextPage: pageNum < Math.ceil(jobsCount / pageSizeNum),
      hasPreviousPage: pageNum > 1,
    };

    const singleJobsPagination = {
      currentPage: pageNum,
      pageSize: pageSizeNum,
      totalItems: singleJobsCount,
      totalPages: Math.ceil(singleJobsCount / pageSizeNum),
      hasNextPage: pageNum < Math.ceil(singleJobsCount / pageSizeNum),
      hasPreviousPage: pageNum > 1,
    };

    const finalResponse = {
      jobs: {
        data: jobsResponse,
        pagination: jobsPagination,
      },
      singleJobs: {
        data: singleJobsResponse,
        pagination: singleJobsPagination,
      },
      meta: {
        sortBy: sortField,
        sortOrder: sortDirection,
        appliedFilters: {
          clientId,
          page: pageNum,
          pageSize: pageSizeNum,
        },
      },
    };

    res.status(200).json(finalResponse);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching jobs:", error.message);
    res.status(500).json({ error: "Internal server error" });
  }
});

router.get(
  "/api/jobs/download-input-csv/:jobId",
  userAuth,
  async (req, res) => {
    try {
      const jobId = parseInt(req.params.jobId);
      const job = await prisma.job.findUnique({ where: { id: jobId } });
      if (!job) {
        return res.status(404).json({ error: "Job not found" });
      }
      // Check if the user is authorized to access this job
      // if (job.clientId !== req.user.userId && req.user.userType !== "admin") {
      //   return res.status(403).json({ error: "Unauthorized" });
      // };
      const inputCSV = await prisma.outputData.findMany({
        where: {
          jobId,
        },
        select: {
          sellerDetails: true,
        },
      });

      const csvContent = converter.json2csv(inputCSV);

      // Set the filename for the CSV file
      const inputCsvFileName = `input_data_${job.id}.csv`;

      // Send the CSV file in response
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${inputCsvFileName}`
      );
      res.setHeader("Content-Type", "text/csv");
      res.status(200).send(csvContent);
    } catch (error) {
      console.error("Error fetching output data:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// Post a Job
router.post(
  "/api/jobs/:clientId",
  userAuth,
  upload.single("csvFile"),
  async (req, res) => {
    try {
      const userId = req.params.clientId;
      const campaignId = req.body.campaignId;
      let job;
      let scriptType;

      if (req.file) {
        const csvFilePath = req.file.path;
        const originalFileName = req.file.originalname;
        job = await prisma.job.create({
          data: {
            clientId: parseInt(userId),
            status: "pending",
            name: originalFileName,
            ppcAudit: req.query.ppcAudit == "true",
            campaignId: parseInt(campaignId) || 1,
          },
        });
        await csvParser.processCSV(csvFilePath, parseInt(userId), job);
        scriptType = "bulkJeff";
        await addToQueue(scriptType, { jobId: job.id, clientId: job.clientId });
        // csvParser.parseAndInsertCSV(csvFilePath, userId, originalFileName);

        res.json({ message: "File uploaded successfully" });
      } else if (req.body.companyName) {
        try {
          const row = {
            "Seller Name": req.body.companyName || "",
            "Best Selling Product URL": req.body.productUrl || "",
            "Seller Storefront Link": req.body.storeFrontLink || "",
            "Competitor Product Link": req.body.competitorProductUrl || "",
          };
          const clientId = parseInt(userId);

          if (
            !row["Seller Name"] &&
            (!row["Best Selling Product URL"] || !row["Seller Storefront Link"])
          ) {
            console.log("Fields missing", { row });
            res.status(400).json({
              status:
                "Fields missing. Please provide company name, website, and first name",
            });
          }
          job = await prisma.job.create({
            data: {
              clientId: parseInt(userId),
              status: "pending",
              singleCompany: true,
              ppcAudit: req.body.ppcAudit,
              name: req.body.companyName + "-singlejob",
              campaignId: parseInt(campaignId) || 1,
            },
          });
          await csvParser.processJsonData(row, clientId, job);
        } catch (error) {
          console.error("Error Stack:", error.stack);
          console.error("Error posting job:", error.message);
          res.status(500).json({ error: "Internal server error" });
        }
        scriptType = "singleJeff";

        // Process the product URL (e.g., generate PDF, etc.)
        await addToQueue(scriptType, { jobId: job.id, clientId: job.clientId });
        res.json({ message: "Product URL processed successfully" });
      } else {
        res.status(400).json({ error: "No file or product URL provided" });
      }
    } catch (error) {
      console.error("Error Stack:", error.stack);
      console.error("Error posting job:", error.message);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// API to add new campaign
router.post("/api/add-campaign", userAuth, async (req, res) => {
  try {
    const campaign = req.body.campaign;
    const newCampaign = await prisma.campaign.create({
      data: {
        campaign,
      },
    });
    res.status(200).json({ newCampaign });
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error adding campaign :", error.message);
    res.status(500).json({ error: "Internal server error" });
  }
});

// API to fetch campaigns
router.get("/api/fetch-campaigns", userAuth, async (req, res) => {
  try {
    const campaigns = await prisma.campaign.findMany();
    res.status(200).json({ campaigns });
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error adding campaign :", error.message);
    res.status(500).json({ error: "Internal server error" });
  }
});

// API to download jobs data as CSV
router.get(
  "/api/jobs/download-csv/:jobId/:slug?",
  userAuth,
  async (req, res) => {
    try {
      function formatRevenue(revenue) {
        if (revenue >= 1000000) {
          return (revenue / 1000000).toFixed() + "M";
        } else if (revenue >= 1000) {
          return (revenue / 1000).toFixed() + "K";
        } else {
          return Math.round(revenue).toString() || revenue;
        }
      }
      const jobId = parseInt(req.params.jobId);
      const slug = req.params.slug;
      const forceDownload = req.query.forceDownload || false;
      console.log("Downloading CSV for Job ID:", jobId);
      console.log("Downloading CSV for Slug:", slug);
      // Find the Job with the given ID
      const job = await prisma.job.findUnique({ where: { id: jobId } });
      if (!job) {
        return res.status(404).json({ error: "Job not found" });
      }
      if (!forceDownload){
        // If it is an bulk job and it has a csv link in the statusMap, then return the csv link
        if (!job.singleCompany && slug === "success-with-revenue" 
          && job.statusMap["success-with-revenue"] 
          && isValidS3Link(job.statusMap["success-with-revenue"])){
          return res.status(200).json({ data: job.statusMap["success-with-revenue"] });
        }
        // If it is an bulk job and it has a csv link in the statusMap, then return the csv link
        if (!job.singleCompany && slug === "success-without-revenue" 
          && job.statusMap["success-without-revenue"] 
          && isValidS3Link(job.statusMap["success-without-revenue"])){
          return res.status(200).json({ data: job.statusMap["success-without-revenue"] });
        }
        // If it is an bulk job and it has a csv link in the statusMap, then return the csv link
        if (!job.singleCompany && slug === "unsuccessful" 
          && job.statusMap["unsuccessful"] 
          && isValidS3Link(job.statusMap["unsuccessful"])){
          return res.status(200).json({ data: job.statusMap["unsuccessful"] });
        }
        // If it is an bulk job and it has a csv link in the statusMap, then return the csv link
        if (!job.singleCompany && slug === "all" 
          && job.statusMap["all"] 
          && isValidS3Link(job.statusMap["all"])){
          return res.status(200).json({ data: job.statusMap["all"] });
        }
      }
      // Check if the user is authorized to access this job
      if (job.clientId !== req.user.userId && req.user.userType !== "admin") {
        return res.status(403).json({ error: "Unauthorized" });
      }
      // console.log(job.singleCompany);
      if (job.singleCompany) {
        const response = await prisma.outputData.findMany({
          where: {
            jobId,
          },
        });
        const data = response.map((obj) => ({
          mailData: obj.mailData || "Couldn't Generate the Mail Data",
          webpageLink: obj.finalData["Audit webpage link"] || "",
          pdfLink: obj.finalData["Audit PDF link"] || "",
        }));
        console.log({ response, data });
        return res.status(200).json({ data });
      }

      let outputData = null;
      if (slug === "success-with-revenue") {
        // Fetch all data from the OutputData table where jobId matches and revenue > 0
        outputData = await prisma.outputData.findMany({
          where: {
            jobId,
            prospectDetails: {
              path: ["revenue"],
              gt: 0,
            },
            mailData: {
              not: "",
            },
          },
        });
      } else if (slug === "success-without-revenue") {
        // Fetch all data from the OutputData table where jobId matches and revenue = 0
        outputData = await prisma.outputData.findMany({
          where: {
            jobId,
            prospectDetails: {
              path: ["revenue"],
              lte: 0,
            },
            mailData: {
              not: "",
            },
          },
        });
      } else if (slug === "unsuccessful") {
        // Fetch all data from the OutputData table where jobId matches and qualificationStatus is "unqualified"
        outputData = await prisma.outputData.findMany({
          where: {
            jobId,
            mailData: "",
          },
        });
      } else {
        // Fetch all data from the OutputData table where jobId matches
        outputData = await prisma.outputData.findMany({
          where: {
            jobId,
          },
        });
      }

      // Transform the data for CSV conversion
      outputData = outputData.map((obj) => ({
        "Seller name": obj.companyName || "",
        "Seller name (humanized)":
          obj.prospectDetails.humanizedProspectName || "",
        "Seller Website": obj.website || "",
        "Prospect LinkedIn URL":
          obj.sellerDetails["Prospect LinkedIn URL"] || "",
        "Storefront URL": obj.sellerDetails["Seller Storefront Link"] || "",
        "Prospect First Name": obj.sellerDetails["First Name"] || "",
        "Prospect Second Name": obj.sellerDetails["Second Name"] || "",
        "Job Title": obj.position || "",
        Email: obj.email || "",
        "Competitor Email": obj.mailData || "",
        Status: slug,
        "BSR Category":
          Array.isArray(obj.category) && obj.category.length > 0
            ? obj.category.find(
                (c) =>
                  c.category &&
                  typeof c.category === "string" &&
                  c.category.trim()
              )?.category || ""
            : typeof obj.category === "string"
            ? obj.category
            : JSON.stringify(obj.category || ""),
        "Prospect product URL": obj.prospectDetails.productAmazonURL || "",
        "Prospect ASIN": obj.prospectDetails.asin || "",
        "Competitor Product URL": obj.competitorDetails.productAmazonURL || "",
        "Competitor ASIN":obj.competitorDetails.asin || "",
        "Audit webpage link": obj.finalData["Audit webpage link"] || "",
        "Audit PDF link": obj.finalData["Audit PDF link"] || "",
        "Amazon search URL": obj.amazonSearchUrl || "",
        "Prospect Product Title (Humanized)":
          obj.prospectDetails.humanizedProspectProductTitle || "",
        "Competitor Product Title (Humanized)":
          obj.competitorDetails.humanizedCompProductTitle || "",
        "Revenue difference monthly": formatRevenue(obj.revenueDifference) || 0,
        "Revenue difference yearly":
          formatRevenue(obj.revenueDifference * 12) || 0,
        "Competitor brand name (humanized)":
          obj.competitorDetails.humanizedCompCompanyName || "",
        "Prospect revenue":
          formatRevenue(obj.prospectDetails?.revenue || 0) || 0,
        "Competitor revenue":
          formatRevenue(obj.competitorDetails?.revenue || 0) || 0,
        "Annual Competitor Revenue":
          formatRevenue(obj.competitorDetails?.revenue * 12 || 0) || 0,
        "Video: calculated value":
          formatRevenue(obj.prospectDetails?.revenue * 0.097 || 0) || 0,
        "Revenue Source": obj.prospectDetails.revenueSource || "",
        "Number of optimization": Object.keys(obj.auditReport).length || 0,
        "Revenue source": obj.prospectDetails.revenueSource || "",
        "User Prompt": JSON.stringify(obj.userPrompt),
        "Prospect Details": JSON.stringify(obj.prospectDetails),
        "Competitor Details": JSON.stringify(obj.competitorDetails),
        "Amazon Audit": JSON.stringify(obj.amazonAudit),
        "Audit Report": JSON.stringify(obj.auditReport),
        "1 Star Reviews": obj.finalData["1 Star Reviews"],
        "2 Star Reviews": obj.finalData["2 Star Reviews"],
        "1 and 2 Star Reviews": obj.finalData["1 and 2 Star Reviews"],
        "Total Number Of Ratings": obj.finalData["Total Number Of Ratings"],
        "Number Of Stars": obj.finalData["Number Of Stars"],
        "Number Of Stars Comes As": obj.finalData["Number Of Stars Comes As"],
        "Goal for Number of stars": obj.finalData["Goal for Number of stars"],
        "Goal for Number of stars 'comes as' ":
          obj.finalData["Goal for Number of stars 'comes as' "],
        "Number of 5* ratings needed":
          obj.finalData["Number of 5* ratings needed"],
        "Minimum number of 1* & 2* to be removed": JSON.stringify(
          obj.finalData["Minimum number of 1* & 2* to be removed"]
        ),
        "Branded Keyword": obj.auditMailData?.brandedKeyword || "",
        "Non Branded Keyword": obj.auditMailData?.nonBrandedKeyword || "",
        "Main Image Optimisation":
          obj.auditMailData?.mainImageOptimisationText || "",
        "Data Point 1": obj.auditMailData?.Data_Point_1 || "",
        "Pain Point 1": obj.auditMailData?.Pain_Point_1 || "",
        "Top Improvement 1": obj.auditMailData?.Top_Improvement_1 || "",
        "Data Point 2": obj.auditMailData?.Data_Point_2 || "",
        "Pain Point 2": obj.auditMailData?.Pain_Point_2 || "",
        "Top Improvement 2": obj.auditMailData?.Top_Improvement_2 || "",
        "Data Point 3": obj.auditMailData?.Data_Point_3 || "",
        "Pain Point 3": obj.auditMailData?.Pain_Point_3 || "",
        "Top Improvement 3": obj.auditMailData?.Top_Improvement_3 || "",
        "Page Image": obj.finalData["Page Image"] || "",
        "Product Image": obj.finalData["Product Image"] || "",
        "Audit Mail Image": obj.finalData["Audit Mail Image"] || "",
        "Case Studies": JSON.stringify(obj.caseStudies),
        ID: obj.id,
        "Prompt Tokens": obj.promptTokens || 0,
        "Completion Tokens": obj.completionTokens || 0,
        "Homepage Data Status": obj.homepageDataStatus || "not-found",
        "Prompt Template": obj.promptTemplate || "",
        "GPT Details": obj.gptDetails || "",
        "Input Price": obj.inputPrice || 0.0,
        "Output Price": obj.outputPrice || 0.0,
        "Comp Key Prompt": obj.compKeyPrompt || "",
        "Search Keyword": obj.searchKeyword || "",
        "Company Slug": obj.companySlug || "",
        "Product Slug": obj.productSlug || "",
        "Job ID": obj.jobId,
        "Qualification Status": obj.qualificationStatus || "",
        "Amazon Status": obj.amazonDataStatus || "",
        "PPC Report": JSON.stringify(obj.ppcAudit),
        "Scraper API Credits Used": obj.pricing?.scraperApi?.creditUsed || 0,
        "Scraping Bee Credits Used": obj.pricing?.scrapingBee?.creditUsed || 0,
        "Jungle Scout Credits Used": obj.pricing?.jungleScout?.creditUsed || 0,
        "Chat GPT Input Credits Used": obj.pricing?.chatGPT?.inputToken || 0,
        "Chat GPT Output Credits Used": obj.pricing?.chatGPT?.outputToken || 0,
        "Scraper API Per Credit Cost (150)": 0.00015,
        "Scraping Bee Credit Cost (150)": 0.0001,
        "Jungle Scout Credit Cost (178)": 0.0445,
        "Chat GPT Input Per Token Cost": 0.0000025,
        "Chat GPT Output Per Token Cost": 0.00001,
        "Scraper API Total Cost": obj.pricing?.scraperApi?.creditUsed * 0.00015,
        "Scraping Bee Total Cost":
          obj.pricing?.scrapingBee?.creditUsed * 0.0001,
        "Jungle Scout Total Cost":
          obj.pricing?.jungleScout?.creditUsed * 0.0445,
        "Chat GPT Total Cost":
          obj.pricing?.chatGPT?.inputToken * 0.0000025 +
          obj.pricing?.chatGPT?.outputToken * 0.00001,
        "Total Cost":
          obj.pricing?.scraperApi?.creditUsed * 0.00015 +
          obj.pricing?.scrapingBee?.creditUsed * 0.0001 +
          obj.pricing?.jungleScout?.creditUsed * 0.0445 +
          (obj.pricing?.chatGPT?.inputToken * 0.0000025 +
            obj.pricing?.chatGPT?.outputToken * 0.00001),
        "Encoded Audit URL": obj.finalData["encodedauditurl"] || "",
        "Encoded Audit Slug": obj.finalData["encodedauditslug"] || "",
        "Company ID": obj.companyId,
        "Created At": obj.createdAt,
        "Updated At": obj.updatedAt,
      }));
      const csvContent = converter.json2csv(outputData);

      // Set the filename for the CSV file
      const analyzableCsvFileName = `job_data_${slug}_${job.id}.csv`;

      // Upload Single Job CSV to S3 and update the statusMap with the csv link
      const s3Response = await uploadCsv(csvContent, analyzableCsvFileName);
      const statusKey = slug + "CSVLink";
      await prisma.job.update({
        where: { id: jobId },
        data: { statusMap: { ...job.statusMap, [statusKey]: s3Response.url } },
      });

      // Send the CSV file in response
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${analyzableCsvFileName}`
      );
      res.setHeader("Content-Type", "text/csv");
      res.status(200).send(csvContent);
    } catch (error) {
      console.error("Error fetching output data:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.get("/api/regenerate_pdf/:slug", async (req, res) => {
  try {
    const slug = req.params.slug;
    const report = await prisma.amazonAuditReport.findFirst({
      where: {
        slug: slug,
      },
    });
    // Send all data to the client
    await generatePDF(report.finalUrl, report.companyName);

    res.status(200).json({ Status: "Success" });
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching jobs:", error.message);
    res.status(500).json({ error: "Internal server error" });
  }
});

// API to generate QA summary report as CSV
router.get(
  "/api/jobs/qa-summary/:jobId",
  userAuth,
  async (req, res) => {
    try {
      const jobId = parseInt(req.params.jobId);
      console.log("Generating QA Summary for Job ID:", jobId);

      // Find the Job with the given ID
      const job = await prisma.job.findUnique({ where: { id: jobId } });
      if (!job) {
        return res.status(404).json({ error: "Job not found" });
      }

      // Fetch all output data for the job
      const outputData = await prisma.outputData.findMany({
        where: { jobId },
      });

      // Calculate QA metrics
      const qaMetrics = {
        "Total Leads": outputData.length,
        "Number of Success": outputData.filter(
          (obj) => obj.mailData && obj.mailData !== ""
        ).length,
        "Number of Unsuccessful": outputData.filter(
          (obj) => !obj.mailData || obj.mailData === ""
        ).length,
        "Numbe of Success But No Revenue": outputData.filter(
          (obj) => obj.revenue <= 0
        ).length,
        "Number of Page Images Not Found": outputData.filter(
          (obj) => !obj.finalData["Page Image"]
        ).length,
        "Number of Product Images Not Found": outputData.filter(
          (obj) => !obj.finalData["Product Image"]
        ).length,
        "Number of Audit Mail Images Not Found": outputData.filter(
          (obj) => !obj.finalData["Audit Mail Image"]
        ).length,
        "Pdf Created": outputData.filter(
          (obj) => (obj.finalData["Audit PDF link"])
        ).length,
        "Webpage Link Created": outputData.filter(
          (obj) => (obj.finalData["Audit webpage link"])
        ).length,
      };

    res.status(200).send(qaMetrics);
  } catch (error) {
    console.error("Error generating QA summary:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// API to get pricing
router.get("/api/pricing",userAuth, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({ error: "startDate and endDate are required" });
    }

    const entries = await prisma.outputData.findMany({
      where: {
        updatedAt: {
          gte: new Date(startDate ),
          lte: new Date(endDate),
        },
      },
      select: {
        pricing: true,
      },
    });

    let total = {
      totalLeads: 0,
      scraperApiCredits: 0,
      scrapingBeeCredits: 0,
      jungleScoutCredits: 0,
      chatGPTInputToken: 0,
      chatGPTOutputToken: 0,
      scraperApiPagesScraped: 0,
    };
    total.totalLeads = entries.length;

    for (const obj of entries) {
      total.scraperApiCredits += obj.pricing?.scraperApi?.creditUsed || 0;
      total.scrapingBeeCredits += obj.pricing?.scrapingBee?.creditUsed || 0;
      total.jungleScoutCredits += obj.pricing?.jungleScout?.creditUsed || 0;
      total.chatGPTInputToken += obj.pricing?.chatGPT?.inputToken || 0;
      total.chatGPTOutputToken += obj.pricing?.chatGPT?.outputToken || 0;
      total.scraperApiPagesScraped +=
        obj.pricing?.scraperApi?.pages?.length || 0;
    }

    res.status(200).json(total);
  } catch (error) {
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
});


module.exports = router;
