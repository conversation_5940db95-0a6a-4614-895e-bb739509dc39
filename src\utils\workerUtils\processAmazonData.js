const prisma = require("../../database/prisma/getPrismaClient");

async function processAmazonData(userPrompt, csvData, dataFlag) {
  try {
    const amazonData = await prisma.amazonProductData.findFirst({
      where: {
        companyId: csvData.companyId,
      },
    });

    if (!amazonData) {
      // dataFlag[0] = dataFlag[0] && false;
      return { status: "pending" };
    }

    if (amazonData.data) {
      userPrompt["Amazon Data"] = JSON.parse(JSON.stringify(amazonData.data));
      if (userPrompt["Amazon Data"]["productData"]) {
        userPrompt["Amazon Data"]["productData"] =
          userPrompt["Amazon Data"]["productData"][0];
      }
    }
    csvData["amazonSearchUrl"] = amazonData.searchUrl;
    csvData["amazonDataStatus"] = amazonData.status;
    return amazonData;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in processing Amazon Data:", error);
    console.error("Error in processing Amazon Data:", error);
  }
}

module.exports = processAmazonData;
