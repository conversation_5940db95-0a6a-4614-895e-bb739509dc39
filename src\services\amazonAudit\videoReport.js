function getVideoReport(data, report) {
  if (!data.productData || !data.productData[0].images) {
    return;
  }
  // console.log("Images: ", data.productData[0].images);
  const imagesCount = data.productData[0].images.noOfImages;
  const videosCount = data.productData[0].images.noOfVideos;
  const prospectRevenue = Math.round(data.productData[0].price * data.productData[0].sales) || 0;

  // console.log("Videos: ", videosCount);

  // Check if video is not present
  if (videosCount === 0) {
    report.push({
      DATA_POINT: "Videos",
      PRIORITY: "Urgent",
      Logic: "Video not present",
      PAIN_POINT: "I don't see any product video on the listing",
      Improvements: [
        `You should instantly make a great product video showing benefits & addressing pain points. You can see up to a 9.7% lift in sales`+
          (prospectRevenue > 0
            ? `. Which is $${Math.ceil(
                prospectRevenue * 0.097
              )} per month just for this listing alone.`
            : "."),
        "Keep it below 40 seconds & you can start by making it in Canva to keep costs low.",
      ],
      Benefits: ["CVR ↑", "Algorithm boost"],
    });
  }

  // Check if more than 2 videos are present and images are less than 6
//   if (videosCount > 2 && imagesCount < 6) {
//     report.push({
//       DATA_POINT: "Videos",
//       PRIORITY: "High",
//       Logic:
//         "2+ Videos present and images < 6",
//       PAIN_POINT: `I see there are ${videosCount} videos on your listing, which can cause your CVR to go down... moving into lower profits`,
//       Improvements: [
//         "You should just have 1 main video on your listing.",
//         "We have seen lower conversions with multiple videos.",
//       ],
//       Benefits: ["CVR ↑"],
//     });
//   }
}

module.exports = getVideoReport;
