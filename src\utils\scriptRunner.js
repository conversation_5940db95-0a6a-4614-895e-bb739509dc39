const { exec } = require('child_process');
const path = require('path');
const scrapeAmazon = require('../services/scrapeAmazon');
const scrapeAboutData = require('../services/scrapeAboutData/process');
const cron = require('node-cron');

const prisma = require('../database/prisma/getPrismaClient');

async function scrapeData(company, clientId) {
    // if (company.website) {
    //     await scrapeAboutData({
    //         company_url: company.website,
    //         company_id: company.id
    //     });
    // }

    await scrapeAmazon({
        company_name: company.name,
        company_id: company.id,
        clientId: clientId

    });
}

module.exports = { scrapeData };
