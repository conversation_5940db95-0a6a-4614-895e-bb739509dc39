/**
 * Test script to verify <PERSON><PERSON><PERSON> tracing for prompt chains
 */

const { runLexAnalysisChain } = require("./src/services/spike/runPromptChain");

// Sample review data for testing
const testReview = {
  productTitle: "Spartan Mosquito Pro Tech - 1 Acre Pack 4 Tubes (2 Boxes) 100% American Made",
  "Review Title": "False advertisement. The owner puts videos online. Lies.",
  reviewContent: "EDIT: ABSOLUTE SCAM NO WONDER THEY WERE BEING SUED LEFT RIGHT UP AND DOWN BY THEIR INVESTORS! Yesterday I swatted one mosquito while I was setting these traps up. TODAY HOWEVER, I cannot even stand outside on my property. This drew THE ENTIRE NEIGHBORHOOD OF MOSQUITOS ALL INTO MY YARD.",
  ASIN: "B09B1BP1JH",
  "Review ID": "R1I2I0WG0EKGOW"
};

async function testPromptChainTracing() {
  try {
    console.log("🧪 Testing prompt chain tracing in LangSmith...");
    console.log("📝 This should appear as 'prompt_chain_1' in Lang<PERSON>mith with nested steps");
    
    const result = await runLexAnalysisChain(testReview, {
      debug: true,
      temperature: 0.7
    });
    
    console.log("\n✅ Test completed successfully!");
    console.log("📊 Results summary:");
    console.log(`- Analysis: ${result.analysis ? 'Generated' : 'Failed'}`);
    console.log(`- Top Violations: ${result.topViolations.guidelineViolation1 || 'None detected'}`);
    console.log(`- Violation Analysis: ${result.violation ? 'Generated' : 'Skipped'}`);
    console.log(`- Total Cost: $${(result.cost || 0).toFixed(4)}`);
    console.log(`- Total Tokens: ${result.tokenUsage.totalTokens}`);
    
    console.log("\n🔍 Check LangSmith dashboard for trace labeled 'prompt_chain_1'");
    
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error(error.stack);
  }
}

// Run the test
testPromptChainTracing();
