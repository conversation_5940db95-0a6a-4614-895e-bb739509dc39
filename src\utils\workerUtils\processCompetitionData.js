const prisma = require("../../database/prisma/getPrismaClient");
const { getOpenApiKey } = require("../../models/configuration");
const getCompetition = require("../../services/competitionData/getCompetition");
const { completionFactory } = require("../../services/scrapeGPT/factory");
const calculatePricing = require("../calculatePrice");
const Sentry = require("@sentry/node");

async function processCompetitionData(
  csvData,
  amazonData,
  clientId
  // aboutData
) {
  try {
    console.log(
      "Processing competition data for company:-----------------",
      csvData.companyName
    );
    let compData = await prisma.competitionData.findFirst({
      where: {
        companyId: csvData.companyId,
      },
    });
    const isCompDataOutdated =
      compData && compData.updatedAt < amazonData.updatedAt;

    if (
      !compData ||
      compData.prospectRevenue == -1 ||
      !compData.competitorProductName ||
      !compData.competitorName ||
      isCompDataOutdated
    ) {
      let usage = [
        { promptTokens: 0, completionTokens: 0, totalTokens: 0, totalRuns: 0 },
      ];
      compData = await getCompetition({
        companyId: csvData.companyId,
        competitorUrl: csvData.competitorDetails.productAmazonURL,
        amazonData: amazonData,
        aboutData: null,
        company_name: csvData.companyName,
        usage: usage,
        clientId: clientId,
      });
      // console.log("Competition Data:", compData, usage[0]);
      csvData["promptTokens"] += usage[0]["promptTokens"];
      csvData["completionTokens"] += usage[0]["completionTokens"];
    }

    if (compData) {
      console.log(
        "Competition data found for company:-----------------",
        csvData.companyName
      );
      // console.log("COMP DATA IN Fn:", compData);
      let humanizedCompCompanyNameResponse;
      let humanizedCompProductTitleResponse;
      let humanizedProspectProductTitleResponse;
      let humanizedProspectNameResponse;
      // let totalPromptTokens = 0;
      // let totalCompletionTokens = 0;
      if (compData.competitorName) {
          humanizedCompCompanyNameResponse = await completionFactory(
            "companyNameHumanisation",
            compData.competitorName,
            clientId
          );
        csvData["promptTokens"] +=
          humanizedCompCompanyNameResponse?.prompt_tokens || 0;
        csvData["completionTokens"] +=
          humanizedCompCompanyNameResponse?.completion_tokens || 0;
        csvData["competitorDetails"]["humanizedCompCompanyName"] =
          humanizedCompCompanyNameResponse?.message || compData.competitorName;
      }
      if (compData.competitorProductName) {
         humanizedCompProductTitleResponse = await completionFactory(
           "productTitleHumanisation",
           compData.competitorProductName,
           clientId
         );
        csvData["promptTokens"] +=
          humanizedCompProductTitleResponse?.prompt_tokens || 0;
        csvData["completionTokens"] +=
          humanizedCompProductTitleResponse?.completion_tokens || 0;
        csvData["competitorDetails"]["humanizedCompProductName"] =
          humanizedCompProductTitleResponse?.message || compData.competitorProductName;
      }
      if (csvData["companyName"]) {
       humanizedProspectNameResponse = await completionFactory(
         "companyNameHumanisation",
         csvData["companyName"],
         clientId
       );
        csvData["promptTokens"] += humanizedProspectNameResponse?.prompt_tokens || 0;
        csvData["completionTokens"] +=
          humanizedProspectNameResponse?.completion_tokens || 0;
      }
      if (compData.prospectProductName) {
         humanizedProspectProductTitleResponse = await completionFactory(
           "productTitleHumanisation",
           compData.prospectProductName,
           clientId
         );
        csvData["promptTokens"] +=
          humanizedProspectProductTitleResponse?.prompt_tokens;
        csvData["completionTokens"] +=
          humanizedProspectProductTitleResponse?.completion_tokens;
      }
      const { modelId } = await getOpenApiKey(clientId);
      csvData["gptDetails"] = modelId;
      const { inputPricing, outputPricing } = calculatePricing(
        csvData.promptTokens,
        csvData.completionTokens,
        modelId
      );
      csvData["inputPrice"] += inputPricing;
      csvData["outputPrice"] += outputPricing;
      csvData["competitorDetails"] = {
        asin: JSON.parse(compData.data).asin,
        status: compData.status,
        name: compData.competitorName,
        revenue: compData.competitorRevenue,
        productAmazonURL: compData.competitorProductAmazonURL,
        productName: compData.competitorProductName,
        humanizedCompCompanyName:
          humanizedCompCompanyNameResponse?.message || "",
        humanizedCompProductTitle:
          humanizedCompProductTitleResponse?.message || "",
      };
      csvData["compKeyPrompt"] = compData.compKeyPrompt;
      csvData["searchKeyword"] = compData.searchKeyword;
      csvData["prospectDetails"] = {
        asin:
          JSON.parse(compData.data).prospectAsin ||
          amazonData.data.productData[0].asin ||
          "",
        revenue: compData.prospectRevenue,
        revenueSource: compData.prospectRevenueSource,
        productName: compData.prospectProductName,
        productAmazonURL: compData.prospectProductAmazonURL,
        humanizedProspectName: humanizedProspectNameResponse.message,
        humanizedProspectProductTitle:
          humanizedProspectProductTitleResponse?.message,
      };
      csvData["revenueDifference"] =
        compData.competitorRevenue - compData.prospectRevenue;
      console.log(
        "Competition Data Updated for company:-----------------",
        csvData.companyName
      );

      await prisma.outputData.update({
        where: {
          id: csvData.id,
        },
        data: csvData,
      });
    } else {
      console.log(
        "No competition data found for company:-----------------",
        csvData.companyName
      );
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in processing Competition Data:", error);
    console.error("Error in processing Competition Data:", error);
  }
}

module.exports = processCompetitionData;
