const fs = require("fs");
const path = require("path");
const prisma = require("../database/prisma/getPrismaClient");

async function reloadServer() {
  try {
    console.log("RELOADING SERVER...");
    const cacheDir = path.resolve(
      __dirname,
      "../utils/puppeteer/puppeteer-cache"
    );
    // console.log({ cacheDir });
    // Delete the custom cache directory if specified
    if (cacheDir && fs.existsSync(cacheDir)) {
      fs.rmSync(cacheDir, { recursive: true, force: true });
      console.log("Custom cache directory deleted...");
    }
    await prisma.$disconnect();
    console.log("Prisma Disconnected...");
    console.log("====================================");
    const metrics = await prisma.$metrics.json();
    console.log("PRISMA METRICS:", metrics.counters);
    console.log("====================================");
    process.exit(0);
  } catch (error) {
    console.log("ERROR RELOADING:", error);
    process.exit(0);
  }
}
module.exports = reloadServer;
